import{$ as l,$a as Po,$b as et,A as kt,Aa as Pt,Ab as P,B as Un,<PERSON> as he,Bb as ge,<PERSON> as So,Ca as Pi,Cb as Z,<PERSON> as <PERSON>e,<PERSON> as Js,Db as <PERSON>e,<PERSON> as <PERSON>,<PERSON>a as Oo,Eb as ue,F as dt,Fa as Wn,Fb as Q,G as ut,Ga as Ni,Gb as J,H as Ao,Ha as Li,Hb as hl,I as Us,Ia as ei,Ib as fl,J as Io,Ja as ti,Jb as jt,K as Hs,Ka as el,Kb as p,L as $s,La as tl,Lb as zt,M as Ro,Ma as il,Mb as Ut,N as Hn,Na as nl,Nb as pl,O as Ze,Oa as rl,Ob as fe,P as ye,Pa as ol,Pb as gl,Q as de,Qa as al,Qb as _l,R as Gs,Ra as Ie,Rb as Yn,S as oe,Sa as f,Sb as Bo,T as z,Ta as ii,Tb as vl,U as mt,Ua as Nt,Ub as bl,V as v,Va as pe,Vb as qe,W as T,Wa as Se,Wb as Vo,X as Ws,Xa as G,Xb as yl,Y as b,Ya as sl,Yb as q,Z as To,Za as Ke,Zb as pt,_ as F,_a as ll,_b as je,a as _,aa as Ft,ab as cl,ac as Zn,b as ee,ba as qs,bb as No,bc as Kn,ca as Ne,cb as U,d as ve,da as We,db as k,e as js,ea as Ce,eb as I,f as st,fa as Le,fb as dl,g as wo,ga as Be,gb as we,h as Do,ha as Fi,hb as y,i as E,ia as Ys,ib as Lo,j as xe,ja as Ee,jb as Bi,k as Ge,ka as se,kb as ul,l as be,la as $n,lb as Lt,m as D,ma as ko,mb as ne,n as Tt,na as Zs,nb as g,o as xo,oa as Gn,ob as Vi,p as zs,pa as Y,pb as $,q as M,qa as V,qb as Je,r as Ti,ra as ht,rb as ml,s as Oe,sa as Ks,sb as le,t as jn,ta as H,tb as u,u as ki,ua as Ot,ub as d,v as zn,va as ft,vb as O,w as lt,wa as Fo,wb as qn,x as ae,xa as Xs,xb as Bt,y as Eo,ya as Oi,yb as Vt,z as ct,za as Qs,zb as re}from"./chunk-RY3FH4ZG.js";var N=new b("");var Dl=null;function ze(){return Dl}function jo(n){Dl??=n}var ji=class{},zo=(()=>{class n{historyGo(e){throw new Error("")}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>l(xl),providedIn:"platform"})}return n})();var xl=(()=>{class n extends zo{_location;_history;_doc=l(N);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ze().getBaseHref(this._doc)}onPopState(e){let t=ze().getGlobalEventTarget(this._doc,"window");return t.addEventListener("popstate",e,!1),()=>t.removeEventListener("popstate",e)}onHashChange(e){let t=ze().getGlobalEventTarget(this._doc,"window");return t.addEventListener("hashchange",e,!1),()=>t.removeEventListener("hashchange",e)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(e){this._location.pathname=e}pushState(e,t,r){this._history.pushState(e,t,r)}replaceState(e,t,r){this._history.replaceState(e,t,r)}forward(){this._history.forward()}back(){this._history.back()}historyGo(e=0){this._history.go(e)}getState(){return this._history.state}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>new n,providedIn:"platform"})}return n})();function El(n,i){return n?i?n.endsWith("/")?i.startsWith("/")?n+i.slice(1):n+i:i.startsWith("/")?n+i:`${n}/${i}`:n:i}function Cl(n){let i=n.search(/#|\?|$/);return n[i-1]==="/"?n.slice(0,i-1)+n.slice(i):n}function gt(n){return n&&n[0]!=="?"?`?${n}`:n}var Xn=(()=>{class n{historyGo(e){throw new Error("")}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>l(Ml),providedIn:"root"})}return n})(),Sl=new b(""),Ml=(()=>{class n extends Xn{_platformLocation;_baseHref;_removeListenerFns=[];constructor(e,t){super(),this._platformLocation=e,this._baseHref=t??this._platformLocation.getBaseHrefFromDOM()??l(N).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(e){this._removeListenerFns.push(this._platformLocation.onPopState(e),this._platformLocation.onHashChange(e))}getBaseHref(){return this._baseHref}prepareExternalUrl(e){return El(this._baseHref,e)}path(e=!1){let t=this._platformLocation.pathname+gt(this._platformLocation.search),r=this._platformLocation.hash;return r&&e?`${t}${r}`:t}pushState(e,t,r,o){let a=this.prepareExternalUrl(r+gt(o));this._platformLocation.pushState(e,t,a)}replaceState(e,t,r,o){let a=this.prepareExternalUrl(r+gt(o));this._platformLocation.replaceState(e,t,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(e=0){this._platformLocation.historyGo?.(e)}static \u0275fac=function(t){return new(t||n)(F(zo),F(Sl,8))};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),_t=(()=>{class n{_subject=new E;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(e){this._locationStrategy=e;let t=this._locationStrategy.getBaseHref();this._basePath=hm(Cl(wl(t))),this._locationStrategy.onPopState(r=>{this._subject.next({url:this.path(!0),pop:!0,state:r.state,type:r.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(e=!1){return this.normalize(this._locationStrategy.path(e))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(e,t=""){return this.path()==this.normalize(e+gt(t))}normalize(e){return n.stripTrailingSlash(mm(this._basePath,wl(e)))}prepareExternalUrl(e){return e&&e[0]!=="/"&&(e="/"+e),this._locationStrategy.prepareExternalUrl(e)}go(e,t="",r=null){this._locationStrategy.pushState(r,"",e,t),this._notifyUrlChangeListeners(this.prepareExternalUrl(e+gt(t)),r)}replaceState(e,t="",r=null){this._locationStrategy.replaceState(r,"",e,t),this._notifyUrlChangeListeners(this.prepareExternalUrl(e+gt(t)),r)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(e=0){this._locationStrategy.historyGo?.(e)}onUrlChange(e){return this._urlChangeListeners.push(e),this._urlChangeSubscription??=this.subscribe(t=>{this._notifyUrlChangeListeners(t.url,t.state)}),()=>{let t=this._urlChangeListeners.indexOf(e);this._urlChangeListeners.splice(t,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(e="",t){this._urlChangeListeners.forEach(r=>r(e,t))}subscribe(e,t,r){return this._subject.subscribe({next:e,error:t??void 0,complete:r??void 0})}static normalizeQueryParams=gt;static joinWithSlash=El;static stripTrailingSlash=Cl;static \u0275fac=function(t){return new(t||n)(F(Xn))};static \u0275prov=v({token:n,factory:()=>um(),providedIn:"root"})}return n})();function um(){return new _t(F(Xn))}function mm(n,i){if(!n||!i.startsWith(n))return i;let e=i.substring(n.length);return e===""||["/",";","?","#"].includes(e[0])?e:i}function wl(n){return n.replace(/\/index.html$/,"")}function hm(n){if(new RegExp("^(https?:)?//").test(n)){let[,e]=n.split(/\/\/[^\/]+/);return e}return n}var Uo=/\s+/,Al=[],Ho=(()=>{class n{_ngEl;_renderer;initialClasses=Al;rawClass;stateMap=new Map;constructor(e,t){this._ngEl=e,this._renderer=t}set klass(e){this.initialClasses=e!=null?e.trim().split(Uo):Al}set ngClass(e){this.rawClass=typeof e=="string"?e.trim().split(Uo):e}ngDoCheck(){for(let t of this.initialClasses)this._updateState(t,!0);let e=this.rawClass;if(Array.isArray(e)||e instanceof Set)for(let t of e)this._updateState(t,!0);else if(e!=null)for(let t of Object.keys(e))this._updateState(t,!!e[t]);this._applyStateDiff()}_updateState(e,t){let r=this.stateMap.get(e);r!==void 0?(r.enabled!==t&&(r.changed=!0,r.enabled=t),r.touched=!0):this.stateMap.set(e,{enabled:t,changed:!0,touched:!0})}_applyStateDiff(){for(let e of this.stateMap){let t=e[0],r=e[1];r.changed?(this._toggleClass(t,r.enabled),r.changed=!1):r.touched||(r.enabled&&this._toggleClass(t,!1),this.stateMap.delete(t)),r.touched=!1}}_toggleClass(e,t){e=e.trim(),e.length>0&&e.split(Uo).forEach(r=>{t?this._renderer.addClass(this._ngEl.nativeElement,r):this._renderer.removeClass(this._ngEl.nativeElement,r)})}static \u0275fac=function(t){return new(t||n)(G(H),G(Se))};static \u0275dir=I({type:n,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return n})();var Qn=class{$implicit;ngForOf;index;count;constructor(i,e,t,r){this.$implicit=i,this.ngForOf=e,this.index=t,this.count=r}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},er=(()=>{class n{_viewContainer;_template;_differs;set ngForOf(e){this._ngForOf=e,this._ngForOfDirty=!0}set ngForTrackBy(e){this._trackByFn=e}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(e,t,r){this._viewContainer=e,this._template=t,this._differs=r}set ngForTemplate(e){e&&(this._template=e)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let e=this._ngForOf;!this._differ&&e&&(this._differ=this._differs.find(e).create(this.ngForTrackBy))}if(this._differ){let e=this._differ.diff(this._ngForOf);e&&this._applyChanges(e)}}_applyChanges(e){let t=this._viewContainer;e.forEachOperation((r,o,a)=>{if(r.previousIndex==null)t.createEmbeddedView(this._template,new Qn(r.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)t.remove(o===null?void 0:o);else if(o!==null){let s=t.get(o);t.move(s,a),Il(s,r)}});for(let r=0,o=t.length;r<o;r++){let s=t.get(r).context;s.index=r,s.count=o,s.ngForOf=this._ngForOf}e.forEachIdentityChange(r=>{let o=t.get(r.currentIndex);Il(o,r)})}static ngTemplateContextGuard(e,t){return!0}static \u0275fac=function(t){return new(t||n)(G(Ke),G(Nt),G(Vo))};static \u0275dir=I({type:n,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return n})();function Il(n,i){n.context.$implicit=i.item}var Ht=(()=>{class n{_viewContainer;_context=new Jn;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(e,t){this._viewContainer=e,this._thenTemplateRef=t}set ngIf(e){this._context.$implicit=this._context.ngIf=e,this._updateView()}set ngIfThen(e){Rl(e,!1),this._thenTemplateRef=e,this._thenViewRef=null,this._updateView()}set ngIfElse(e){Rl(e,!1),this._elseTemplateRef=e,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(e,t){return!0}static \u0275fac=function(t){return new(t||n)(G(Ke),G(Nt))};static \u0275dir=I({type:n,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return n})(),Jn=class{$implicit=null;ngIf=null};function Rl(n,i){if(n&&!n.createEmbeddedView)throw new z(2020,!1)}var zi=(()=>{class n{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(e){this._viewContainerRef=e}ngOnChanges(e){if(this._shouldRecreateView(e)){let t=this._viewContainerRef;if(this._viewRef&&t.remove(t.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let r=this._createContextForwardProxy();this._viewRef=t.createEmbeddedView(this.ngTemplateOutlet,r,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(e){return!!e.ngTemplateOutlet||!!e.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(e,t,r)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,t,r):!1,get:(e,t,r)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,t,r)}})}static \u0275fac=function(t){return new(t||n)(G(Ke))};static \u0275dir=I({type:n,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Ce]})}return n})();var $o=(()=>{class n{transform(e){return JSON.stringify(e,null,2)}static \u0275fac=function(t){return new(t||n)};static \u0275pipe=dl({name:"json",type:n,pure:!1})}return n})();var vt=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({})}return n})();function Ui(n,i){i=encodeURIComponent(i);for(let e of n.split(";")){let t=e.indexOf("="),[r,o]=t==-1?[e,""]:[e.slice(0,t),e.slice(t+1)];if(r.trim()===i)return decodeURIComponent(o)}return null}var tr="browser",Tl="server";function Go(n){return n===tr}function ir(n){return n===Tl}var $t=class{};var or=new b(""),Ko=(()=>{class n{_zone;_plugins;_eventNameToPlugin=new Map;constructor(e,t){this._zone=t,e.forEach(r=>{r.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,t,r,o){return this._findPluginFor(t).addEventListener(e,t,r,o)}getZone(){return this._zone}_findPluginFor(e){let t=this._eventNameToPlugin.get(e);if(t)return t;if(t=this._plugins.find(o=>o.supports(e)),!t)throw new z(5101,!1);return this._eventNameToPlugin.set(e,t),t}static \u0275fac=function(t){return new(t||n)(F(or),F(V))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),Hi=class{_doc;constructor(i){this._doc=i}manager},nr="ng-app-id";function kl(n){for(let i of n)i.remove()}function Fl(n,i){let e=i.createElement("style");return e.textContent=n,e}function pm(n,i,e,t){let r=n.head?.querySelectorAll(`style[${nr}="${i}"],link[${nr}="${i}"]`);if(r)for(let o of r)o.removeAttribute(nr),o instanceof HTMLLinkElement?t.set(o.href.slice(o.href.lastIndexOf("/")+1),{usage:0,elements:[o]}):o.textContent&&e.set(o.textContent,{usage:0,elements:[o]})}function Yo(n,i){let e=i.createElement("link");return e.setAttribute("rel","stylesheet"),e.setAttribute("href",n),e}var Xo=(()=>{class n{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(e,t,r,o={}){this.doc=e,this.appId=t,this.nonce=r,this.isServer=ir(o),pm(e,t,this.inline,this.external),this.hosts.add(e.head)}addStyles(e,t){for(let r of e)this.addUsage(r,this.inline,Fl);t?.forEach(r=>this.addUsage(r,this.external,Yo))}removeStyles(e,t){for(let r of e)this.removeUsage(r,this.inline);t?.forEach(r=>this.removeUsage(r,this.external))}addUsage(e,t,r){let o=t.get(e);o?o.usage++:t.set(e,{usage:1,elements:[...this.hosts].map(a=>this.addElement(a,r(e,this.doc)))})}removeUsage(e,t){let r=t.get(e);r&&(r.usage--,r.usage<=0&&(kl(r.elements),t.delete(e)))}ngOnDestroy(){for(let[,{elements:e}]of[...this.inline,...this.external])kl(e);this.hosts.clear()}addHost(e){this.hosts.add(e);for(let[t,{elements:r}]of this.inline)r.push(this.addElement(e,Fl(t,this.doc)));for(let[t,{elements:r}]of this.external)r.push(this.addElement(e,Yo(t,this.doc)))}removeHost(e){this.hosts.delete(e)}addElement(e,t){return this.nonce&&t.setAttribute("nonce",this.nonce),this.isServer&&t.setAttribute(nr,this.appId),e.appendChild(t)}static \u0275fac=function(t){return new(t||n)(F(N),F(Oi),F(Pi,8),F(Pt))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),qo={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Qo=/%COMP%/g;var Pl="%COMP%",gm=`_nghost-${Pl}`,_m=`_ngcontent-${Pl}`,vm=!0,bm=new b("",{providedIn:"root",factory:()=>vm});function ym(n){return _m.replace(Qo,n)}function Cm(n){return gm.replace(Qo,n)}function Nl(n,i){return i.map(e=>e.replace(Qo,n))}var Wi=(()=>{class n{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(e,t,r,o,a,s,c,m=null,h=null){this.eventManager=e,this.sharedStylesHost=t,this.appId=r,this.removeStylesOnCompDestroy=o,this.doc=a,this.platformId=s,this.ngZone=c,this.nonce=m,this.tracingService=h,this.platformIsServer=ir(s),this.defaultRenderer=new $i(e,a,c,this.platformIsServer,this.tracingService)}createRenderer(e,t){if(!e||!t)return this.defaultRenderer;this.platformIsServer&&t.encapsulation===Li.ShadowDom&&(t=ee(_({},t),{encapsulation:Li.Emulated}));let r=this.getOrCreateRenderer(e,t);return r instanceof rr?r.applyToHost(e):r instanceof Gi&&r.applyStyles(),r}getOrCreateRenderer(e,t){let r=this.rendererByCompId,o=r.get(t.id);if(!o){let a=this.doc,s=this.ngZone,c=this.eventManager,m=this.sharedStylesHost,h=this.removeStylesOnCompDestroy,C=this.platformIsServer,x=this.tracingService;switch(t.encapsulation){case Li.Emulated:o=new rr(c,m,t,this.appId,h,a,s,C,x);break;case Li.ShadowDom:return new Zo(c,m,e,t,a,s,this.nonce,C,x);default:o=new Gi(c,m,t,h,a,s,C,x);break}r.set(t.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(e){this.rendererByCompId.delete(e)}static \u0275fac=function(t){return new(t||n)(F(Ko),F(Xo),F(Oi),F(bm),F(N),F(Pt),F(V),F(Pi),F(Js,8))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),$i=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(i,e,t,r,o){this.eventManager=i,this.doc=e,this.ngZone=t,this.platformIsServer=r,this.tracingService=o}destroy(){}destroyNode=null;createElement(i,e){return e?this.doc.createElementNS(qo[e]||e,i):this.doc.createElement(i)}createComment(i){return this.doc.createComment(i)}createText(i){return this.doc.createTextNode(i)}appendChild(i,e){(Ol(i)?i.content:i).appendChild(e)}insertBefore(i,e,t){i&&(Ol(i)?i.content:i).insertBefore(e,t)}removeChild(i,e){e.remove()}selectRootElement(i,e){let t=typeof i=="string"?this.doc.querySelector(i):i;if(!t)throw new z(-5104,!1);return e||(t.textContent=""),t}parentNode(i){return i.parentNode}nextSibling(i){return i.nextSibling}setAttribute(i,e,t,r){if(r){e=r+":"+e;let o=qo[r];o?i.setAttributeNS(o,e,t):i.setAttribute(e,t)}else i.setAttribute(e,t)}removeAttribute(i,e,t){if(t){let r=qo[t];r?i.removeAttributeNS(r,e):i.removeAttribute(`${t}:${e}`)}else i.removeAttribute(e)}addClass(i,e){i.classList.add(e)}removeClass(i,e){i.classList.remove(e)}setStyle(i,e,t,r){r&(ii.DashCase|ii.Important)?i.style.setProperty(e,t,r&ii.Important?"important":""):i.style[e]=t}removeStyle(i,e,t){t&ii.DashCase?i.style.removeProperty(e):i.style[e]=""}setProperty(i,e,t){i!=null&&(i[e]=t)}setValue(i,e){i.nodeValue=e}listen(i,e,t,r){if(typeof i=="string"&&(i=ze().getGlobalEventTarget(this.doc,i),!i))throw new z(5102,!1);let o=this.decoratePreventDefault(t);return this.tracingService?.wrapEventListener&&(o=this.tracingService.wrapEventListener(i,e,o)),this.eventManager.addEventListener(i,e,o,r)}decoratePreventDefault(i){return e=>{if(e==="__ngUnwrap__")return i;(this.platformIsServer?this.ngZone.runGuarded(()=>i(e)):i(e))===!1&&e.preventDefault()}}};function Ol(n){return n.tagName==="TEMPLATE"&&n.content!==void 0}var Zo=class extends $i{sharedStylesHost;hostEl;shadowRoot;constructor(i,e,t,r,o,a,s,c,m){super(i,o,a,c,m),this.sharedStylesHost=e,this.hostEl=t,this.shadowRoot=t.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let h=r.styles;h=Nl(r.id,h);for(let x of h){let A=document.createElement("style");s&&A.setAttribute("nonce",s),A.textContent=x,this.shadowRoot.appendChild(A)}let C=r.getExternalStyles?.();if(C)for(let x of C){let A=Yo(x,o);s&&A.setAttribute("nonce",s),this.shadowRoot.appendChild(A)}}nodeOrShadowRoot(i){return i===this.hostEl?this.shadowRoot:i}appendChild(i,e){return super.appendChild(this.nodeOrShadowRoot(i),e)}insertBefore(i,e,t){return super.insertBefore(this.nodeOrShadowRoot(i),e,t)}removeChild(i,e){return super.removeChild(null,e)}parentNode(i){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(i)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Gi=class extends $i{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(i,e,t,r,o,a,s,c,m){super(i,o,a,s,c),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=r;let h=t.styles;this.styles=m?Nl(m,h):h,this.styleUrls=t.getExternalStyles?.(m)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},rr=class extends Gi{contentAttr;hostAttr;constructor(i,e,t,r,o,a,s,c,m){let h=r+"-"+t.id;super(i,e,t,o,a,s,c,m,h),this.contentAttr=ym(h),this.hostAttr=Cm(h)}applyToHost(i){this.applyStyles(),this.setAttribute(i,this.hostAttr,"")}createElement(i,e){let t=super.createElement(i,e);return super.setAttribute(t,this.contentAttr,""),t}};var ar=class n extends ji{supportsDOMEvents=!0;static makeCurrent(){jo(new n)}onAndCancel(i,e,t,r){return i.addEventListener(e,t,r),()=>{i.removeEventListener(e,t,r)}}dispatchEvent(i,e){i.dispatchEvent(e)}remove(i){i.remove()}createElement(i,e){return e=e||this.getDefaultDocument(),e.createElement(i)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(i){return i.nodeType===Node.ELEMENT_NODE}isShadowRoot(i){return i instanceof DocumentFragment}getGlobalEventTarget(i,e){return e==="window"?window:e==="document"?i:e==="body"?i.body:null}getBaseHref(i){let e=Dm();return e==null?null:xm(e)}resetBaseElement(){qi=null}getUserAgent(){return window.navigator.userAgent}getCookie(i){return Ui(document.cookie,i)}},qi=null;function Dm(){return qi=qi||document.head.querySelector("base"),qi?qi.getAttribute("href"):null}function xm(n){return new URL(n,document.baseURI).pathname}var Em=(()=>{class n{build(){return new XMLHttpRequest}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),Bl=(()=>{class n extends Hi{constructor(e){super(e)}supports(e){return!0}addEventListener(e,t,r,o){return e.addEventListener(t,r,o),()=>this.removeEventListener(e,t,r,o)}removeEventListener(e,t,r,o){return e.removeEventListener(t,r,o)}static \u0275fac=function(t){return new(t||n)(F(N))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),Ll=["alt","control","meta","shift"],Sm={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Mm={alt:n=>n.altKey,control:n=>n.ctrlKey,meta:n=>n.metaKey,shift:n=>n.shiftKey},Vl=(()=>{class n extends Hi{constructor(e){super(e)}supports(e){return n.parseEventName(e)!=null}addEventListener(e,t,r,o){let a=n.parseEventName(t),s=n.eventCallback(a.fullKey,r,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>ze().onAndCancel(e,a.domEventName,s,o))}static parseEventName(e){let t=e.toLowerCase().split("."),r=t.shift();if(t.length===0||!(r==="keydown"||r==="keyup"))return null;let o=n._normalizeKey(t.pop()),a="",s=t.indexOf("code");if(s>-1&&(t.splice(s,1),a="code."),Ll.forEach(m=>{let h=t.indexOf(m);h>-1&&(t.splice(h,1),a+=m+".")}),a+=o,t.length!=0||o.length===0)return null;let c={};return c.domEventName=r,c.fullKey=a,c}static matchEventFullKeyCode(e,t){let r=Sm[e.key]||e.key,o="";return t.indexOf("code.")>-1&&(r=e.code,o="code."),r==null||!r?!1:(r=r.toLowerCase(),r===" "?r="space":r==="."&&(r="dot"),Ll.forEach(a=>{if(a!==r){let s=Mm[a];s(e)&&(o+=a+".")}}),o+=r,o===t)}static eventCallback(e,t,r){return o=>{n.matchEventFullKeyCode(o,e)&&r.runGuarded(()=>t(o))}}static _normalizeKey(e){return e==="esc"?"escape":e}static \u0275fac=function(t){return new(t||n)(F(N))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})();function Jo(n,i){return yl(_({rootComponent:n},Am(i)))}function Am(n){return{appProviders:[...Fm,...n?.providers??[]],platformProviders:km}}function Im(){ar.makeCurrent()}function Rm(){return new ht}function Tm(){return Xs(document),document}var km=[{provide:Pt,useValue:tr},{provide:Qs,useValue:Im,multi:!0},{provide:N,useFactory:Tm}];var Fm=[{provide:qs,useValue:"root"},{provide:ht,useFactory:Rm},{provide:or,useClass:Bl,multi:!0,deps:[N]},{provide:or,useClass:Vl,multi:!0,deps:[N]},Wi,Xo,Ko,{provide:pe,useExisting:Wi},{provide:$t,useClass:Em},[]];var ri=class{},Yi=class{},bt=class n{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(i){i?typeof i=="string"?this.lazyInit=()=>{this.headers=new Map,i.split(`
`).forEach(e=>{let t=e.indexOf(":");if(t>0){let r=e.slice(0,t),o=e.slice(t+1).trim();this.addHeaderEntry(r,o)}})}:typeof Headers<"u"&&i instanceof Headers?(this.headers=new Map,i.forEach((e,t)=>{this.addHeaderEntry(t,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(i).forEach(([e,t])=>{this.setHeaderEntries(e,t)})}:this.headers=new Map}has(i){return this.init(),this.headers.has(i.toLowerCase())}get(i){this.init();let e=this.headers.get(i.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(i){return this.init(),this.headers.get(i.toLowerCase())||null}append(i,e){return this.clone({name:i,value:e,op:"a"})}set(i,e){return this.clone({name:i,value:e,op:"s"})}delete(i,e){return this.clone({name:i,value:e,op:"d"})}maybeSetNormalizedName(i,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,i)}init(){this.lazyInit&&(this.lazyInit instanceof n?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(i=>this.applyUpdate(i)),this.lazyUpdate=null))}copyFrom(i){i.init(),Array.from(i.headers.keys()).forEach(e=>{this.headers.set(e,i.headers.get(e)),this.normalizedNames.set(e,i.normalizedNames.get(e))})}clone(i){let e=new n;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof n?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([i]),e}applyUpdate(i){let e=i.name.toLowerCase();switch(i.op){case"a":case"s":let t=i.value;if(typeof t=="string"&&(t=[t]),t.length===0)return;this.maybeSetNormalizedName(i.name,e);let r=(i.op==="a"?this.headers.get(e):void 0)||[];r.push(...t),this.headers.set(e,r);break;case"d":let o=i.value;if(!o)this.headers.delete(e),this.normalizedNames.delete(e);else{let a=this.headers.get(e);if(!a)return;a=a.filter(s=>o.indexOf(s)===-1),a.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,a)}break}}addHeaderEntry(i,e){let t=i.toLowerCase();this.maybeSetNormalizedName(i,t),this.headers.has(t)?this.headers.get(t).push(e):this.headers.set(t,[e])}setHeaderEntries(i,e){let t=(Array.isArray(e)?e:[e]).map(o=>o.toString()),r=i.toLowerCase();this.headers.set(r,t),this.maybeSetNormalizedName(i,r)}forEach(i){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>i(this.normalizedNames.get(e),this.headers.get(e)))}};var lr=class{encodeKey(i){return jl(i)}encodeValue(i){return jl(i)}decodeKey(i){return decodeURIComponent(i)}decodeValue(i){return decodeURIComponent(i)}};function Om(n,i){let e=new Map;return n.length>0&&n.replace(/^\?/,"").split("&").forEach(r=>{let o=r.indexOf("="),[a,s]=o==-1?[i.decodeKey(r),""]:[i.decodeKey(r.slice(0,o)),i.decodeValue(r.slice(o+1))],c=e.get(a)||[];c.push(s),e.set(a,c)}),e}var Pm=/%(\d[a-f0-9])/gi,Nm={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function jl(n){return encodeURIComponent(n).replace(Pm,(i,e)=>Nm[e]??i)}function sr(n){return`${n}`}var tt=class n{map;encoder;updates=null;cloneFrom=null;constructor(i={}){if(this.encoder=i.encoder||new lr,i.fromString){if(i.fromObject)throw new z(2805,!1);this.map=Om(i.fromString,this.encoder)}else i.fromObject?(this.map=new Map,Object.keys(i.fromObject).forEach(e=>{let t=i.fromObject[e],r=Array.isArray(t)?t.map(sr):[sr(t)];this.map.set(e,r)})):this.map=null}has(i){return this.init(),this.map.has(i)}get(i){this.init();let e=this.map.get(i);return e?e[0]:null}getAll(i){return this.init(),this.map.get(i)||null}keys(){return this.init(),Array.from(this.map.keys())}append(i,e){return this.clone({param:i,value:e,op:"a"})}appendAll(i){let e=[];return Object.keys(i).forEach(t=>{let r=i[t];Array.isArray(r)?r.forEach(o=>{e.push({param:t,value:o,op:"a"})}):e.push({param:t,value:r,op:"a"})}),this.clone(e)}set(i,e){return this.clone({param:i,value:e,op:"s"})}delete(i,e){return this.clone({param:i,value:e,op:"d"})}toString(){return this.init(),this.keys().map(i=>{let e=this.encoder.encodeKey(i);return this.map.get(i).map(t=>e+"="+this.encoder.encodeValue(t)).join("&")}).filter(i=>i!=="").join("&")}clone(i){let e=new n({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(i),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(i=>this.map.set(i,this.cloneFrom.map.get(i))),this.updates.forEach(i=>{switch(i.op){case"a":case"s":let e=(i.op==="a"?this.map.get(i.param):void 0)||[];e.push(sr(i.value)),this.map.set(i.param,e);break;case"d":if(i.value!==void 0){let t=this.map.get(i.param)||[],r=t.indexOf(sr(i.value));r!==-1&&t.splice(r,1),t.length>0?this.map.set(i.param,t):this.map.delete(i.param)}else{this.map.delete(i.param);break}}}),this.cloneFrom=this.updates=null)}};var cr=class{map=new Map;set(i,e){return this.map.set(i,e),this}get(i){return this.map.has(i)||this.map.set(i,i.defaultValue()),this.map.get(i)}delete(i){return this.map.delete(i),this}has(i){return this.map.has(i)}keys(){return this.map.keys()}};function Lm(n){switch(n){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function zl(n){return typeof ArrayBuffer<"u"&&n instanceof ArrayBuffer}function Ul(n){return typeof Blob<"u"&&n instanceof Blob}function Hl(n){return typeof FormData<"u"&&n instanceof FormData}function Bm(n){return typeof URLSearchParams<"u"&&n instanceof URLSearchParams}var $l="Content-Type",Gl="Accept",Wl="X-Request-URL",ql="text/plain",Yl="application/json",Vm=`${Yl}, ${ql}, */*`,ni=class n{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(i,e,t,r){this.url=e,this.method=i.toUpperCase();let o;if(Lm(this.method)||r?(this.body=t!==void 0?t:null,o=r):o=t,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new bt,this.context??=new cr,!this.params)this.params=new tt,this.urlWithParams=e;else{let a=this.params.toString();if(a.length===0)this.urlWithParams=e;else{let s=e.indexOf("?"),c=s===-1?"?":s<e.length-1?"&":"";this.urlWithParams=e+c+a}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||zl(this.body)||Ul(this.body)||Hl(this.body)||Bm(this.body)?this.body:this.body instanceof tt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Hl(this.body)?null:Ul(this.body)?this.body.type||null:zl(this.body)?null:typeof this.body=="string"?ql:this.body instanceof tt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Yl:null}clone(i={}){let e=i.method||this.method,t=i.url||this.url,r=i.responseType||this.responseType,o=i.transferCache??this.transferCache,a=i.body!==void 0?i.body:this.body,s=i.withCredentials??this.withCredentials,c=i.reportProgress??this.reportProgress,m=i.headers||this.headers,h=i.params||this.params,C=i.context??this.context;return i.setHeaders!==void 0&&(m=Object.keys(i.setHeaders).reduce((x,A)=>x.set(A,i.setHeaders[A]),m)),i.setParams&&(h=Object.keys(i.setParams).reduce((x,A)=>x.set(A,i.setParams[A]),h)),new n(e,t,a,{params:h,headers:m,context:C,reportProgress:c,responseType:r,withCredentials:s,transferCache:o})}},Gt=function(n){return n[n.Sent=0]="Sent",n[n.UploadProgress=1]="UploadProgress",n[n.ResponseHeader=2]="ResponseHeader",n[n.DownloadProgress=3]="DownloadProgress",n[n.Response=4]="Response",n[n.User=5]="User",n}(Gt||{}),oi=class{headers;status;statusText;url;ok;type;constructor(i,e=200,t="OK"){this.headers=i.headers||new bt,this.status=i.status!==void 0?i.status:e,this.statusText=i.statusText||t,this.url=i.url||null,this.ok=this.status>=200&&this.status<300}},dr=class n extends oi{constructor(i={}){super(i)}type=Gt.ResponseHeader;clone(i={}){return new n({headers:i.headers||this.headers,status:i.status!==void 0?i.status:this.status,statusText:i.statusText||this.statusText,url:i.url||this.url||void 0})}},Zi=class n extends oi{body;constructor(i={}){super(i),this.body=i.body!==void 0?i.body:null}type=Gt.Response;clone(i={}){return new n({body:i.body!==void 0?i.body:this.body,headers:i.headers||this.headers,status:i.status!==void 0?i.status:this.status,statusText:i.statusText||this.statusText,url:i.url||this.url||void 0})}},Ki=class extends oi{name="HttpErrorResponse";message;error;ok=!1;constructor(i){super(i,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${i.url||"(unknown url)"}`:this.message=`Http failure response for ${i.url||"(unknown url)"}: ${i.status} ${i.statusText}`,this.error=i.error||null}},jm=200,zm=204;function ea(n,i){return{body:i,headers:n.headers,context:n.context,observe:n.observe,params:n.params,reportProgress:n.reportProgress,responseType:n.responseType,withCredentials:n.withCredentials,transferCache:n.transferCache}}var yt=(()=>{class n{handler;constructor(e){this.handler=e}request(e,t,r={}){let o;if(e instanceof ni)o=e;else{let c;r.headers instanceof bt?c=r.headers:c=new bt(r.headers);let m;r.params&&(r.params instanceof tt?m=r.params:m=new tt({fromObject:r.params})),o=new ni(e,t,r.body!==void 0?r.body:null,{headers:c,context:r.context,params:m,reportProgress:r.reportProgress,responseType:r.responseType||"json",withCredentials:r.withCredentials,transferCache:r.transferCache})}let a=D(o).pipe(kt(c=>this.handler.handle(c)));if(e instanceof ni||r.observe==="events")return a;let s=a.pipe(ae(c=>c instanceof Zi));switch(r.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return s.pipe(M(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new z(2806,!1);return c.body}));case"blob":return s.pipe(M(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new z(2807,!1);return c.body}));case"text":return s.pipe(M(c=>{if(c.body!==null&&typeof c.body!="string")throw new z(2808,!1);return c.body}));case"json":default:return s.pipe(M(c=>c.body))}case"response":return s;default:throw new z(2809,!1)}}delete(e,t={}){return this.request("DELETE",e,t)}get(e,t={}){return this.request("GET",e,t)}head(e,t={}){return this.request("HEAD",e,t)}jsonp(e,t){return this.request("JSONP",e,{params:new tt().append(t,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,t={}){return this.request("OPTIONS",e,t)}patch(e,t,r={}){return this.request("PATCH",e,ea(r,t))}post(e,t,r={}){return this.request("POST",e,ea(r,t))}put(e,t,r={}){return this.request("PUT",e,ea(r,t))}static \u0275fac=function(t){return new(t||n)(F(ri))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})();var Um=new b("");function Hm(n,i){return i(n)}function $m(n,i,e){return(t,r)=>We(e,()=>i(t,o=>n(o,r)))}var Zl=new b(""),Kl=new b(""),Xl=new b("",{providedIn:"root",factory:()=>!0});var ur=(()=>{class n extends ri{backend;injector;chain=null;pendingTasks=l(Gn);contributeToStability=l(Xl);constructor(e,t){super(),this.backend=e,this.injector=t}handle(e){if(this.chain===null){let t=Array.from(new Set([...this.injector.get(Zl),...this.injector.get(Kl,[])]));this.chain=t.reduceRight((r,o)=>$m(r,o,this.injector),Hm)}if(this.contributeToStability){let t=this.pendingTasks.add();return this.chain(e,r=>this.backend.handle(r)).pipe(dt(()=>this.pendingTasks.remove(t)))}else return this.chain(e,t=>this.backend.handle(t))}static \u0275fac=function(t){return new(t||n)(F(Yi),F(Ne))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})();var Gm=/^\)\]\}',?\n/,Wm=RegExp(`^${Wl}:`,"m");function qm(n){return"responseURL"in n&&n.responseURL?n.responseURL:Wm.test(n.getAllResponseHeaders())?n.getResponseHeader(Wl):null}var ta=(()=>{class n{xhrFactory;constructor(e){this.xhrFactory=e}handle(e){if(e.method==="JSONP")throw new z(-2800,!1);let t=this.xhrFactory;return(t.\u0275loadImpl?be(t.\u0275loadImpl()):D(null)).pipe(ye(()=>new st(o=>{let a=t.build();if(a.open(e.method,e.urlWithParams),e.withCredentials&&(a.withCredentials=!0),e.headers.forEach((w,B)=>a.setRequestHeader(w,B.join(","))),e.headers.has(Gl)||a.setRequestHeader(Gl,Vm),!e.headers.has($l)){let w=e.detectContentTypeHeader();w!==null&&a.setRequestHeader($l,w)}if(e.responseType){let w=e.responseType.toLowerCase();a.responseType=w!=="json"?w:"text"}let s=e.serializeBody(),c=null,m=()=>{if(c!==null)return c;let w=a.statusText||"OK",B=new bt(a.getAllResponseHeaders()),me=qm(a)||e.url;return c=new dr({headers:B,status:a.status,statusText:w,url:me}),c},h=()=>{let{headers:w,status:B,statusText:me,url:Ae}=m(),te=null;B!==zm&&(te=typeof a.response>"u"?a.responseText:a.response),B===0&&(B=te?jm:0);let S=B>=200&&B<300;if(e.responseType==="json"&&typeof te=="string"){let cm=te;te=te.replace(Gm,"");try{te=te!==""?JSON.parse(te):null}catch(dm){te=cm,S&&(S=!1,te={error:dm,text:te})}}S?(o.next(new Zi({body:te,headers:w,status:B,statusText:me,url:Ae||void 0})),o.complete()):o.error(new Ki({error:te,headers:w,status:B,statusText:me,url:Ae||void 0}))},C=w=>{let{url:B}=m(),me=new Ki({error:w,status:a.status||0,statusText:a.statusText||"Unknown Error",url:B||void 0});o.error(me)},x=!1,A=w=>{x||(o.next(m()),x=!0);let B={type:Gt.DownloadProgress,loaded:w.loaded};w.lengthComputable&&(B.total=w.total),e.responseType==="text"&&a.responseText&&(B.partialText=a.responseText),o.next(B)},L=w=>{let B={type:Gt.UploadProgress,loaded:w.loaded};w.lengthComputable&&(B.total=w.total),o.next(B)};return a.addEventListener("load",h),a.addEventListener("error",C),a.addEventListener("timeout",C),a.addEventListener("abort",C),e.reportProgress&&(a.addEventListener("progress",A),s!==null&&a.upload&&a.upload.addEventListener("progress",L)),a.send(s),o.next({type:Gt.Sent}),()=>{a.removeEventListener("error",C),a.removeEventListener("abort",C),a.removeEventListener("load",h),a.removeEventListener("timeout",C),e.reportProgress&&(a.removeEventListener("progress",A),s!==null&&a.upload&&a.upload.removeEventListener("progress",L)),a.readyState!==a.DONE&&a.abort()}})))}static \u0275fac=function(t){return new(t||n)(F($t))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),Ql=new b(""),Ym="XSRF-TOKEN",Zm=new b("",{providedIn:"root",factory:()=>Ym}),Km="X-XSRF-TOKEN",Xm=new b("",{providedIn:"root",factory:()=>Km}),Xi=class{},Qm=(()=>{class n{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(e,t){this.doc=e,this.cookieName=t}getToken(){let e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=Ui(e,this.cookieName),this.lastCookieString=e),this.lastToken}static \u0275fac=function(t){return new(t||n)(F(N),F(Zm))};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})();function Jm(n,i){let e=n.url.toLowerCase();if(!l(Ql)||n.method==="GET"||n.method==="HEAD"||e.startsWith("http://")||e.startsWith("https://"))return i(n);let t=l(Xi).getToken(),r=l(Xm);return t!=null&&!n.headers.has(r)&&(n=n.clone({headers:n.headers.set(r,t)})),i(n)}function ia(...n){let i=[yt,ta,ur,{provide:ri,useExisting:ur},{provide:Yi,useFactory:()=>l(Um,{optional:!0})??l(ta)},{provide:Zl,useValue:Jm,multi:!0},{provide:Ql,useValue:!0},{provide:Xi,useClass:Qm}];for(let e of n)i.push(...e.\u0275providers);return Ft(i)}var ec=(()=>{class n{_doc;constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static \u0275fac=function(t){return new(t||n)(F(N))};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var ra=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:function(t){let r=null;return t?r=new(t||n):r=F(ih),r},providedIn:"root"})}return n})(),ih=(()=>{class n extends ra{_doc;constructor(e){super(),this._doc=e}sanitize(e,t){if(t==null)return null;switch(e){case Ie.NONE:return t;case Ie.HTML:return ti(t,"HTML")?ei(t):al(this._doc,String(t)).toString();case Ie.STYLE:return ti(t,"Style")?ei(t):t;case Ie.SCRIPT:if(ti(t,"Script"))return ei(t);throw new z(5200,!1);case Ie.URL:return ti(t,"URL")?ei(t):ol(String(t));case Ie.RESOURCE_URL:if(ti(t,"ResourceURL"))return ei(t);throw new z(5201,!1);default:throw new z(5202,!1)}}bypassSecurityTrustHtml(e){return el(e)}bypassSecurityTrustStyle(e){return tl(e)}bypassSecurityTrustScript(e){return il(e)}bypassSecurityTrustUrl(e){return nl(e)}bypassSecurityTrustResourceUrl(e){return rl(e)}static \u0275fac=function(t){return new(t||n)(F(N))};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var j="primary",dn=Symbol("RouteTitle"),ca=class{params;constructor(i){this.params=i||{}}has(i){return Object.prototype.hasOwnProperty.call(this.params,i)}get(i){if(this.has(i)){let e=this.params[i];return Array.isArray(e)?e[0]:e}return null}getAll(i){if(this.has(i)){let e=this.params[i];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function ui(n){return new ca(n)}function nh(n,i,e){let t=e.path.split("/");if(t.length>n.length||e.pathMatch==="full"&&(i.hasChildren()||t.length<n.length))return null;let r={};for(let o=0;o<t.length;o++){let a=t[o],s=n[o];if(a[0]===":")r[a.substring(1)]=s;else if(a!==s.path)return null}return{consumed:n.slice(0,t.length),posParams:r}}function rh(n,i){if(n.length!==i.length)return!1;for(let e=0;e<n.length;++e)if(!Xe(n[e],i[e]))return!1;return!0}function Xe(n,i){let e=n?da(n):void 0,t=i?da(i):void 0;if(!e||!t||e.length!=t.length)return!1;let r;for(let o=0;o<e.length;o++)if(r=e[o],!cc(n[r],i[r]))return!1;return!0}function da(n){return[...Object.keys(n),...Object.getOwnPropertySymbols(n)]}function cc(n,i){if(Array.isArray(n)&&Array.isArray(i)){if(n.length!==i.length)return!1;let e=[...n].sort(),t=[...i].sort();return e.every((r,o)=>t[o]===r)}else return n===i}function dc(n){return n.length>0?n[n.length-1]:null}function Dt(n){return xo(n)?n:Bi(n)?be(Promise.resolve(n)):D(n)}var oh={exact:mc,subset:hc},uc={exact:ah,subset:sh,ignored:()=>!0};function ic(n,i,e){return oh[e.paths](n.root,i.root,e.matrixParams)&&uc[e.queryParams](n.queryParams,i.queryParams)&&!(e.fragment==="exact"&&n.fragment!==i.fragment)}function ah(n,i){return Xe(n,i)}function mc(n,i,e){if(!qt(n.segments,i.segments)||!fr(n.segments,i.segments,e)||n.numberOfChildren!==i.numberOfChildren)return!1;for(let t in i.children)if(!n.children[t]||!mc(n.children[t],i.children[t],e))return!1;return!0}function sh(n,i){return Object.keys(i).length<=Object.keys(n).length&&Object.keys(i).every(e=>cc(n[e],i[e]))}function hc(n,i,e){return fc(n,i,i.segments,e)}function fc(n,i,e,t){if(n.segments.length>e.length){let r=n.segments.slice(0,e.length);return!(!qt(r,e)||i.hasChildren()||!fr(r,e,t))}else if(n.segments.length===e.length){if(!qt(n.segments,e)||!fr(n.segments,e,t))return!1;for(let r in i.children)if(!n.children[r]||!hc(n.children[r],i.children[r],t))return!1;return!0}else{let r=e.slice(0,n.segments.length),o=e.slice(n.segments.length);return!qt(n.segments,r)||!fr(n.segments,r,t)||!n.children[j]?!1:fc(n.children[j],i,o,t)}}function fr(n,i,e){return i.every((t,r)=>uc[e](n[r].parameters,t.parameters))}var nt=class{root;queryParams;fragment;_queryParamMap;constructor(i=new K([],{}),e={},t=null){this.root=i,this.queryParams=e,this.fragment=t}get queryParamMap(){return this._queryParamMap??=ui(this.queryParams),this._queryParamMap}toString(){return dh.serialize(this)}},K=class{segments;children;parent=null;constructor(i,e){this.segments=i,this.children=e,Object.values(e).forEach(t=>t.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return pr(this)}},Wt=class{path;parameters;_parameterMap;constructor(i,e){this.path=i,this.parameters=e}get parameterMap(){return this._parameterMap??=ui(this.parameters),this._parameterMap}toString(){return gc(this)}};function lh(n,i){return qt(n,i)&&n.every((e,t)=>Xe(e.parameters,i[t].parameters))}function qt(n,i){return n.length!==i.length?!1:n.every((e,t)=>e.path===i[t].path)}function ch(n,i){let e=[];return Object.entries(n.children).forEach(([t,r])=>{t===j&&(e=e.concat(i(r,t)))}),Object.entries(n.children).forEach(([t,r])=>{t!==j&&(e=e.concat(i(r,t)))}),e}var Ar=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>new mi,providedIn:"root"})}return n})(),mi=class{parse(i){let e=new ma(i);return new nt(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(i){let e=`/${Qi(i.root,!0)}`,t=hh(i.queryParams),r=typeof i.fragment=="string"?`#${uh(i.fragment)}`:"";return`${e}${t}${r}`}},dh=new mi;function pr(n){return n.segments.map(i=>gc(i)).join("/")}function Qi(n,i){if(!n.hasChildren())return pr(n);if(i){let e=n.children[j]?Qi(n.children[j],!1):"",t=[];return Object.entries(n.children).forEach(([r,o])=>{r!==j&&t.push(`${r}:${Qi(o,!1)}`)}),t.length>0?`${e}(${t.join("//")})`:e}else{let e=ch(n,(t,r)=>r===j?[Qi(n.children[j],!1)]:[`${r}:${Qi(t,!1)}`]);return Object.keys(n.children).length===1&&n.children[j]!=null?`${pr(n)}/${e[0]}`:`${pr(n)}/(${e.join("//")})`}}function pc(n){return encodeURIComponent(n).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function mr(n){return pc(n).replace(/%3B/gi,";")}function uh(n){return encodeURI(n)}function ua(n){return pc(n).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function gr(n){return decodeURIComponent(n)}function nc(n){return gr(n.replace(/\+/g,"%20"))}function gc(n){return`${ua(n.path)}${mh(n.parameters)}`}function mh(n){return Object.entries(n).map(([i,e])=>`;${ua(i)}=${ua(e)}`).join("")}function hh(n){let i=Object.entries(n).map(([e,t])=>Array.isArray(t)?t.map(r=>`${mr(e)}=${mr(r)}`).join("&"):`${mr(e)}=${mr(t)}`).filter(e=>e);return i.length?`?${i.join("&")}`:""}var fh=/^[^\/()?;#]+/;function oa(n){let i=n.match(fh);return i?i[0]:""}var ph=/^[^\/()?;=#]+/;function gh(n){let i=n.match(ph);return i?i[0]:""}var _h=/^[^=?&#]+/;function vh(n){let i=n.match(_h);return i?i[0]:""}var bh=/^[^&#]+/;function yh(n){let i=n.match(bh);return i?i[0]:""}var ma=class{url;remaining;constructor(i){this.url=i,this.remaining=i}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new K([],{}):new K([],this.parseChildren())}parseQueryParams(){let i={};if(this.consumeOptional("?"))do this.parseQueryParam(i);while(this.consumeOptional("&"));return i}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let i=[];for(this.peekStartsWith("(")||i.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),i.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let t={};return this.peekStartsWith("(")&&(t=this.parseParens(!1)),(i.length>0||Object.keys(e).length>0)&&(t[j]=new K(i,e)),t}parseSegment(){let i=oa(this.remaining);if(i===""&&this.peekStartsWith(";"))throw new z(4009,!1);return this.capture(i),new Wt(gr(i),this.parseMatrixParams())}parseMatrixParams(){let i={};for(;this.consumeOptional(";");)this.parseParam(i);return i}parseParam(i){let e=gh(this.remaining);if(!e)return;this.capture(e);let t="";if(this.consumeOptional("=")){let r=oa(this.remaining);r&&(t=r,this.capture(t))}i[gr(e)]=gr(t)}parseQueryParam(i){let e=vh(this.remaining);if(!e)return;this.capture(e);let t="";if(this.consumeOptional("=")){let a=yh(this.remaining);a&&(t=a,this.capture(t))}let r=nc(e),o=nc(t);if(i.hasOwnProperty(r)){let a=i[r];Array.isArray(a)||(a=[a],i[r]=a),a.push(o)}else i[r]=o}parseParens(i){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let t=oa(this.remaining),r=this.remaining[t.length];if(r!=="/"&&r!==")"&&r!==";")throw new z(4010,!1);let o;t.indexOf(":")>-1?(o=t.slice(0,t.indexOf(":")),this.capture(o),this.capture(":")):i&&(o=j);let a=this.parseChildren();e[o]=Object.keys(a).length===1?a[j]:new K([],a),this.consumeOptional("//")}return e}peekStartsWith(i){return this.remaining.startsWith(i)}consumeOptional(i){return this.peekStartsWith(i)?(this.remaining=this.remaining.substring(i.length),!0):!1}capture(i){if(!this.consumeOptional(i))throw new z(4011,!1)}};function _c(n){return n.segments.length>0?new K([],{[j]:n}):n}function vc(n){let i={};for(let[t,r]of Object.entries(n.children)){let o=vc(r);if(t===j&&o.segments.length===0&&o.hasChildren())for(let[a,s]of Object.entries(o.children))i[a]=s;else(o.segments.length>0||o.hasChildren())&&(i[t]=o)}let e=new K(n.segments,i);return Ch(e)}function Ch(n){if(n.numberOfChildren===1&&n.children[j]){let i=n.children[j];return new K(n.segments.concat(i.segments),i.children)}return n}function hi(n){return n instanceof nt}function wh(n,i,e=null,t=null){let r=bc(n);return yc(r,i,e,t)}function bc(n){let i;function e(o){let a={};for(let c of o.children){let m=e(c);a[c.outlet]=m}let s=new K(o.url,a);return o===n&&(i=s),s}let t=e(n.root),r=_c(t);return i??r}function yc(n,i,e,t){let r=n;for(;r.parent;)r=r.parent;if(i.length===0)return aa(r,r,r,e,t);let o=Dh(i);if(o.toRoot())return aa(r,r,new K([],{}),e,t);let a=xh(o,r,n),s=a.processChildren?en(a.segmentGroup,a.index,o.commands):wc(a.segmentGroup,a.index,o.commands);return aa(r,a.segmentGroup,s,e,t)}function vr(n){return typeof n=="object"&&n!=null&&!n.outlets&&!n.segmentPath}function nn(n){return typeof n=="object"&&n!=null&&n.outlets}function aa(n,i,e,t,r){let o={};t&&Object.entries(t).forEach(([c,m])=>{o[c]=Array.isArray(m)?m.map(h=>`${h}`):`${m}`});let a;n===i?a=e:a=Cc(n,i,e);let s=_c(vc(a));return new nt(s,o,r)}function Cc(n,i,e){let t={};return Object.entries(n.children).forEach(([r,o])=>{o===i?t[r]=e:t[r]=Cc(o,i,e)}),new K(n.segments,t)}var br=class{isAbsolute;numberOfDoubleDots;commands;constructor(i,e,t){if(this.isAbsolute=i,this.numberOfDoubleDots=e,this.commands=t,i&&t.length>0&&vr(t[0]))throw new z(4003,!1);let r=t.find(nn);if(r&&r!==dc(t))throw new z(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Dh(n){if(typeof n[0]=="string"&&n.length===1&&n[0]==="/")return new br(!0,0,n);let i=0,e=!1,t=n.reduce((r,o,a)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let s={};return Object.entries(o.outlets).forEach(([c,m])=>{s[c]=typeof m=="string"?m.split("/"):m}),[...r,{outlets:s}]}if(o.segmentPath)return[...r,o.segmentPath]}return typeof o!="string"?[...r,o]:a===0?(o.split("/").forEach((s,c)=>{c==0&&s==="."||(c==0&&s===""?e=!0:s===".."?i++:s!=""&&r.push(s))}),r):[...r,o]},[]);return new br(e,i,t)}var li=class{segmentGroup;processChildren;index;constructor(i,e,t){this.segmentGroup=i,this.processChildren=e,this.index=t}};function xh(n,i,e){if(n.isAbsolute)return new li(i,!0,0);if(!e)return new li(i,!1,NaN);if(e.parent===null)return new li(e,!0,0);let t=vr(n.commands[0])?0:1,r=e.segments.length-1+t;return Eh(e,r,n.numberOfDoubleDots)}function Eh(n,i,e){let t=n,r=i,o=e;for(;o>r;){if(o-=r,t=t.parent,!t)throw new z(4005,!1);r=t.segments.length}return new li(t,!1,r-o)}function Sh(n){return nn(n[0])?n[0].outlets:{[j]:n}}function wc(n,i,e){if(n??=new K([],{}),n.segments.length===0&&n.hasChildren())return en(n,i,e);let t=Mh(n,i,e),r=e.slice(t.commandIndex);if(t.match&&t.pathIndex<n.segments.length){let o=new K(n.segments.slice(0,t.pathIndex),{});return o.children[j]=new K(n.segments.slice(t.pathIndex),n.children),en(o,0,r)}else return t.match&&r.length===0?new K(n.segments,{}):t.match&&!n.hasChildren()?ha(n,i,e):t.match?en(n,0,r):ha(n,i,e)}function en(n,i,e){if(e.length===0)return new K(n.segments,{});{let t=Sh(e),r={};if(Object.keys(t).some(o=>o!==j)&&n.children[j]&&n.numberOfChildren===1&&n.children[j].segments.length===0){let o=en(n.children[j],i,e);return new K(n.segments,o.children)}return Object.entries(t).forEach(([o,a])=>{typeof a=="string"&&(a=[a]),a!==null&&(r[o]=wc(n.children[o],i,a))}),Object.entries(n.children).forEach(([o,a])=>{t[o]===void 0&&(r[o]=a)}),new K(n.segments,r)}}function Mh(n,i,e){let t=0,r=i,o={match:!1,pathIndex:0,commandIndex:0};for(;r<n.segments.length;){if(t>=e.length)return o;let a=n.segments[r],s=e[t];if(nn(s))break;let c=`${s}`,m=t<e.length-1?e[t+1]:null;if(r>0&&c===void 0)break;if(c&&m&&typeof m=="object"&&m.outlets===void 0){if(!oc(c,m,a))return o;t+=2}else{if(!oc(c,{},a))return o;t++}r++}return{match:!0,pathIndex:r,commandIndex:t}}function ha(n,i,e){let t=n.segments.slice(0,i),r=0;for(;r<e.length;){let o=e[r];if(nn(o)){let c=Ah(o.outlets);return new K(t,c)}if(r===0&&vr(e[0])){let c=n.segments[i];t.push(new Wt(c.path,rc(e[0]))),r++;continue}let a=nn(o)?o.outlets[j]:`${o}`,s=r<e.length-1?e[r+1]:null;a&&s&&vr(s)?(t.push(new Wt(a,rc(s))),r+=2):(t.push(new Wt(a,{})),r++)}return new K(t,{})}function Ah(n){let i={};return Object.entries(n).forEach(([e,t])=>{typeof t=="string"&&(t=[t]),t!==null&&(i[e]=ha(new K([],{}),0,t))}),i}function rc(n){let i={};return Object.entries(n).forEach(([e,t])=>i[e]=`${t}`),i}function oc(n,i,e){return n==e.path&&Xe(i,e.parameters)}var _r="imperative",_e=function(n){return n[n.NavigationStart=0]="NavigationStart",n[n.NavigationEnd=1]="NavigationEnd",n[n.NavigationCancel=2]="NavigationCancel",n[n.NavigationError=3]="NavigationError",n[n.RoutesRecognized=4]="RoutesRecognized",n[n.ResolveStart=5]="ResolveStart",n[n.ResolveEnd=6]="ResolveEnd",n[n.GuardsCheckStart=7]="GuardsCheckStart",n[n.GuardsCheckEnd=8]="GuardsCheckEnd",n[n.RouteConfigLoadStart=9]="RouteConfigLoadStart",n[n.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",n[n.ChildActivationStart=11]="ChildActivationStart",n[n.ChildActivationEnd=12]="ChildActivationEnd",n[n.ActivationStart=13]="ActivationStart",n[n.ActivationEnd=14]="ActivationEnd",n[n.Scroll=15]="Scroll",n[n.NavigationSkipped=16]="NavigationSkipped",n}(_e||{}),Ue=class{id;url;constructor(i,e){this.id=i,this.url=e}},fi=class extends Ue{type=_e.NavigationStart;navigationTrigger;restoredState;constructor(i,e,t="imperative",r=null){super(i,e),this.navigationTrigger=t,this.restoredState=r}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ct=class extends Ue{urlAfterRedirects;type=_e.NavigationEnd;constructor(i,e,t){super(i,e),this.urlAfterRedirects=t}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Te=function(n){return n[n.Redirect=0]="Redirect",n[n.SupersededByNewNavigation=1]="SupersededByNewNavigation",n[n.NoDataFromResolver=2]="NoDataFromResolver",n[n.GuardRejected=3]="GuardRejected",n}(Te||{}),yr=function(n){return n[n.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",n[n.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",n}(yr||{}),it=class extends Ue{reason;code;type=_e.NavigationCancel;constructor(i,e,t,r){super(i,e),this.reason=t,this.code=r}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},wt=class extends Ue{reason;code;type=_e.NavigationSkipped;constructor(i,e,t,r){super(i,e),this.reason=t,this.code=r}},rn=class extends Ue{error;target;type=_e.NavigationError;constructor(i,e,t,r){super(i,e),this.error=t,this.target=r}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Cr=class extends Ue{urlAfterRedirects;state;type=_e.RoutesRecognized;constructor(i,e,t,r){super(i,e),this.urlAfterRedirects=t,this.state=r}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},fa=class extends Ue{urlAfterRedirects;state;type=_e.GuardsCheckStart;constructor(i,e,t,r){super(i,e),this.urlAfterRedirects=t,this.state=r}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},pa=class extends Ue{urlAfterRedirects;state;shouldActivate;type=_e.GuardsCheckEnd;constructor(i,e,t,r,o){super(i,e),this.urlAfterRedirects=t,this.state=r,this.shouldActivate=o}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ga=class extends Ue{urlAfterRedirects;state;type=_e.ResolveStart;constructor(i,e,t,r){super(i,e),this.urlAfterRedirects=t,this.state=r}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},_a=class extends Ue{urlAfterRedirects;state;type=_e.ResolveEnd;constructor(i,e,t,r){super(i,e),this.urlAfterRedirects=t,this.state=r}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},va=class{route;type=_e.RouteConfigLoadStart;constructor(i){this.route=i}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ba=class{route;type=_e.RouteConfigLoadEnd;constructor(i){this.route=i}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ya=class{snapshot;type=_e.ChildActivationStart;constructor(i){this.snapshot=i}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ca=class{snapshot;type=_e.ChildActivationEnd;constructor(i){this.snapshot=i}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},wa=class{snapshot;type=_e.ActivationStart;constructor(i){this.snapshot=i}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Da=class{snapshot;type=_e.ActivationEnd;constructor(i){this.snapshot=i}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var on=class{},pi=class{url;navigationBehaviorOptions;constructor(i,e){this.url=i,this.navigationBehaviorOptions=e}};function Ih(n,i){return n.providers&&!n._injector&&(n._injector=No(n.providers,i,`Route: ${n.path}`)),n._injector??i}function Ye(n){return n.outlet||j}function Rh(n,i){let e=n.filter(t=>Ye(t)===i);return e.push(...n.filter(t=>Ye(t)!==i)),e}function un(n){if(!n)return null;if(n.routeConfig?._injector)return n.routeConfig._injector;for(let i=n.parent;i;i=i.parent){let e=i.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var xa=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return un(this.route?.snapshot)??this.rootInjector}constructor(i){this.rootInjector=i,this.children=new mn(this.rootInjector)}},mn=(()=>{class n{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,t){let r=this.getOrCreateContext(e);r.outlet=t,this.contexts.set(e,r)}onChildOutletDestroyed(e){let t=this.getContext(e);t&&(t.outlet=null,t.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let t=this.getContext(e);return t||(t=new xa(this.rootInjector),this.contexts.set(e,t)),t}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(t){return new(t||n)(F(Ne))};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),wr=class{_root;constructor(i){this._root=i}get root(){return this._root.value}parent(i){let e=this.pathFromRoot(i);return e.length>1?e[e.length-2]:null}children(i){let e=Ea(i,this._root);return e?e.children.map(t=>t.value):[]}firstChild(i){let e=Ea(i,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(i){let e=Sa(i,this._root);return e.length<2?[]:e[e.length-2].children.map(r=>r.value).filter(r=>r!==i)}pathFromRoot(i){return Sa(i,this._root).map(e=>e.value)}};function Ea(n,i){if(n===i.value)return i;for(let e of i.children){let t=Ea(n,e);if(t)return t}return null}function Sa(n,i){if(n===i.value)return[i];for(let e of i.children){let t=Sa(n,e);if(t.length)return t.unshift(i),t}return[]}var Re=class{value;children;constructor(i,e){this.value=i,this.children=e}toString(){return`TreeNode(${this.value})`}};function si(n){let i={};return n&&n.children.forEach(e=>i[e.value.outlet]=e),i}var Dr=class extends wr{snapshot;constructor(i,e){super(i),this.snapshot=e,Pa(this,i)}toString(){return this.snapshot.toString()}};function Dc(n){let i=Th(n),e=new xe([new Wt("",{})]),t=new xe({}),r=new xe({}),o=new xe({}),a=new xe(""),s=new Yt(e,t,o,a,r,j,n,i.root);return s.snapshot=i.root,new Dr(new Re(s,[]),i)}function Th(n){let i={},e={},t={},r="",o=new ci([],i,t,r,e,j,n,null,{});return new Er("",new Re(o,[]))}var Yt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(i,e,t,r,o,a,s,c){this.urlSubject=i,this.paramsSubject=e,this.queryParamsSubject=t,this.fragmentSubject=r,this.dataSubject=o,this.outlet=a,this.component=s,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(M(m=>m[dn]))??D(void 0),this.url=i,this.params=e,this.queryParams=t,this.fragment=r,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(M(i=>ui(i))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(M(i=>ui(i))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function xr(n,i,e="emptyOnly"){let t,{routeConfig:r}=n;return i!==null&&(e==="always"||r?.path===""||!i.component&&!i.routeConfig?.loadComponent)?t={params:_(_({},i.params),n.params),data:_(_({},i.data),n.data),resolve:_(_(_(_({},n.data),i.data),r?.data),n._resolvedData)}:t={params:_({},n.params),data:_({},n.data),resolve:_(_({},n.data),n._resolvedData??{})},r&&Ec(r)&&(t.resolve[dn]=r.title),t}var ci=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[dn]}constructor(i,e,t,r,o,a,s,c,m){this.url=i,this.params=e,this.queryParams=t,this.fragment=r,this.data=o,this.outlet=a,this.component=s,this.routeConfig=c,this._resolve=m}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ui(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ui(this.queryParams),this._queryParamMap}toString(){let i=this.url.map(t=>t.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${i}', path:'${e}')`}},Er=class extends wr{url;constructor(i,e){super(e),this.url=i,Pa(this,e)}toString(){return xc(this._root)}};function Pa(n,i){i.value._routerState=n,i.children.forEach(e=>Pa(n,e))}function xc(n){let i=n.children.length>0?` { ${n.children.map(xc).join(", ")} } `:"";return`${n.value}${i}`}function sa(n){if(n.snapshot){let i=n.snapshot,e=n._futureSnapshot;n.snapshot=e,Xe(i.queryParams,e.queryParams)||n.queryParamsSubject.next(e.queryParams),i.fragment!==e.fragment&&n.fragmentSubject.next(e.fragment),Xe(i.params,e.params)||n.paramsSubject.next(e.params),rh(i.url,e.url)||n.urlSubject.next(e.url),Xe(i.data,e.data)||n.dataSubject.next(e.data)}else n.snapshot=n._futureSnapshot,n.dataSubject.next(n._futureSnapshot.data)}function Ma(n,i){let e=Xe(n.params,i.params)&&lh(n.url,i.url),t=!n.parent!=!i.parent;return e&&!t&&(!n.parent||Ma(n.parent,i.parent))}function Ec(n){return typeof n.title=="string"||n.title===null}var kh=new b(""),Sc=(()=>{class n{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;activateEvents=new Y;deactivateEvents=new Y;attachEvents=new Y;detachEvents=new Y;routerOutletData=Ks(void 0);parentContexts=l(mn);location=l(Ke);changeDetector=l(qe);inputBinder=l(Ir,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:t,previousValue:r}=e.name;if(t)return;this.isTrackedInParentContexts(r)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(r)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new z(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new z(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new z(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,t){this.activated=e,this._activatedRoute=t,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,t){if(this.isActivated)throw new z(4013,!1);this._activatedRoute=e;let r=this.location,a=e.snapshot.component,s=this.parentContexts.getOrCreateContext(this.name).children,c=new Aa(e,s,r.injector,this.routerOutletData);this.activated=r.createComponent(a,{index:r.length,injector:c,environmentInjector:t}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ce]})}return n})(),Aa=class{route;childContexts;parent;outletData;constructor(i,e,t,r){this.route=i,this.childContexts=e,this.parent=t,this.outletData=r}get(i,e){return i===Yt?this.route:i===mn?this.childContexts:i===kh?this.outletData:this.parent.get(i,e)}},Ir=new b("");var Mc=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(t,r){t&1&&O(0,"router-outlet")},dependencies:[Sc],encapsulation:2})}return n})();function Na(n){let i=n.children&&n.children.map(Na),e=i?ee(_({},n),{children:i}):_({},n);return!e.component&&!e.loadComponent&&(i||e.loadChildren)&&e.outlet&&e.outlet!==j&&(e.component=Mc),e}function Fh(n,i,e){let t=an(n,i._root,e?e._root:void 0);return new Dr(t,i)}function an(n,i,e){if(e&&n.shouldReuseRoute(i.value,e.value.snapshot)){let t=e.value;t._futureSnapshot=i.value;let r=Oh(n,i,e);return new Re(t,r)}else{if(n.shouldAttach(i.value)){let o=n.retrieve(i.value);if(o!==null){let a=o.route;return a.value._futureSnapshot=i.value,a.children=i.children.map(s=>an(n,s)),a}}let t=Ph(i.value),r=i.children.map(o=>an(n,o));return new Re(t,r)}}function Oh(n,i,e){return i.children.map(t=>{for(let r of e.children)if(n.shouldReuseRoute(t.value,r.value.snapshot))return an(n,t,r);return an(n,t)})}function Ph(n){return new Yt(new xe(n.url),new xe(n.params),new xe(n.queryParams),new xe(n.fragment),new xe(n.data),n.outlet,n.component,n)}var sn=class{redirectTo;navigationBehaviorOptions;constructor(i,e){this.redirectTo=i,this.navigationBehaviorOptions=e}},Ac="ngNavigationCancelingError";function Sr(n,i){let{redirectTo:e,navigationBehaviorOptions:t}=hi(i)?{redirectTo:i,navigationBehaviorOptions:void 0}:i,r=Ic(!1,Te.Redirect);return r.url=e,r.navigationBehaviorOptions=t,r}function Ic(n,i){let e=new Error(`NavigationCancelingError: ${n||""}`);return e[Ac]=!0,e.cancellationCode=i,e}function Nh(n){return Rc(n)&&hi(n.url)}function Rc(n){return!!n&&n[Ac]}var Lh=(n,i,e,t)=>M(r=>(new Ia(i,r.targetRouterState,r.currentRouterState,e,t).activate(n),r)),Ia=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(i,e,t,r,o){this.routeReuseStrategy=i,this.futureState=e,this.currState=t,this.forwardEvent=r,this.inputBindingEnabled=o}activate(i){let e=this.futureState._root,t=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,t,i),sa(this.futureState.root),this.activateChildRoutes(e,t,i)}deactivateChildRoutes(i,e,t){let r=si(e);i.children.forEach(o=>{let a=o.value.outlet;this.deactivateRoutes(o,r[a],t),delete r[a]}),Object.values(r).forEach(o=>{this.deactivateRouteAndItsChildren(o,t)})}deactivateRoutes(i,e,t){let r=i.value,o=e?e.value:null;if(r===o)if(r.component){let a=t.getContext(r.outlet);a&&this.deactivateChildRoutes(i,e,a.children)}else this.deactivateChildRoutes(i,e,t);else o&&this.deactivateRouteAndItsChildren(e,t)}deactivateRouteAndItsChildren(i,e){i.value.component&&this.routeReuseStrategy.shouldDetach(i.value.snapshot)?this.detachAndStoreRouteSubtree(i,e):this.deactivateRouteAndOutlet(i,e)}detachAndStoreRouteSubtree(i,e){let t=e.getContext(i.value.outlet),r=t&&i.value.component?t.children:e,o=si(i);for(let a of Object.values(o))this.deactivateRouteAndItsChildren(a,r);if(t&&t.outlet){let a=t.outlet.detach(),s=t.children.onOutletDeactivated();this.routeReuseStrategy.store(i.value.snapshot,{componentRef:a,route:i,contexts:s})}}deactivateRouteAndOutlet(i,e){let t=e.getContext(i.value.outlet),r=t&&i.value.component?t.children:e,o=si(i);for(let a of Object.values(o))this.deactivateRouteAndItsChildren(a,r);t&&(t.outlet&&(t.outlet.deactivate(),t.children.onOutletDeactivated()),t.attachRef=null,t.route=null)}activateChildRoutes(i,e,t){let r=si(e);i.children.forEach(o=>{this.activateRoutes(o,r[o.value.outlet],t),this.forwardEvent(new Da(o.value.snapshot))}),i.children.length&&this.forwardEvent(new Ca(i.value.snapshot))}activateRoutes(i,e,t){let r=i.value,o=e?e.value:null;if(sa(r),r===o)if(r.component){let a=t.getOrCreateContext(r.outlet);this.activateChildRoutes(i,e,a.children)}else this.activateChildRoutes(i,e,t);else if(r.component){let a=t.getOrCreateContext(r.outlet);if(this.routeReuseStrategy.shouldAttach(r.snapshot)){let s=this.routeReuseStrategy.retrieve(r.snapshot);this.routeReuseStrategy.store(r.snapshot,null),a.children.onOutletReAttached(s.contexts),a.attachRef=s.componentRef,a.route=s.route.value,a.outlet&&a.outlet.attach(s.componentRef,s.route.value),sa(s.route.value),this.activateChildRoutes(i,null,a.children)}else a.attachRef=null,a.route=r,a.outlet&&a.outlet.activateWith(r,a.injector),this.activateChildRoutes(i,null,a.children)}else this.activateChildRoutes(i,null,t)}},Mr=class{path;route;constructor(i){this.path=i,this.route=this.path[this.path.length-1]}},di=class{component;route;constructor(i,e){this.component=i,this.route=e}};function Bh(n,i,e){let t=n._root,r=i?i._root:null;return Ji(t,r,e,[t.value])}function Vh(n){let i=n.routeConfig?n.routeConfig.canActivateChild:null;return!i||i.length===0?null:{node:n,guards:i}}function _i(n,i){let e=Symbol(),t=i.get(n,e);return t===e?typeof n=="function"&&!Ws(n)?n:i.get(n):t}function Ji(n,i,e,t,r={canDeactivateChecks:[],canActivateChecks:[]}){let o=si(i);return n.children.forEach(a=>{jh(a,o[a.value.outlet],e,t.concat([a.value]),r),delete o[a.value.outlet]}),Object.entries(o).forEach(([a,s])=>tn(s,e.getContext(a),r)),r}function jh(n,i,e,t,r={canDeactivateChecks:[],canActivateChecks:[]}){let o=n.value,a=i?i.value:null,s=e?e.getContext(n.value.outlet):null;if(a&&o.routeConfig===a.routeConfig){let c=zh(a,o,o.routeConfig.runGuardsAndResolvers);c?r.canActivateChecks.push(new Mr(t)):(o.data=a.data,o._resolvedData=a._resolvedData),o.component?Ji(n,i,s?s.children:null,t,r):Ji(n,i,e,t,r),c&&s&&s.outlet&&s.outlet.isActivated&&r.canDeactivateChecks.push(new di(s.outlet.component,a))}else a&&tn(i,s,r),r.canActivateChecks.push(new Mr(t)),o.component?Ji(n,null,s?s.children:null,t,r):Ji(n,null,e,t,r);return r}function zh(n,i,e){if(typeof e=="function")return e(n,i);switch(e){case"pathParamsChange":return!qt(n.url,i.url);case"pathParamsOrQueryParamsChange":return!qt(n.url,i.url)||!Xe(n.queryParams,i.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Ma(n,i)||!Xe(n.queryParams,i.queryParams);case"paramsChange":default:return!Ma(n,i)}}function tn(n,i,e){let t=si(n),r=n.value;Object.entries(t).forEach(([o,a])=>{r.component?i?tn(a,i.children.getContext(o),e):tn(a,null,e):tn(a,i,e)}),r.component?i&&i.outlet&&i.outlet.isActivated?e.canDeactivateChecks.push(new di(i.outlet.component,r)):e.canDeactivateChecks.push(new di(null,r)):e.canDeactivateChecks.push(new di(null,r))}function hn(n){return typeof n=="function"}function Uh(n){return typeof n=="boolean"}function Hh(n){return n&&hn(n.canLoad)}function $h(n){return n&&hn(n.canActivate)}function Gh(n){return n&&hn(n.canActivateChild)}function Wh(n){return n&&hn(n.canDeactivate)}function qh(n){return n&&hn(n.canMatch)}function Tc(n){return n instanceof zs||n?.name==="EmptyError"}var hr=Symbol("INITIAL_VALUE");function gi(){return ye(n=>Ti(n.map(i=>i.pipe(Pe(1),Ze(hr)))).pipe(M(i=>{for(let e of i)if(e!==!0){if(e===hr)return hr;if(e===!1||Yh(e))return e}return!0}),ae(i=>i!==hr),Pe(1)))}function Yh(n){return hi(n)||n instanceof sn}function Zh(n,i){return Oe(e=>{let{targetSnapshot:t,currentSnapshot:r,guards:{canActivateChecks:o,canDeactivateChecks:a}}=e;return a.length===0&&o.length===0?D(ee(_({},e),{guardsResult:!0})):Kh(a,t,r,n).pipe(Oe(s=>s&&Uh(s)?Xh(t,o,n,i):D(s)),M(s=>ee(_({},e),{guardsResult:s})))})}function Kh(n,i,e,t){return be(n).pipe(Oe(r=>nf(r.component,r.route,e,i,t)),ut(r=>r!==!0,!0))}function Xh(n,i,e,t){return be(i).pipe(kt(r=>jn(Jh(r.route.parent,t),Qh(r.route,t),tf(n,r.path,e),ef(n,r.route,e))),ut(r=>r!==!0,!0))}function Qh(n,i){return n!==null&&i&&i(new wa(n)),D(!0)}function Jh(n,i){return n!==null&&i&&i(new ya(n)),D(!0)}function ef(n,i,e){let t=i.routeConfig?i.routeConfig.canActivate:null;if(!t||t.length===0)return D(!0);let r=t.map(o=>ki(()=>{let a=un(i)??e,s=_i(o,a),c=$h(s)?s.canActivate(i,n):We(a,()=>s(i,n));return Dt(c).pipe(ut())}));return D(r).pipe(gi())}function tf(n,i,e){let t=i[i.length-1],o=i.slice(0,i.length-1).reverse().map(a=>Vh(a)).filter(a=>a!==null).map(a=>ki(()=>{let s=a.guards.map(c=>{let m=un(a.node)??e,h=_i(c,m),C=Gh(h)?h.canActivateChild(t,n):We(m,()=>h(t,n));return Dt(C).pipe(ut())});return D(s).pipe(gi())}));return D(o).pipe(gi())}function nf(n,i,e,t,r){let o=i&&i.routeConfig?i.routeConfig.canDeactivate:null;if(!o||o.length===0)return D(!0);let a=o.map(s=>{let c=un(i)??r,m=_i(s,c),h=Wh(m)?m.canDeactivate(n,i,e,t):We(c,()=>m(n,i,e,t));return Dt(h).pipe(ut())});return D(a).pipe(gi())}function rf(n,i,e,t){let r=i.canLoad;if(r===void 0||r.length===0)return D(!0);let o=r.map(a=>{let s=_i(a,n),c=Hh(s)?s.canLoad(i,e):We(n,()=>s(i,e));return Dt(c)});return D(o).pipe(gi(),kc(t))}function kc(n){return js(oe(i=>{if(typeof i!="boolean")throw Sr(n,i)}),M(i=>i===!0))}function of(n,i,e,t){let r=i.canMatch;if(!r||r.length===0)return D(!0);let o=r.map(a=>{let s=_i(a,n),c=qh(s)?s.canMatch(i,e):We(n,()=>s(i,e));return Dt(c)});return D(o).pipe(gi(),kc(t))}var ln=class{segmentGroup;constructor(i){this.segmentGroup=i||null}},cn=class extends Error{urlTree;constructor(i){super(),this.urlTree=i}};function ai(n){return Tt(new ln(n))}function af(n){return Tt(new z(4e3,!1))}function sf(n){return Tt(Ic(!1,Te.GuardRejected))}var Ra=class{urlSerializer;urlTree;constructor(i,e){this.urlSerializer=i,this.urlTree=e}lineralizeSegments(i,e){let t=[],r=e.root;for(;;){if(t=t.concat(r.segments),r.numberOfChildren===0)return D(t);if(r.numberOfChildren>1||!r.children[j])return af(`${i.redirectTo}`);r=r.children[j]}}applyRedirectCommands(i,e,t,r,o){if(typeof e!="string"){let s=e,{queryParams:c,fragment:m,routeConfig:h,url:C,outlet:x,params:A,data:L,title:w}=r,B=We(o,()=>s({params:A,data:L,queryParams:c,fragment:m,routeConfig:h,url:C,outlet:x,title:w}));if(B instanceof nt)throw new cn(B);e=B}let a=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),i,t);if(e[0]==="/")throw new cn(a);return a}applyRedirectCreateUrlTree(i,e,t,r){let o=this.createSegmentGroup(i,e.root,t,r);return new nt(o,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(i,e){let t={};return Object.entries(i).forEach(([r,o])=>{if(typeof o=="string"&&o[0]===":"){let s=o.substring(1);t[r]=e[s]}else t[r]=o}),t}createSegmentGroup(i,e,t,r){let o=this.createSegments(i,e.segments,t,r),a={};return Object.entries(e.children).forEach(([s,c])=>{a[s]=this.createSegmentGroup(i,c,t,r)}),new K(o,a)}createSegments(i,e,t,r){return e.map(o=>o.path[0]===":"?this.findPosParam(i,o,r):this.findOrReturn(o,t))}findPosParam(i,e,t){let r=t[e.path.substring(1)];if(!r)throw new z(4001,!1);return r}findOrReturn(i,e){let t=0;for(let r of e){if(r.path===i.path)return e.splice(t),r;t++}return i}},Ta={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function lf(n,i,e,t,r){let o=Fc(n,i,e);return o.matched?(t=Ih(i,t),of(t,i,e,r).pipe(M(a=>a===!0?o:_({},Ta)))):D(o)}function Fc(n,i,e){if(i.path==="**")return cf(e);if(i.path==="")return i.pathMatch==="full"&&(n.hasChildren()||e.length>0)?_({},Ta):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let r=(i.matcher||nh)(e,n,i);if(!r)return _({},Ta);let o={};Object.entries(r.posParams??{}).forEach(([s,c])=>{o[s]=c.path});let a=r.consumed.length>0?_(_({},o),r.consumed[r.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:r.consumed,remainingSegments:e.slice(r.consumed.length),parameters:a,positionalParamSegments:r.posParams??{}}}function cf(n){return{matched:!0,parameters:n.length>0?dc(n).parameters:{},consumedSegments:n,remainingSegments:[],positionalParamSegments:{}}}function ac(n,i,e,t){return e.length>0&&mf(n,e,t)?{segmentGroup:new K(i,uf(t,new K(e,n.children))),slicedSegments:[]}:e.length===0&&hf(n,e,t)?{segmentGroup:new K(n.segments,df(n,e,t,n.children)),slicedSegments:e}:{segmentGroup:new K(n.segments,n.children),slicedSegments:e}}function df(n,i,e,t){let r={};for(let o of e)if(Rr(n,i,o)&&!t[Ye(o)]){let a=new K([],{});r[Ye(o)]=a}return _(_({},t),r)}function uf(n,i){let e={};e[j]=i;for(let t of n)if(t.path===""&&Ye(t)!==j){let r=new K([],{});e[Ye(t)]=r}return e}function mf(n,i,e){return e.some(t=>Rr(n,i,t)&&Ye(t)!==j)}function hf(n,i,e){return e.some(t=>Rr(n,i,t))}function Rr(n,i,e){return(n.hasChildren()||i.length>0)&&e.pathMatch==="full"?!1:e.path===""}function ff(n,i,e){return i.length===0&&!n.children[e]}var ka=class{};function pf(n,i,e,t,r,o,a="emptyOnly"){return new Fa(n,i,e,t,r,a,o).recognize()}var gf=31,Fa=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(i,e,t,r,o,a,s){this.injector=i,this.configLoader=e,this.rootComponentType=t,this.config=r,this.urlTree=o,this.paramsInheritanceStrategy=a,this.urlSerializer=s,this.applyRedirects=new Ra(this.urlSerializer,this.urlTree)}noMatchError(i){return new z(4002,`'${i.segmentGroup}'`)}recognize(){let i=ac(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(i).pipe(M(({children:e,rootSnapshot:t})=>{let r=new Re(t,e),o=new Er("",r),a=wh(t,[],this.urlTree.queryParams,this.urlTree.fragment);return a.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(a),{state:o,tree:a}}))}match(i){let e=new ci([],Object.freeze({}),Object.freeze(_({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),j,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,i,j,e).pipe(M(t=>({children:t,rootSnapshot:e})),ct(t=>{if(t instanceof cn)return this.urlTree=t.urlTree,this.match(t.urlTree.root);throw t instanceof ln?this.noMatchError(t):t}))}processSegmentGroup(i,e,t,r,o){return t.segments.length===0&&t.hasChildren()?this.processChildren(i,e,t,o):this.processSegment(i,e,t,t.segments,r,!0,o).pipe(M(a=>a instanceof Re?[a]:[]))}processChildren(i,e,t,r){let o=[];for(let a of Object.keys(t.children))a==="primary"?o.unshift(a):o.push(a);return be(o).pipe(kt(a=>{let s=t.children[a],c=Rh(e,a);return this.processSegmentGroup(i,c,s,a,r)}),Hs((a,s)=>(a.push(...s),a)),So(null),Us(),Oe(a=>{if(a===null)return ai(t);let s=Oc(a);return _f(s),D(s)}))}processSegment(i,e,t,r,o,a,s){return be(e).pipe(kt(c=>this.processSegmentAgainstRoute(c._injector??i,e,c,t,r,o,a,s).pipe(ct(m=>{if(m instanceof ln)return D(null);throw m}))),ut(c=>!!c),ct(c=>{if(Tc(c))return ff(t,r,o)?D(new ka):ai(t);throw c}))}processSegmentAgainstRoute(i,e,t,r,o,a,s,c){return Ye(t)!==a&&(a===j||!Rr(r,o,t))?ai(r):t.redirectTo===void 0?this.matchSegmentAgainstRoute(i,r,t,o,a,c):this.allowRedirects&&s?this.expandSegmentAgainstRouteUsingRedirect(i,r,e,t,o,a,c):ai(r)}expandSegmentAgainstRouteUsingRedirect(i,e,t,r,o,a,s){let{matched:c,parameters:m,consumedSegments:h,positionalParamSegments:C,remainingSegments:x}=Fc(e,r,o);if(!c)return ai(e);typeof r.redirectTo=="string"&&r.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>gf&&(this.allowRedirects=!1));let A=new ci(o,m,Object.freeze(_({},this.urlTree.queryParams)),this.urlTree.fragment,sc(r),Ye(r),r.component??r._loadedComponent??null,r,lc(r)),L=xr(A,s,this.paramsInheritanceStrategy);A.params=Object.freeze(L.params),A.data=Object.freeze(L.data);let w=this.applyRedirects.applyRedirectCommands(h,r.redirectTo,C,A,i);return this.applyRedirects.lineralizeSegments(r,w).pipe(Oe(B=>this.processSegment(i,t,e,B.concat(x),a,!1,s)))}matchSegmentAgainstRoute(i,e,t,r,o,a){let s=lf(e,t,r,i,this.urlSerializer);return t.path==="**"&&(e.children={}),s.pipe(ye(c=>c.matched?(i=t._injector??i,this.getChildConfig(i,t,r).pipe(ye(({routes:m})=>{let h=t._loadedInjector??i,{parameters:C,consumedSegments:x,remainingSegments:A}=c,L=new ci(x,C,Object.freeze(_({},this.urlTree.queryParams)),this.urlTree.fragment,sc(t),Ye(t),t.component??t._loadedComponent??null,t,lc(t)),w=xr(L,a,this.paramsInheritanceStrategy);L.params=Object.freeze(w.params),L.data=Object.freeze(w.data);let{segmentGroup:B,slicedSegments:me}=ac(e,x,A,m);if(me.length===0&&B.hasChildren())return this.processChildren(h,m,B,L).pipe(M(te=>new Re(L,te)));if(m.length===0&&me.length===0)return D(new Re(L,[]));let Ae=Ye(t)===o;return this.processSegment(h,m,B,me,Ae?j:o,!0,L).pipe(M(te=>new Re(L,te instanceof Re?[te]:[])))}))):ai(e)))}getChildConfig(i,e,t){return e.children?D({routes:e.children,injector:i}):e.loadChildren?e._loadedRoutes!==void 0?D({routes:e._loadedRoutes,injector:e._loadedInjector}):rf(i,e,t,this.urlSerializer).pipe(Oe(r=>r?this.configLoader.loadChildren(i,e).pipe(oe(o=>{e._loadedRoutes=o.routes,e._loadedInjector=o.injector})):sf(e))):D({routes:[],injector:i})}};function _f(n){n.sort((i,e)=>i.value.outlet===j?-1:e.value.outlet===j?1:i.value.outlet.localeCompare(e.value.outlet))}function vf(n){let i=n.value.routeConfig;return i&&i.path===""}function Oc(n){let i=[],e=new Set;for(let t of n){if(!vf(t)){i.push(t);continue}let r=i.find(o=>t.value.routeConfig===o.value.routeConfig);r!==void 0?(r.children.push(...t.children),e.add(r)):i.push(t)}for(let t of e){let r=Oc(t.children);i.push(new Re(t.value,r))}return i.filter(t=>!e.has(t))}function sc(n){return n.data||{}}function lc(n){return n.resolve||{}}function bf(n,i,e,t,r,o){return Oe(a=>pf(n,i,e,t,a.extractedUrl,r,o).pipe(M(({state:s,tree:c})=>ee(_({},a),{targetSnapshot:s,urlAfterRedirects:c}))))}function yf(n,i){return Oe(e=>{let{targetSnapshot:t,guards:{canActivateChecks:r}}=e;if(!r.length)return D(e);let o=new Set(r.map(c=>c.route)),a=new Set;for(let c of o)if(!a.has(c))for(let m of Pc(c))a.add(m);let s=0;return be(a).pipe(kt(c=>o.has(c)?Cf(c,t,n,i):(c.data=xr(c,c.parent,n).resolve,D(void 0))),oe(()=>s++),Ao(1),Oe(c=>s===a.size?D(e):Ge))})}function Pc(n){let i=n.children.map(e=>Pc(e)).flat();return[n,...i]}function Cf(n,i,e,t){let r=n.routeConfig,o=n._resolve;return r?.title!==void 0&&!Ec(r)&&(o[dn]=r.title),wf(o,n,i,t).pipe(M(a=>(n._resolvedData=a,n.data=xr(n,n.parent,e).resolve,null)))}function wf(n,i,e,t){let r=da(n);if(r.length===0)return D({});let o={};return be(r).pipe(Oe(a=>Df(n[a],i,e,t).pipe(ut(),oe(s=>{if(s instanceof sn)throw Sr(new mi,s);o[a]=s}))),Ao(1),M(()=>o),ct(a=>Tc(a)?Ge:Tt(a)))}function Df(n,i,e,t){let r=un(i)??t,o=_i(n,r),a=o.resolve?o.resolve(i,e):We(r,()=>o(i,e));return Dt(a)}function la(n){return ye(i=>{let e=n(i);return e?be(e).pipe(M(()=>i)):D(i)})}var Nc=(()=>{class n{buildTitle(e){let t,r=e.root;for(;r!==void 0;)t=this.getResolvedTitleForRoute(r)??t,r=r.children.find(o=>o.outlet===j);return t}getResolvedTitleForRoute(e){return e.data[dn]}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>l(xf),providedIn:"root"})}return n})(),xf=(()=>{class n extends Nc{title;constructor(e){super(),this.title=e}updateTitle(e){let t=this.buildTitle(e);t!==void 0&&this.title.setTitle(t)}static \u0275fac=function(t){return new(t||n)(F(ec))};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Tr=new b("",{providedIn:"root",factory:()=>({})}),kr=new b(""),Lc=(()=>{class n{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=l(vl);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return D(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let t=Dt(e.loadComponent()).pipe(M(Bc),oe(o=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=o}),dt(()=>{this.componentLoaders.delete(e)})),r=new Do(t,()=>new E).pipe(wo());return this.componentLoaders.set(e,r),r}loadChildren(e,t){if(this.childrenLoaders.get(t))return this.childrenLoaders.get(t);if(t._loadedRoutes)return D({routes:t._loadedRoutes,injector:t._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(t);let o=Ef(t,this.compiler,e,this.onLoadEndListener).pipe(dt(()=>{this.childrenLoaders.delete(t)})),a=new Do(o,()=>new E).pipe(wo());return this.childrenLoaders.set(t,a),a}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Ef(n,i,e,t){return Dt(n.loadChildren()).pipe(M(Bc),Oe(r=>r instanceof cl||Array.isArray(r)?D(r):be(i.compileModuleAsync(r))),M(r=>{t&&t(n);let o,a,s=!1;return Array.isArray(r)?(a=r,s=!0):(o=r.create(e).injector,a=o.get(kr,[],{optional:!0,self:!0}).flat()),{routes:a.map(Na),injector:o}}))}function Sf(n){return n&&typeof n=="object"&&"default"in n}function Bc(n){return Sf(n)?n.default:n}var La=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>l(Mf),providedIn:"root"})}return n})(),Mf=(()=>{class n{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,t){return e}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Vc=new b("");var jc=new b(""),zc=(()=>{class n{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new E;transitionAbortSubject=new E;configLoader=l(Lc);environmentInjector=l(Ne);destroyRef=l(ko);urlSerializer=l(Ar);rootContexts=l(mn);location=l(_t);inputBindingEnabled=l(Ir,{optional:!0})!==null;titleStrategy=l(Nc);options=l(Tr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=l(La);createViewTransition=l(Vc,{optional:!0});navigationErrorHandler=l(jc,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>D(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=r=>this.events.next(new va(r)),t=r=>this.events.next(new ba(r));this.configLoader.onLoadEndListener=t,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let t=++this.navigationId;this.transitions?.next(ee(_({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:t}))}setupNavigations(e){return this.transitions=new xe(null),this.transitions.pipe(ae(t=>t!==null),ye(t=>{let r=!1,o=!1;return D(t).pipe(ye(a=>{if(this.navigationId>t.id)return this.cancelNavigationTransition(t,"",Te.SupersededByNewNavigation),Ge;this.currentTransition=t,this.currentNavigation={id:a.id,initialUrl:a.rawUrl,extractedUrl:a.extractedUrl,targetBrowserUrl:typeof a.extras.browserUrl=="string"?this.urlSerializer.parse(a.extras.browserUrl):a.extras.browserUrl,trigger:a.source,extras:a.extras,previousNavigation:this.lastSuccessfulNavigation?ee(_({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let s=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=a.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!s&&c!=="reload"){let m="";return this.events.next(new wt(a.id,this.urlSerializer.serialize(a.rawUrl),m,yr.IgnoredSameUrlNavigation)),a.resolve(!1),Ge}if(this.urlHandlingStrategy.shouldProcessUrl(a.rawUrl))return D(a).pipe(ye(m=>(this.events.next(new fi(m.id,this.urlSerializer.serialize(m.extractedUrl),m.source,m.restoredState)),m.id!==this.navigationId?Ge:Promise.resolve(m))),bf(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),oe(m=>{t.targetSnapshot=m.targetSnapshot,t.urlAfterRedirects=m.urlAfterRedirects,this.currentNavigation=ee(_({},this.currentNavigation),{finalUrl:m.urlAfterRedirects});let h=new Cr(m.id,this.urlSerializer.serialize(m.extractedUrl),this.urlSerializer.serialize(m.urlAfterRedirects),m.targetSnapshot);this.events.next(h)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(a.currentRawUrl)){let{id:m,extractedUrl:h,source:C,restoredState:x,extras:A}=a,L=new fi(m,this.urlSerializer.serialize(h),C,x);this.events.next(L);let w=Dc(this.rootComponentType).snapshot;return this.currentTransition=t=ee(_({},a),{targetSnapshot:w,urlAfterRedirects:h,extras:ee(_({},A),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=h,D(t)}else{let m="";return this.events.next(new wt(a.id,this.urlSerializer.serialize(a.extractedUrl),m,yr.IgnoredByUrlHandlingStrategy)),a.resolve(!1),Ge}}),oe(a=>{let s=new fa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(s)}),M(a=>(this.currentTransition=t=ee(_({},a),{guards:Bh(a.targetSnapshot,a.currentSnapshot,this.rootContexts)}),t)),Zh(this.environmentInjector,a=>this.events.next(a)),oe(a=>{if(t.guardsResult=a.guardsResult,a.guardsResult&&typeof a.guardsResult!="boolean")throw Sr(this.urlSerializer,a.guardsResult);let s=new pa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot,!!a.guardsResult);this.events.next(s)}),ae(a=>a.guardsResult?!0:(this.cancelNavigationTransition(a,"",Te.GuardRejected),!1)),la(a=>{if(a.guards.canActivateChecks.length!==0)return D(a).pipe(oe(s=>{let c=new ga(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(c)}),ye(s=>{let c=!1;return D(s).pipe(yf(this.paramsInheritanceStrategy,this.environmentInjector),oe({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(s,"",Te.NoDataFromResolver)}}))}),oe(s=>{let c=new _a(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(c)}))}),la(a=>{let s=c=>{let m=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&m.push(this.configLoader.loadComponent(c.routeConfig).pipe(oe(h=>{c.component=h}),M(()=>{})));for(let h of c.children)m.push(...s(h));return m};return Ti(s(a.targetSnapshot.root)).pipe(So(null),Pe(1))}),la(()=>this.afterPreactivation()),ye(()=>{let{currentSnapshot:a,targetSnapshot:s}=t,c=this.createViewTransition?.(this.environmentInjector,a.root,s.root);return c?be(c).pipe(M(()=>t)):D(t)}),M(a=>{let s=Fh(e.routeReuseStrategy,a.targetSnapshot,a.currentRouterState);return this.currentTransition=t=ee(_({},a),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,t}),oe(()=>{this.events.next(new on)}),Lh(this.rootContexts,e.routeReuseStrategy,a=>this.events.next(a),this.inputBindingEnabled),Pe(1),oe({next:a=>{r=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ct(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects))),this.titleStrategy?.updateTitle(a.targetRouterState.snapshot),a.resolve(!0)},complete:()=>{r=!0}}),de(this.transitionAbortSubject.pipe(oe(a=>{throw a}))),dt(()=>{!r&&!o&&this.cancelNavigationTransition(t,"",Te.SupersededByNewNavigation),this.currentTransition?.id===t.id&&(this.currentNavigation=null,this.currentTransition=null)}),ct(a=>{if(this.destroyed)return t.resolve(!1),Ge;if(o=!0,Rc(a))this.events.next(new it(t.id,this.urlSerializer.serialize(t.extractedUrl),a.message,a.cancellationCode)),Nh(a)?this.events.next(new pi(a.url,a.navigationBehaviorOptions)):t.resolve(!1);else{let s=new rn(t.id,this.urlSerializer.serialize(t.extractedUrl),a,t.targetSnapshot??void 0);try{let c=We(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(c instanceof sn){let{message:m,cancellationCode:h}=Sr(this.urlSerializer,c);this.events.next(new it(t.id,this.urlSerializer.serialize(t.extractedUrl),m,h)),this.events.next(new pi(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(s),a}catch(c){this.options.resolveNavigationPromiseOnError?t.resolve(!1):t.reject(c)}}return Ge}))}))}cancelNavigationTransition(e,t,r){let o=new it(e.id,this.urlSerializer.serialize(e.extractedUrl),t,r);this.events.next(o),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),t=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==t?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Af(n){return n!==_r}var If=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>l(Rf),providedIn:"root"})}return n})(),Oa=class{shouldDetach(i){return!1}store(i,e){}shouldAttach(i){return!1}retrieve(i){return null}shouldReuseRoute(i,e){return i.routeConfig===e.routeConfig}},Rf=(()=>{class n extends Oa{static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Uc=(()=>{class n{urlSerializer=l(Ar);options=l(Tr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=l(_t);urlHandlingStrategy=l(La);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new nt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:t,targetBrowserUrl:r}){let o=e!==void 0?this.urlHandlingStrategy.merge(e,t):t,a=r??o;return a instanceof nt?this.urlSerializer.serialize(a):a}commitTransition({targetRouterState:e,finalUrl:t,initialUrl:r}){t&&e?(this.currentUrlTree=t,this.rawUrlTree=this.urlHandlingStrategy.merge(t,r),this.routerState=e):this.rawUrlTree=r}routerState=Dc(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:()=>l(Tf),providedIn:"root"})}return n})(),Tf=(()=>{class n extends Uc{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(t=>{t.type==="popstate"&&setTimeout(()=>{e(t.url,t.state,"popstate")})})}handleRouterEvent(e,t){e instanceof fi?this.updateStateMemento():e instanceof wt?this.commitTransition(t):e instanceof Cr?this.urlUpdateStrategy==="eager"&&(t.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(t),t)):e instanceof on?(this.commitTransition(t),this.urlUpdateStrategy==="deferred"&&!t.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(t),t)):e instanceof it&&(e.code===Te.GuardRejected||e.code===Te.NoDataFromResolver)?this.restoreHistory(t):e instanceof rn?this.restoreHistory(t,!0):e instanceof Ct&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:t,id:r}){let{replaceUrl:o,state:a}=t;if(this.location.isCurrentPathEqualTo(e)||o){let s=this.browserPageId,c=_(_({},a),this.generateNgRouterState(r,s));this.location.replaceState(e,"",c)}else{let s=_(_({},a),this.generateNgRouterState(r,this.browserPageId+1));this.location.go(e,"",s)}}restoreHistory(e,t=!1){if(this.canceledNavigationResolution==="computed"){let r=this.browserPageId,o=this.currentPageId-r;o!==0?this.location.historyGo(o):this.getCurrentUrlTree()===e.finalUrl&&o===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(t&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,t){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:t}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Hc(n,i){n.events.pipe(ae(e=>e instanceof Ct||e instanceof it||e instanceof rn||e instanceof wt),M(e=>e instanceof Ct||e instanceof wt?0:(e instanceof it?e.code===Te.Redirect||e.code===Te.SupersededByNewNavigation:!1)?2:1),ae(e=>e!==2),Pe(1)).subscribe(()=>{i()})}var kf={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Ff={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ba=(()=>{class n{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=l(Lo);stateManager=l(Uc);options=l(Tr,{optional:!0})||{};pendingTasks=l(Gn);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=l(zc);urlSerializer=l(Ar);location=l(_t);urlHandlingStrategy=l(La);_events=new E;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=l(If);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=l(kr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!l(Ir,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new ve;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(t=>{try{let r=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(r!==null&&o!==null){if(this.stateManager.handleRouterEvent(t,o),t instanceof it&&t.code!==Te.Redirect&&t.code!==Te.SupersededByNewNavigation)this.navigated=!0;else if(t instanceof Ct)this.navigated=!0;else if(t instanceof pi){let a=t.navigationBehaviorOptions,s=this.urlHandlingStrategy.merge(t.url,r.currentRawUrl),c=_({browserUrl:r.extras.browserUrl,info:r.extras.info,skipLocationChange:r.extras.skipLocationChange,replaceUrl:r.extras.replaceUrl||this.urlUpdateStrategy==="eager"||Af(r.source)},a);this.scheduleNavigation(s,_r,null,c,{resolve:r.resolve,reject:r.reject,promise:r.promise})}}Pf(t)&&this._events.next(t)}catch(r){this.navigationTransitions.transitionAbortSubject.next(r)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),_r,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,t,r)=>{this.navigateToSyncWithBrowser(e,r,t)})}navigateToSyncWithBrowser(e,t,r){let o={replaceUrl:!0},a=r?.navigationId?r:null;if(r){let c=_({},r);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(o.state=c)}let s=this.parseUrl(e);this.scheduleNavigation(s,t,a,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(Na),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,t={}){let{relativeTo:r,queryParams:o,fragment:a,queryParamsHandling:s,preserveFragment:c}=t,m=c?this.currentUrlTree.fragment:a,h=null;switch(s??this.options.defaultQueryParamsHandling){case"merge":h=_(_({},this.currentUrlTree.queryParams),o);break;case"preserve":h=this.currentUrlTree.queryParams;break;default:h=o||null}h!==null&&(h=this.removeEmptyProps(h));let C;try{let x=r?r.snapshot:this.routerState.snapshot.root;C=bc(x)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),C=this.currentUrlTree.root}return yc(C,e,h,m??null)}navigateByUrl(e,t={skipLocationChange:!1}){let r=hi(e)?e:this.parseUrl(e),o=this.urlHandlingStrategy.merge(r,this.rawUrlTree);return this.scheduleNavigation(o,_r,null,t)}navigate(e,t={skipLocationChange:!1}){return Of(e),this.navigateByUrl(this.createUrlTree(e,t),t)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,t){let r;if(t===!0?r=_({},kf):t===!1?r=_({},Ff):r=t,hi(e))return ic(this.currentUrlTree,e,r);let o=this.parseUrl(e);return ic(this.currentUrlTree,o,r)}removeEmptyProps(e){return Object.entries(e).reduce((t,[r,o])=>(o!=null&&(t[r]=o),t),{})}scheduleNavigation(e,t,r,o,a){if(this.disposed)return Promise.resolve(!1);let s,c,m;a?(s=a.resolve,c=a.reject,m=a.promise):m=new Promise((C,x)=>{s=C,c=x});let h=this.pendingTasks.add();return Hc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(h))}),this.navigationTransitions.handleNavigationRequest({source:t,restoredState:r,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:o,resolve:s,reject:c,promise:m,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),m.catch(C=>Promise.reject(C))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Of(n){for(let i=0;i<n.length;i++)if(n[i]==null)throw new z(4008,!1)}function Pf(n){return!(n instanceof on)&&!(n instanceof pi)}var Nf=new b("");function Va(n,...i){return Ft([{provide:kr,multi:!0,useValue:n},[],{provide:Yt,useFactory:Lf,deps:[Ba]},{provide:ul,multi:!0,useFactory:Bf},i.map(e=>e.\u0275providers)])}function Lf(n){return n.routerState.root}function Bf(){let n=l(se);return i=>{let e=n.get(Lt);if(i!==e.components[0])return;let t=n.get(Ba),r=n.get(Vf);n.get(jf)===1&&t.initialNavigation(),n.get(zf,null,To.Optional)?.setUpPreloading(),n.get(Nf,null,To.Optional)?.init(),t.resetRootComponentType(e.componentTypes[0]),r.closed||(r.next(),r.complete(),r.unsubscribe())}}var Vf=new b("",{factory:()=>new E}),jf=new b("",{providedIn:"root",factory:()=>1});var zf=new b("");var Hf="@",$f=(()=>{class n{doc;delegate;zone;animationType;moduleImpl;_rendererFactoryPromise=null;scheduler=null;injector=l(se);loadingSchedulerFn=l(Gf,{optional:!0});_engine;constructor(e,t,r,o,a){this.doc=e,this.delegate=t,this.zone=r,this.animationType=o,this.moduleImpl=a}ngOnDestroy(){this._engine?.flush()}loadImpl(){let e=()=>this.moduleImpl??import("./chunk-IYJLWEFY.js").then(r=>r),t;return this.loadingSchedulerFn?t=this.loadingSchedulerFn(e):t=e(),t.catch(r=>{throw new z(5300,!1)}).then(({\u0275createEngine:r,\u0275AnimationRendererFactory:o})=>{this._engine=r(this.animationType,this.doc);let a=new o(this.delegate,this._engine,this.zone);return this.delegate=a,a})}createRenderer(e,t){let r=this.delegate.createRenderer(e,t);if(r.\u0275type===0)return r;typeof r.throwOnSyntheticProps=="boolean"&&(r.throwOnSyntheticProps=!1);let o=new ja(r);return t?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(a=>{let s=a.createRenderer(e,t);o.use(s),this.scheduler??=this.injector.get(Zs,null,{optional:!0}),this.scheduler?.notify(10)}).catch(a=>{o.use(r)}),o}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}componentReplaced(e){this._engine?.flush(),this.delegate.componentReplaced?.(e)}static \u0275fac=function(t){sl()};static \u0275prov=v({token:n,factory:n.\u0275fac})}return n})(),ja=class{delegate;replay=[];\u0275type=1;constructor(i){this.delegate=i}use(i){if(this.delegate=i,this.replay!==null){for(let e of this.replay)e(i);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(i,e){return this.delegate.createElement(i,e)}createComment(i){return this.delegate.createComment(i)}createText(i){return this.delegate.createText(i)}get destroyNode(){return this.delegate.destroyNode}appendChild(i,e){this.delegate.appendChild(i,e)}insertBefore(i,e,t,r){this.delegate.insertBefore(i,e,t,r)}removeChild(i,e,t){this.delegate.removeChild(i,e,t)}selectRootElement(i,e){return this.delegate.selectRootElement(i,e)}parentNode(i){return this.delegate.parentNode(i)}nextSibling(i){return this.delegate.nextSibling(i)}setAttribute(i,e,t,r){this.delegate.setAttribute(i,e,t,r)}removeAttribute(i,e,t){this.delegate.removeAttribute(i,e,t)}addClass(i,e){this.delegate.addClass(i,e)}removeClass(i,e){this.delegate.removeClass(i,e)}setStyle(i,e,t,r){this.delegate.setStyle(i,e,t,r)}removeStyle(i,e,t){this.delegate.removeStyle(i,e,t)}setProperty(i,e,t){this.shouldReplay(e)&&this.replay.push(r=>r.setProperty(i,e,t)),this.delegate.setProperty(i,e,t)}setValue(i,e){this.delegate.setValue(i,e)}listen(i,e,t,r){return this.shouldReplay(e)&&this.replay.push(o=>o.listen(i,e,t,r)),this.delegate.listen(i,e,t,r)}shouldReplay(i){return this.replay!==null&&i.startsWith(Hf)}},Gf=new b("");function $c(n="animations"){return Oo("NgAsyncAnimations"),Ft([{provide:pe,useFactory:(i,e,t)=>new $f(i,e,t,n),deps:[N,Wi,V]},{provide:he,useValue:n==="noop"?"NoopAnimations":"BrowserAnimations"}])}var Qc=(()=>{class n{_renderer;_elementRef;onChange=e=>{};onTouched=()=>{};constructor(e,t){this._renderer=e,this._elementRef=t}setProperty(e,t){this._renderer.setProperty(this._elementRef.nativeElement,e,t)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static \u0275fac=function(t){return new(t||n)(G(Se),G(H))};static \u0275dir=I({type:n})}return n})(),Wf=(()=>{class n extends Qc{static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275dir=I({type:n,features:[we]})}return n})(),Jc=new b("");var qf={provide:Jc,useExisting:mt(()=>wi),multi:!0};function Yf(){let n=ze()?ze().getUserAgent():"";return/android (\d+)/.test(n.toLowerCase())}var Zf=new b(""),wi=(()=>{class n extends Qc{_compositionMode;_composing=!1;constructor(e,t,r){super(e,t),this._compositionMode=r,this._compositionMode==null&&(this._compositionMode=!Yf())}writeValue(e){let t=e??"";this.setProperty("value",t)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static \u0275fac=function(t){return new(t||n)(G(Se),G(H),G(Zf,8))};static \u0275dir=I({type:n,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(t,r){t&1&&re("input",function(a){return r._handleInput(a.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(a){return r._compositionEnd(a.target.value)})},standalone:!1,features:[fe([qf]),we]})}return n})();function Ga(n){return n==null||Wa(n)===0}function Wa(n){return n==null?null:Array.isArray(n)||typeof n=="string"?n.length:n instanceof Set?n.size:null}var bn=new b(""),qa=new b(""),Kf=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,W=class{static min(i){return Xf(i)}static max(i){return Qf(i)}static required(i){return ed(i)}static requiredTrue(i){return Jf(i)}static email(i){return ep(i)}static minLength(i){return tp(i)}static maxLength(i){return td(i)}static pattern(i){return ip(i)}static nullValidator(i){return Pr()}static compose(i){return sd(i)}static composeAsync(i){return ld(i)}};function Xf(n){return i=>{if(i.value==null||n==null)return null;let e=parseFloat(i.value);return!isNaN(e)&&e<n?{min:{min:n,actual:i.value}}:null}}function Qf(n){return i=>{if(i.value==null||n==null)return null;let e=parseFloat(i.value);return!isNaN(e)&&e>n?{max:{max:n,actual:i.value}}:null}}function ed(n){return Ga(n.value)?{required:!0}:null}function Jf(n){return n.value===!0?null:{required:!0}}function ep(n){return Ga(n.value)||Kf.test(n.value)?null:{email:!0}}function tp(n){return i=>{let e=i.value?.length??Wa(i.value);return e===null||e===0?null:e<n?{minlength:{requiredLength:n,actualLength:e}}:null}}function td(n){return i=>{let e=i.value?.length??Wa(i.value);return e!==null&&e>n?{maxlength:{requiredLength:n,actualLength:e}}:null}}function ip(n){if(!n)return Pr;let i,e;return typeof n=="string"?(e="",n.charAt(0)!=="^"&&(e+="^"),e+=n,n.charAt(n.length-1)!=="$"&&(e+="$"),i=new RegExp(e)):(e=n.toString(),i=n),t=>{if(Ga(t.value))return null;let r=t.value;return i.test(r)?null:{pattern:{requiredPattern:e,actualValue:r}}}}function Pr(n){return null}function id(n){return n!=null}function nd(n){return Bi(n)?be(n):n}function rd(n){let i={};return n.forEach(e=>{i=e!=null?_(_({},i),e):i}),Object.keys(i).length===0?null:i}function od(n,i){return i.map(e=>e(n))}function np(n){return!n.validate}function ad(n){return n.map(i=>np(i)?i:e=>i.validate(e))}function sd(n){if(!n)return null;let i=n.filter(id);return i.length==0?null:function(e){return rd(od(e,i))}}function Ya(n){return n!=null?sd(ad(n)):null}function ld(n){if(!n)return null;let i=n.filter(id);return i.length==0?null:function(e){let t=od(e,i).map(nd);return zn(t).pipe(M(rd))}}function Za(n){return n!=null?ld(ad(n)):null}function Gc(n,i){return n===null?[i]:Array.isArray(n)?[...n,i]:[n,i]}function cd(n){return n._rawValidators}function dd(n){return n._rawAsyncValidators}function za(n){return n?Array.isArray(n)?n:[n]:[]}function Nr(n,i){return Array.isArray(n)?n.includes(i):n===i}function Wc(n,i){let e=za(i);return za(n).forEach(r=>{Nr(e,r)||e.push(r)}),e}function qc(n,i){return za(i).filter(e=>!Nr(n,e))}var Lr=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(i){this._rawValidators=i||[],this._composedValidatorFn=Ya(this._rawValidators)}_setAsyncValidators(i){this._rawAsyncValidators=i||[],this._composedAsyncValidatorFn=Za(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(i){this._onDestroyCallbacks.push(i)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(i=>i()),this._onDestroyCallbacks=[]}reset(i=void 0){this.control&&this.control.reset(i)}hasError(i,e){return this.control?this.control.hasError(i,e):!1}getError(i,e){return this.control?this.control.getError(i,e):null}},xt=class extends Lr{name;get formDirective(){return null}get path(){return null}},rt=class extends Lr{_parent=null;name=null;valueAccessor=null},Br=class{_cd;constructor(i){this._cd=i}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},rp={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},lw=ee(_({},rp),{"[class.ng-submitted]":"isSubmitted"}),$r=(()=>{class n extends Br{constructor(e){super(e)}static \u0275fac=function(t){return new(t||n)(G(rt,2))};static \u0275dir=I({type:n,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(t,r){t&2&&$("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},standalone:!1,features:[we]})}return n})(),Gr=(()=>{class n extends Br{constructor(e){super(e)}static \u0275fac=function(t){return new(t||n)(G(xt,10))};static \u0275dir=I({type:n,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(t,r){t&2&&$("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)("ng-submitted",r.isSubmitted)},standalone:!1,features:[we]})}return n})();var fn="VALID",Fr="INVALID",vi="PENDING",pn="DISABLED",Et=class{},Vr=class extends Et{value;source;constructor(i,e){super(),this.value=i,this.source=e}},_n=class extends Et{pristine;source;constructor(i,e){super(),this.pristine=i,this.source=e}},vn=class extends Et{touched;source;constructor(i,e){super(),this.touched=i,this.source=e}},bi=class extends Et{status;source;constructor(i,e){super(),this.status=i,this.source=e}},jr=class extends Et{source;constructor(i){super(),this.source=i}},zr=class extends Et{source;constructor(i){super(),this.source=i}};function Ka(n){return(Wr(n)?n.validators:n)||null}function op(n){return Array.isArray(n)?Ya(n):n||null}function Xa(n,i){return(Wr(i)?i.asyncValidators:n)||null}function ap(n){return Array.isArray(n)?Za(n):n||null}function Wr(n){return n!=null&&!Array.isArray(n)&&typeof n=="object"}function ud(n,i,e){let t=n.controls;if(!(i?Object.keys(t):t).length)throw new z(1e3,"");if(!t[e])throw new z(1001,"")}function md(n,i,e){n._forEachChild((t,r)=>{if(e[r]===void 0)throw new z(1002,"")})}var yi=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(i,e){this._assignValidators(i),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(i){this._rawValidators=this._composedValidatorFn=i}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(i){this._rawAsyncValidators=this._composedAsyncValidatorFn=i}get parent(){return this._parent}get status(){return je(this.statusReactive)}set status(i){je(()=>this.statusReactive.set(i))}_status=et(()=>this.statusReactive());statusReactive=ft(void 0);get valid(){return this.status===fn}get invalid(){return this.status===Fr}get pending(){return this.status==vi}get disabled(){return this.status===pn}get enabled(){return this.status!==pn}errors;get pristine(){return je(this.pristineReactive)}set pristine(i){je(()=>this.pristineReactive.set(i))}_pristine=et(()=>this.pristineReactive());pristineReactive=ft(!0);get dirty(){return!this.pristine}get touched(){return je(this.touchedReactive)}set touched(i){je(()=>this.touchedReactive.set(i))}_touched=et(()=>this.touchedReactive());touchedReactive=ft(!1);get untouched(){return!this.touched}_events=new E;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(i){this._assignValidators(i)}setAsyncValidators(i){this._assignAsyncValidators(i)}addValidators(i){this.setValidators(Wc(i,this._rawValidators))}addAsyncValidators(i){this.setAsyncValidators(Wc(i,this._rawAsyncValidators))}removeValidators(i){this.setValidators(qc(i,this._rawValidators))}removeAsyncValidators(i){this.setAsyncValidators(qc(i,this._rawAsyncValidators))}hasValidator(i){return Nr(this._rawValidators,i)}hasAsyncValidator(i){return Nr(this._rawAsyncValidators,i)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(i={}){let e=this.touched===!1;this.touched=!0;let t=i.sourceControl??this;this._parent&&!i.onlySelf&&this._parent.markAsTouched(ee(_({},i),{sourceControl:t})),e&&i.emitEvent!==!1&&this._events.next(new vn(!0,t))}markAllAsTouched(i={}){this.markAsTouched({onlySelf:!0,emitEvent:i.emitEvent,sourceControl:this}),this._forEachChild(e=>e.markAllAsTouched(i))}markAsUntouched(i={}){let e=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let t=i.sourceControl??this;this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0,emitEvent:i.emitEvent,sourceControl:t})}),this._parent&&!i.onlySelf&&this._parent._updateTouched(i,t),e&&i.emitEvent!==!1&&this._events.next(new vn(!1,t))}markAsDirty(i={}){let e=this.pristine===!0;this.pristine=!1;let t=i.sourceControl??this;this._parent&&!i.onlySelf&&this._parent.markAsDirty(ee(_({},i),{sourceControl:t})),e&&i.emitEvent!==!1&&this._events.next(new _n(!1,t))}markAsPristine(i={}){let e=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let t=i.sourceControl??this;this._forEachChild(r=>{r.markAsPristine({onlySelf:!0,emitEvent:i.emitEvent})}),this._parent&&!i.onlySelf&&this._parent._updatePristine(i,t),e&&i.emitEvent!==!1&&this._events.next(new _n(!0,t))}markAsPending(i={}){this.status=vi;let e=i.sourceControl??this;i.emitEvent!==!1&&(this._events.next(new bi(this.status,e)),this.statusChanges.emit(this.status)),this._parent&&!i.onlySelf&&this._parent.markAsPending(ee(_({},i),{sourceControl:e}))}disable(i={}){let e=this._parentMarkedDirty(i.onlySelf);this.status=pn,this.errors=null,this._forEachChild(r=>{r.disable(ee(_({},i),{onlySelf:!0}))}),this._updateValue();let t=i.sourceControl??this;i.emitEvent!==!1&&(this._events.next(new Vr(this.value,t)),this._events.next(new bi(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(ee(_({},i),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(r=>r(!0))}enable(i={}){let e=this._parentMarkedDirty(i.onlySelf);this.status=fn,this._forEachChild(t=>{t.enable(ee(_({},i),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:i.emitEvent}),this._updateAncestors(ee(_({},i),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(t=>t(!1))}_updateAncestors(i,e){this._parent&&!i.onlySelf&&(this._parent.updateValueAndValidity(i),i.skipPristineCheck||this._parent._updatePristine({},e),this._parent._updateTouched({},e))}setParent(i){this._parent=i}getRawValue(){return this.value}updateValueAndValidity(i={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let t=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===fn||this.status===vi)&&this._runAsyncValidator(t,i.emitEvent)}let e=i.sourceControl??this;i.emitEvent!==!1&&(this._events.next(new Vr(this.value,e)),this._events.next(new bi(this.status,e)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!i.onlySelf&&this._parent.updateValueAndValidity(ee(_({},i),{sourceControl:e}))}_updateTreeValidity(i={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(i)),this.updateValueAndValidity({onlySelf:!0,emitEvent:i.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?pn:fn}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(i,e){if(this.asyncValidator){this.status=vi,this._hasOwnPendingAsyncValidator={emitEvent:e!==!1};let t=nd(this.asyncValidator(this));this._asyncValidationSubscription=t.subscribe(r=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(r,{emitEvent:e,shouldHaveEmitted:i})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let i=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,i}return!1}setErrors(i,e={}){this.errors=i,this._updateControlsErrors(e.emitEvent!==!1,this,e.shouldHaveEmitted)}get(i){let e=i;return e==null||(Array.isArray(e)||(e=e.split(".")),e.length===0)?null:e.reduce((t,r)=>t&&t._find(r),this)}getError(i,e){let t=e?this.get(e):this;return t&&t.errors?t.errors[i]:null}hasError(i,e){return!!this.getError(i,e)}get root(){let i=this;for(;i._parent;)i=i._parent;return i}_updateControlsErrors(i,e,t){this.status=this._calculateStatus(),i&&this.statusChanges.emit(this.status),(i||t)&&this._events.next(new bi(this.status,e)),this._parent&&this._parent._updateControlsErrors(i,e,t)}_initObservables(){this.valueChanges=new Y,this.statusChanges=new Y}_calculateStatus(){return this._allControlsDisabled()?pn:this.errors?Fr:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(vi)?vi:this._anyControlsHaveStatus(Fr)?Fr:fn}_anyControlsHaveStatus(i){return this._anyControls(e=>e.status===i)}_anyControlsDirty(){return this._anyControls(i=>i.dirty)}_anyControlsTouched(){return this._anyControls(i=>i.touched)}_updatePristine(i,e){let t=!this._anyControlsDirty(),r=this.pristine!==t;this.pristine=t,this._parent&&!i.onlySelf&&this._parent._updatePristine(i,e),r&&this._events.next(new _n(this.pristine,e))}_updateTouched(i={},e){this.touched=this._anyControlsTouched(),this._events.next(new vn(this.touched,e)),this._parent&&!i.onlySelf&&this._parent._updateTouched(i,e)}_onDisabledChange=[];_registerOnCollectionChange(i){this._onCollectionChange=i}_setUpdateStrategy(i){Wr(i)&&i.updateOn!=null&&(this._updateOn=i.updateOn)}_parentMarkedDirty(i){let e=this._parent&&this._parent.dirty;return!i&&!!e&&!this._parent._anyControlsDirty()}_find(i){return null}_assignValidators(i){this._rawValidators=Array.isArray(i)?i.slice():i,this._composedValidatorFn=op(this._rawValidators)}_assignAsyncValidators(i){this._rawAsyncValidators=Array.isArray(i)?i.slice():i,this._composedAsyncValidatorFn=ap(this._rawAsyncValidators)}},Ci=class extends yi{constructor(i,e,t){super(Ka(e),Xa(t,e)),this.controls=i,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(i,e){return this.controls[i]?this.controls[i]:(this.controls[i]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(i,e,t={}){this.registerControl(i,e),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}removeControl(i,e={}){this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),delete this.controls[i],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(i,e,t={}){this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),delete this.controls[i],e&&this.registerControl(i,e),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}contains(i){return this.controls.hasOwnProperty(i)&&this.controls[i].enabled}setValue(i,e={}){md(this,!0,i),Object.keys(i).forEach(t=>{ud(this,!0,t),this.controls[t].setValue(i[t],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(i,e={}){i!=null&&(Object.keys(i).forEach(t=>{let r=this.controls[t];r&&r.patchValue(i[t],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(i={},e={}){this._forEachChild((t,r)=>{t.reset(i?i[r]:null,{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(i,e,t)=>(i[t]=e.getRawValue(),i))}_syncPendingControls(){let i=this._reduceChildren(!1,(e,t)=>t._syncPendingControls()?!0:e);return i&&this.updateValueAndValidity({onlySelf:!0}),i}_forEachChild(i){Object.keys(this.controls).forEach(e=>{let t=this.controls[e];t&&i(t,e)})}_setUpControls(){this._forEachChild(i=>{i.setParent(this),i._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(i){for(let[e,t]of Object.entries(this.controls))if(this.contains(e)&&i(t))return!0;return!1}_reduceValue(){let i={};return this._reduceChildren(i,(e,t,r)=>((t.enabled||this.disabled)&&(e[r]=t.value),e))}_reduceChildren(i,e){let t=i;return this._forEachChild((r,o)=>{t=e(t,r,o)}),t}_allControlsDisabled(){for(let i of Object.keys(this.controls))if(this.controls[i].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(i){return this.controls.hasOwnProperty(i)?this.controls[i]:null}};var Ua=class extends Ci{};var Qa=new b("",{providedIn:"root",factory:()=>Ja}),Ja="always";function sp(n,i){return[...i.path,n]}function Ha(n,i,e=Ja){es(n,i),i.valueAccessor.writeValue(n.value),(n.disabled||e==="always")&&i.valueAccessor.setDisabledState?.(n.disabled),cp(n,i),up(n,i),dp(n,i),lp(n,i)}function Yc(n,i,e=!0){let t=()=>{};i.valueAccessor&&(i.valueAccessor.registerOnChange(t),i.valueAccessor.registerOnTouched(t)),Hr(n,i),n&&(i._invokeOnDestroyCallbacks(),n._registerOnCollectionChange(()=>{}))}function Ur(n,i){n.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(i)})}function lp(n,i){if(i.valueAccessor.setDisabledState){let e=t=>{i.valueAccessor.setDisabledState(t)};n.registerOnDisabledChange(e),i._registerOnDestroy(()=>{n._unregisterOnDisabledChange(e)})}}function es(n,i){let e=cd(n);i.validator!==null?n.setValidators(Gc(e,i.validator)):typeof e=="function"&&n.setValidators([e]);let t=dd(n);i.asyncValidator!==null?n.setAsyncValidators(Gc(t,i.asyncValidator)):typeof t=="function"&&n.setAsyncValidators([t]);let r=()=>n.updateValueAndValidity();Ur(i._rawValidators,r),Ur(i._rawAsyncValidators,r)}function Hr(n,i){let e=!1;if(n!==null){if(i.validator!==null){let r=cd(n);if(Array.isArray(r)&&r.length>0){let o=r.filter(a=>a!==i.validator);o.length!==r.length&&(e=!0,n.setValidators(o))}}if(i.asyncValidator!==null){let r=dd(n);if(Array.isArray(r)&&r.length>0){let o=r.filter(a=>a!==i.asyncValidator);o.length!==r.length&&(e=!0,n.setAsyncValidators(o))}}}let t=()=>{};return Ur(i._rawValidators,t),Ur(i._rawAsyncValidators,t),e}function cp(n,i){i.valueAccessor.registerOnChange(e=>{n._pendingValue=e,n._pendingChange=!0,n._pendingDirty=!0,n.updateOn==="change"&&hd(n,i)})}function dp(n,i){i.valueAccessor.registerOnTouched(()=>{n._pendingTouched=!0,n.updateOn==="blur"&&n._pendingChange&&hd(n,i),n.updateOn!=="submit"&&n.markAsTouched()})}function hd(n,i){n._pendingDirty&&n.markAsDirty(),n.setValue(n._pendingValue,{emitModelToViewChange:!1}),i.viewToModelUpdate(n._pendingValue),n._pendingChange=!1}function up(n,i){let e=(t,r)=>{i.valueAccessor.writeValue(t),r&&i.viewToModelUpdate(t)};n.registerOnChange(e),i._registerOnDestroy(()=>{n._unregisterOnChange(e)})}function fd(n,i){n==null,es(n,i)}function mp(n,i){return Hr(n,i)}function hp(n,i){if(!n.hasOwnProperty("model"))return!1;let e=n.model;return e.isFirstChange()?!0:!Object.is(i,e.currentValue)}function fp(n){return Object.getPrototypeOf(n.constructor)===Wf}function pd(n,i){n._syncPendingControls(),i.forEach(e=>{let t=e.control;t.updateOn==="submit"&&t._pendingChange&&(e.viewToModelUpdate(t._pendingValue),t._pendingChange=!1)})}function pp(n,i){if(!i)return null;Array.isArray(i);let e,t,r;return i.forEach(o=>{o.constructor===wi?e=o:fp(o)?t=o:r=o}),r||t||e||null}function gp(n,i){let e=n.indexOf(i);e>-1&&n.splice(e,1)}var _p={provide:xt,useExisting:mt(()=>yn)},gn=Promise.resolve(),yn=(()=>{class n extends xt{callSetDisabledState;get submitted(){return je(this.submittedReactive)}_submitted=et(()=>this.submittedReactive());submittedReactive=ft(!1);_directives=new Set;form;ngSubmit=new Y;options;constructor(e,t,r){super(),this.callSetDisabledState=r,this.form=new Ci({},Ya(e),Za(t))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(e){gn.then(()=>{let t=this._findContainer(e.path);e.control=t.registerControl(e.name,e.control),Ha(e.control,e,this.callSetDisabledState),e.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(e)})}getControl(e){return this.form.get(e.path)}removeControl(e){gn.then(()=>{let t=this._findContainer(e.path);t&&t.removeControl(e.name),this._directives.delete(e)})}addFormGroup(e){gn.then(()=>{let t=this._findContainer(e.path),r=new Ci({});fd(r,e),t.registerControl(e.name,r),r.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(e){gn.then(()=>{let t=this._findContainer(e.path);t&&t.removeControl(e.name)})}getFormGroup(e){return this.form.get(e.path)}updateModel(e,t){gn.then(()=>{this.form.get(e.path).setValue(t)})}setValue(e){this.control.setValue(e)}onSubmit(e){return this.submittedReactive.set(!0),pd(this.form,this._directives),this.ngSubmit.emit(e),this.form._events.next(new jr(this.control)),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submittedReactive.set(!1),this.form._events.next(new zr(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(e){return e.pop(),e.length?this.form.get(e):this.form}static \u0275fac=function(t){return new(t||n)(G(bn,10),G(qa,10),G(Qa,8))};static \u0275dir=I({type:n,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(t,r){t&1&&re("submit",function(a){return r.onSubmit(a)})("reset",function(){return r.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[fe([_p]),we]})}return n})();function Zc(n,i){let e=n.indexOf(i);e>-1&&n.splice(e,1)}function Kc(n){return typeof n=="object"&&n!==null&&Object.keys(n).length===2&&"value"in n&&"disabled"in n}var Or=class extends yi{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(i=null,e,t){super(Ka(e),Xa(t,e)),this._applyFormState(i),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Wr(e)&&(e.nonNullable||e.initialValueIsDefault)&&(Kc(i)?this.defaultValue=i.value:this.defaultValue=i)}setValue(i,e={}){this.value=this._pendingValue=i,this._onChange.length&&e.emitModelToViewChange!==!1&&this._onChange.forEach(t=>t(this.value,e.emitViewToModelChange!==!1)),this.updateValueAndValidity(e)}patchValue(i,e={}){this.setValue(i,e)}reset(i=this.defaultValue,e={}){this._applyFormState(i),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(i){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(i){this._onChange.push(i)}_unregisterOnChange(i){Zc(this._onChange,i)}registerOnDisabledChange(i){this._onDisabledChange.push(i)}_unregisterOnDisabledChange(i){Zc(this._onDisabledChange,i)}_forEachChild(i){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(i){Kc(i)?(this.value=this._pendingValue=i.value,i.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=i}};var vp=n=>n instanceof Or;var qr=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return n})();var gd=new b("");var bp={provide:xt,useExisting:mt(()=>ot)},ot=(()=>{class n extends xt{callSetDisabledState;get submitted(){return je(this._submittedReactive)}set submitted(e){this._submittedReactive.set(e)}_submitted=et(()=>this._submittedReactive());_submittedReactive=ft(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new Y;constructor(e,t,r){super(),this.callSetDisabledState=r,this._setValidators(e),this._setAsyncValidators(t)}ngOnChanges(e){e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(Hr(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){let t=this.form.get(e.path);return Ha(t,e,this.callSetDisabledState),t.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),t}getControl(e){return this.form.get(e.path)}removeControl(e){Yc(e.control||null,e,!1),gp(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,t){this.form.get(e.path).setValue(t)}onSubmit(e){return this._submittedReactive.set(!0),pd(this.form,this.directives),this.ngSubmit.emit(e),this.form._events.next(new jr(this.control)),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this._submittedReactive.set(!1),this.form._events.next(new zr(this.form))}_updateDomValue(){this.directives.forEach(e=>{let t=e.control,r=this.form.get(e.path);t!==r&&(Yc(t||null,e),vp(r)&&(Ha(r,e,this.callSetDisabledState),e.control=r))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){let t=this.form.get(e.path);fd(t,e),t.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){let t=this.form.get(e.path);t&&mp(t,e)&&t.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){es(this.form,this),this._oldForm&&Hr(this._oldForm,this)}static \u0275fac=function(t){return new(t||n)(G(bn,10),G(qa,10),G(Qa,8))};static \u0275dir=I({type:n,selectors:[["","formGroup",""]],hostBindings:function(t,r){t&1&&re("submit",function(a){return r.onSubmit(a)})("reset",function(){return r.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[fe([bp]),we,Ce]})}return n})();var yp={provide:rt,useExisting:mt(()=>Cn)},Cn=(()=>{class n extends rt{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(e){}model;update=new Y;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(e,t,r,o,a){super(),this._ngModelWarningConfig=a,this._parent=e,this._setValidators(t),this._setAsyncValidators(r),this.valueAccessor=pp(this,o)}ngOnChanges(e){this._added||this._setUpControl(),hp(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return sp(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(t){return new(t||n)(G(xt,13),G(bn,10),G(qa,10),G(Jc,10),G(gd,8))};static \u0275dir=I({type:n,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[fe([yp]),we,Ce]})}return n})();function Cp(n){return typeof n=="number"?n:parseInt(n,10)}var _d=(()=>{class n{_validator=Pr;_onChange;_enabled;ngOnChanges(e){if(this.inputName in e){let t=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(t),this._validator=this._enabled?this.createValidator(t):Pr,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return e!=null}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,features:[Ce]})}return n})();var wp={provide:bn,useExisting:mt(()=>wn),multi:!0};var wn=(()=>{class n extends _d{required;inputName="required";normalizeInput=q;createValidator=e=>ed;enabled(e){return e}static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275dir=I({type:n,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(t,r){t&2&&ne("required",r._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[fe([wp]),we]})}return n})();var Dp={provide:bn,useExisting:mt(()=>Dn),multi:!0},Dn=(()=>{class n extends _d{maxlength;inputName="maxlength";normalizeInput=e=>Cp(e);createValidator=e=>td(e);static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275dir=I({type:n,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(t,r){t&2&&ne("maxlength",r._enabled?r.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[fe([Dp]),we]})}return n})();var xp=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({})}return n})(),$a=class extends yi{constructor(i,e,t){super(Ka(e),Xa(t,e)),this.controls=i,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(i){return this.controls[this._adjustIndex(i)]}push(i,e={}){this.controls.push(i),this._registerControl(i),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}insert(i,e,t={}){this.controls.splice(i,0,e),this._registerControl(e),this.updateValueAndValidity({emitEvent:t.emitEvent})}removeAt(i,e={}){let t=this._adjustIndex(i);t<0&&(t=0),this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),this.controls.splice(t,1),this.updateValueAndValidity({emitEvent:e.emitEvent})}setControl(i,e,t={}){let r=this._adjustIndex(i);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),e&&(this.controls.splice(r,0,e),this._registerControl(e)),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(i,e={}){md(this,!1,i),i.forEach((t,r)=>{ud(this,!1,r),this.at(r).setValue(t,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(i,e={}){i!=null&&(i.forEach((t,r)=>{this.at(r)&&this.at(r).patchValue(t,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(i=[],e={}){this._forEachChild((t,r)=>{t.reset(i[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this.controls.map(i=>i.getRawValue())}clear(i={}){this.controls.length<1||(this._forEachChild(e=>e._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:i.emitEvent}))}_adjustIndex(i){return i<0?i+this.length:i}_syncPendingControls(){let i=this.controls.reduce((e,t)=>t._syncPendingControls()?!0:e,!1);return i&&this.updateValueAndValidity({onlySelf:!0}),i}_forEachChild(i){this.controls.forEach((e,t)=>{i(e,t)})}_updateValue(){this.value=this.controls.filter(i=>i.enabled||this.disabled).map(i=>i.value)}_anyControls(i){return this.controls.some(e=>e.enabled&&i(e))}_setUpControls(){this._forEachChild(i=>this._registerControl(i))}_allControlsDisabled(){for(let i of this.controls)if(i.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(i){i.setParent(this),i._registerOnCollectionChange(this._onCollectionChange)}_find(i){return this.at(i)??null}};function Xc(n){return!!n&&(n.asyncValidators!==void 0||n.validators!==void 0||n.updateOn!==void 0)}var Yr=(()=>{class n{useNonNullable=!1;get nonNullable(){let e=new n;return e.useNonNullable=!0,e}group(e,t=null){let r=this._reduceControls(e),o={};return Xc(t)?o=t:t!==null&&(o.validators=t.validator,o.asyncValidators=t.asyncValidator),new Ci(r,o)}record(e,t=null){let r=this._reduceControls(e);return new Ua(r,t)}control(e,t,r){let o={};return this.useNonNullable?(Xc(t)?o=t:(o.validators=t,o.asyncValidators=r),new Or(e,ee(_({},o),{nonNullable:!0}))):new Or(e,t,r)}array(e,t,r){let o=e.map(a=>this._createControl(a));return new $a(o,t,r)}_reduceControls(e){let t={};return Object.keys(e).forEach(r=>{t[r]=this._createControl(e[r])}),t}_createControl(e){if(e instanceof Or)return e;if(e instanceof yi)return e;if(Array.isArray(e)){let t=e[0],r=e.length>1?e[1]:null,o=e.length>2?e[2]:null;return this.control(t,r,o)}else return this.control(e)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Zr=(()=>{class n{static withConfig(e){return{ngModule:n,providers:[{provide:gd,useValue:e.warnOnNgModelWithFormControl??"always"},{provide:Qa,useValue:e.callSetDisabledState??Ja}]}}static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[xp]})}return n})();function xn(n){return n.buttons===0||n.detail===0}function En(n){let i=n.touches&&n.touches[0]||n.changedTouches&&n.changedTouches[0];return!!i&&i.identifier===-1&&(i.radiusX==null||i.radiusX===1)&&(i.radiusY==null||i.radiusY===1)}var ts;function bd(){if(ts==null){let n=typeof document<"u"?document.head:null;ts=!!(n&&(n.createShadowRoot||n.attachShadow))}return ts}function is(n){if(bd()){let i=n.getRootNode?n.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&i instanceof ShadowRoot)return i}return null}function De(n){return n.composedPath?n.composedPath()[0]:n.target}function Me(n,i,e,t,r){let o=parseInt(Bo.major),a=parseInt(Bo.minor);return o>19||o===19&&a>0||o===0&&a===0?n.listen(i,e,t,r):(i.addEventListener(e,t,r),()=>{i.removeEventListener(e,t,r)})}var ns;try{ns=typeof Intl<"u"&&Intl.v8BreakIterator}catch{ns=!1}var ie=(()=>{class n{_platformId=l(Pt);isBrowser=this._platformId?Go(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||ns)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Sn;function yd(){if(Sn==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>Sn=!0}))}finally{Sn=Sn||!1}return Sn}function Di(n){return yd()?n:!!n.capture}function ke(n){return n instanceof H?n.nativeElement:n}var Cd=new b("cdk-input-modality-detector-options"),wd={ignoreKeys:[18,17,224,91,16]},Dd=650,rs={passive:!0,capture:!0},xd=(()=>{class n{_platform=l(ie);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new xe(null);_options;_lastTouchMs=0;_onKeydown=e=>{this._options?.ignoreKeys?.some(t=>t===e.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=De(e))};_onMousedown=e=>{Date.now()-this._lastTouchMs<Dd||(this._modality.next(xn(e)?"keyboard":"mouse"),this._mostRecentTarget=De(e))};_onTouchstart=e=>{if(En(e)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=De(e)};constructor(){let e=l(V),t=l(N),r=l(Cd,{optional:!0});if(this._options=_(_({},wd),r),this.modalityDetected=this._modality.pipe(Hn(1)),this.modalityChanged=this.modalityDetected.pipe(Mo()),this._platform.isBrowser){let o=l(pe).createRenderer(null,null);this._listenerCleanups=e.runOutsideAngular(()=>[Me(o,t,"keydown",this._onKeydown,rs),Me(o,t,"mousedown",this._onMousedown,rs),Me(o,t,"touchstart",this._onTouchstart,rs)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(e=>e())}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Mn=function(n){return n[n.IMMEDIATE=0]="IMMEDIATE",n[n.EVENTUAL=1]="EVENTUAL",n}(Mn||{}),Ed=new b("cdk-focus-monitor-default-options"),Kr=Di({passive:!0,capture:!0}),os=(()=>{class n{_ngZone=l(V);_platform=l(ie);_inputModalityDetector=l(xd);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=l(N,{optional:!0});_stopInputModalityDetector=new E;constructor(){let e=l(Ed,{optional:!0});this._detectionMode=e?.detectionMode||Mn.IMMEDIATE}_rootNodeFocusAndBlurListener=e=>{let t=De(e);for(let r=t;r;r=r.parentElement)e.type==="focus"?this._onFocus(e,r):this._onBlur(e,r)};monitor(e,t=!1){let r=ke(e);if(!this._platform.isBrowser||r.nodeType!==1)return D();let o=is(r)||this._getDocument(),a=this._elementInfo.get(r);if(a)return t&&(a.checkChildren=!0),a.subject;let s={checkChildren:t,subject:new E,rootNode:o};return this._elementInfo.set(r,s),this._registerGlobalListeners(s),s.subject}stopMonitoring(e){let t=ke(e),r=this._elementInfo.get(t);r&&(r.subject.complete(),this._setClasses(t),this._elementInfo.delete(t),this._removeGlobalListeners(r))}focusVia(e,t,r){let o=ke(e),a=this._getDocument().activeElement;o===a?this._getClosestElementsInfo(o).forEach(([s,c])=>this._originChanged(s,t,c)):(this._setOrigin(t),typeof o.focus=="function"&&o.focus(r))}ngOnDestroy(){this._elementInfo.forEach((e,t)=>this.stopMonitoring(t))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(e){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(e)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:e&&this._isLastInteractionFromInputLabel(e)?"mouse":"program"}_shouldBeAttributedToTouch(e){return this._detectionMode===Mn.EVENTUAL||!!e?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(e,t){e.classList.toggle("cdk-focused",!!t),e.classList.toggle("cdk-touch-focused",t==="touch"),e.classList.toggle("cdk-keyboard-focused",t==="keyboard"),e.classList.toggle("cdk-mouse-focused",t==="mouse"),e.classList.toggle("cdk-program-focused",t==="program")}_setOrigin(e,t=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=e,this._originFromTouchInteraction=e==="touch"&&t,this._detectionMode===Mn.IMMEDIATE){clearTimeout(this._originTimeoutId);let r=this._originFromTouchInteraction?Dd:1;this._originTimeoutId=setTimeout(()=>this._origin=null,r)}})}_onFocus(e,t){let r=this._elementInfo.get(t),o=De(e);!r||!r.checkChildren&&t!==o||this._originChanged(t,this._getFocusOrigin(o),r)}_onBlur(e,t){let r=this._elementInfo.get(t);!r||r.checkChildren&&e.relatedTarget instanceof Node&&t.contains(e.relatedTarget)||(this._setClasses(t),this._emitOrigin(r,null))}_emitOrigin(e,t){e.subject.observers.length&&this._ngZone.run(()=>e.subject.next(t))}_registerGlobalListeners(e){if(!this._platform.isBrowser)return;let t=e.rootNode,r=this._rootNodeFocusListenerCount.get(t)||0;r||this._ngZone.runOutsideAngular(()=>{t.addEventListener("focus",this._rootNodeFocusAndBlurListener,Kr),t.addEventListener("blur",this._rootNodeFocusAndBlurListener,Kr)}),this._rootNodeFocusListenerCount.set(t,r+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(de(this._stopInputModalityDetector)).subscribe(o=>{this._setOrigin(o,!0)}))}_removeGlobalListeners(e){let t=e.rootNode;if(this._rootNodeFocusListenerCount.has(t)){let r=this._rootNodeFocusListenerCount.get(t);r>1?this._rootNodeFocusListenerCount.set(t,r-1):(t.removeEventListener("focus",this._rootNodeFocusAndBlurListener,Kr),t.removeEventListener("blur",this._rootNodeFocusAndBlurListener,Kr),this._rootNodeFocusListenerCount.delete(t))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(e,t,r){this._setClasses(e,t),this._emitOrigin(r,t),this._lastFocusOrigin=t}_getClosestElementsInfo(e){let t=[];return this._elementInfo.forEach((r,o)=>{(o===e||r.checkChildren&&o.contains(e))&&t.push([o,r])}),t}_isLastInteractionFromInputLabel(e){let{_mostRecentTarget:t,mostRecentModality:r}=this._inputModalityDetector;if(r!=="mouse"||!t||t===e||e.nodeName!=="INPUT"&&e.nodeName!=="TEXTAREA"||e.disabled)return!1;let o=e.labels;if(o){for(let a=0;a<o.length;a++)if(o[a].contains(t))return!0}return!1}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Xr=new WeakMap,He=(()=>{class n{_appRef;_injector=l(se);_environmentInjector=l(Ne);load(e){let t=this._appRef=this._appRef||this._injector.get(Lt),r=Xr.get(t);r||(r={loaders:new Set,refs:[]},Xr.set(t,r),t.onDestroy(()=>{Xr.get(t)?.refs.forEach(o=>o.destroy()),Xr.delete(t)})),r.loaders.has(e)||(r.loaders.add(e),r.refs.push(Kn(e,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Sd=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(t,r){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return n})();function xi(n){return Array.isArray(n)?n:[n]}var Md=new Set,Zt,Ep=(()=>{class n{_platform=l(ie);_nonce=l(Pi,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):Mp}matchMedia(e){return(this._platform.WEBKIT||this._platform.BLINK)&&Sp(e,this._nonce),this._matchMedia(e)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Sp(n,i){if(!Md.has(n))try{Zt||(Zt=document.createElement("style"),i&&Zt.setAttribute("nonce",i),Zt.setAttribute("type","text/css"),document.head.appendChild(Zt)),Zt.sheet&&(Zt.sheet.insertRule(`@media ${n} {body{ }}`,0),Md.add(n))}catch(e){console.error(e)}}function Mp(n){return{matches:n==="all"||n==="",media:n,addListener:()=>{},removeListener:()=>{}}}var Id=(()=>{class n{_mediaMatcher=l(Ep);_zone=l(V);_queries=new Map;_destroySubject=new E;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(e){return Ad(xi(e)).some(r=>this._registerQuery(r).mql.matches)}observe(e){let r=Ad(xi(e)).map(a=>this._registerQuery(a).observable),o=Ti(r);return o=jn(o.pipe(Pe(1)),o.pipe(Hn(1),Un(0))),o.pipe(M(a=>{let s={matches:!1,breakpoints:{}};return a.forEach(({matches:c,query:m})=>{s.matches=s.matches||c,s.breakpoints[m]=c}),s}))}_registerQuery(e){if(this._queries.has(e))return this._queries.get(e);let t=this._mediaMatcher.matchMedia(e),o={observable:new st(a=>{let s=c=>this._zone.run(()=>a.next(c));return t.addListener(s),()=>{t.removeListener(s)}}).pipe(Ze(t),M(({matches:a})=>({query:e,matches:a})),de(this._destroySubject)),mql:t};return this._queries.set(e,o),o}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Ad(n){return n.map(i=>i.split(",")).reduce((i,e)=>i.concat(e)).map(i=>i.trim())}var Ap=(()=>{class n{create(e){return typeof MutationObserver>"u"?null:new MutationObserver(e)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Rd=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({providers:[Ap]})}return n})();var Fd=new b("liveAnnouncerElement",{providedIn:"root",factory:Od});function Od(){return null}var Pd=new b("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),Ip=0,ss=(()=>{class n{_ngZone=l(V);_defaultOptions=l(Pd,{optional:!0});_liveElement;_document=l(N);_previousTimeout;_currentPromise;_currentResolve;constructor(){let e=l(Fd,{optional:!0});this._liveElement=e||this._createLiveElement()}announce(e,...t){let r=this._defaultOptions,o,a;return t.length===1&&typeof t[0]=="number"?a=t[0]:[o,a]=t,this.clear(),clearTimeout(this._previousTimeout),o||(o=r&&r.politeness?r.politeness:"polite"),a==null&&r&&(a=r.duration),this._liveElement.setAttribute("aria-live",o),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(s=>this._currentResolve=s)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=e,typeof a=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),a)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let e="cdk-live-announcer-element",t=this._document.getElementsByClassName(e),r=this._document.createElement("div");for(let o=0;o<t.length;o++)t[o].remove();return r.classList.add(e),r.classList.add("cdk-visually-hidden"),r.setAttribute("aria-atomic","true"),r.setAttribute("aria-live","polite"),r.id=`cdk-live-announcer-${Ip++}`,this._document.body.appendChild(r),r}_exposeAnnouncerToModals(e){let t=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let r=0;r<t.length;r++){let o=t[r],a=o.getAttribute("aria-owns");a?a.indexOf(e)===-1&&o.setAttribute("aria-owns",a+" "+e):o.setAttribute("aria-owns",e)}}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var St=function(n){return n[n.NONE=0]="NONE",n[n.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",n[n.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",n}(St||{}),Td="cdk-high-contrast-black-on-white",kd="cdk-high-contrast-white-on-black",as="cdk-high-contrast-active",ls=(()=>{class n{_platform=l(ie);_hasCheckedHighContrastMode;_document=l(N);_breakpointSubscription;constructor(){this._breakpointSubscription=l(Id).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return St.NONE;let e=this._document.createElement("div");e.style.backgroundColor="rgb(1,2,3)",e.style.position="absolute",this._document.body.appendChild(e);let t=this._document.defaultView||window,r=t&&t.getComputedStyle?t.getComputedStyle(e):null,o=(r&&r.backgroundColor||"").replace(/ /g,"");switch(e.remove(),o){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return St.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return St.BLACK_ON_WHITE}return St.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let e=this._document.body.classList;e.remove(as,Td,kd),this._hasCheckedHighContrastMode=!0;let t=this.getHighContrastMode();t===St.BLACK_ON_WHITE?e.add(as,Td):t===St.WHITE_ON_BLACK&&e.add(as,kd)}}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var cs={},Fe=(()=>{class n{_appId=l(Oi);getId(e){return this._appId!=="ng"&&(e+=this._appId),cs.hasOwnProperty(e)||(cs[e]=0),`${e}${cs[e]++}`}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Rp=200,Qr=class{_letterKeyStream=new E;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new E;selectedItem=this._selectedItem;constructor(i,e){let t=typeof e?.debounceInterval=="number"?e.debounceInterval:Rp;e?.skipPredicate&&(this._skipPredicateFn=e.skipPredicate),this.setItems(i),this._setupKeyHandler(t)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(i){this._selectedItemIndex=i}setItems(i){this._items=i}handleKey(i){let e=i.keyCode;i.key&&i.key.length===1?this._letterKeyStream.next(i.key.toLocaleUpperCase()):(e>=65&&e<=90||e>=48&&e<=57)&&this._letterKeyStream.next(String.fromCharCode(e))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(i){this._letterKeyStream.pipe(oe(e=>this._pressedLetters.push(e)),Un(i),ae(()=>this._pressedLetters.length>0),M(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(e=>{for(let t=1;t<this._items.length+1;t++){let r=(this._selectedItemIndex+t)%this._items.length,o=this._items[r];if(!this._skipPredicateFn?.(o)&&o.getLabel?.().toLocaleUpperCase().trim().indexOf(e)===0){this._selectedItem.next(o);break}}this._pressedLetters=[]})}};function Qe(n,...i){return i.length?i.some(e=>n[e]):n.altKey||n.shiftKey||n.ctrlKey||n.metaKey}var Jr=class{_items;_activeItemIndex=-1;_activeItem=ft(null);_wrap=!1;_typeaheadSubscription=ve.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=i=>i.disabled;constructor(i,e){this._items=i,i instanceof Fo?this._itemChangesSubscription=i.changes.subscribe(t=>this._itemsChanged(t.toArray())):Ot(i)&&(this._effectRef=Zn(()=>this._itemsChanged(i()),{injector:e}))}tabOut=new E;change=new E;skipPredicate(i){return this._skipPredicateFn=i,this}withWrap(i=!0){return this._wrap=i,this}withVerticalOrientation(i=!0){return this._vertical=i,this}withHorizontalOrientation(i){return this._horizontal=i,this}withAllowedModifierKeys(i){return this._allowedModifierKeys=i,this}withTypeAhead(i=200){this._typeaheadSubscription.unsubscribe();let e=this._getItemsArray();return this._typeahead=new Qr(e,{debounceInterval:typeof i=="number"?i:void 0,skipPredicate:t=>this._skipPredicateFn(t)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(t=>{this.setActiveItem(t)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(i=!0){return this._homeAndEnd=i,this}withPageUpDown(i=!0,e=10){return this._pageUpAndDown={enabled:i,delta:e},this}setActiveItem(i){let e=this._activeItem();this.updateActiveItem(i),this._activeItem()!==e&&this.change.next(this._activeItemIndex)}onKeydown(i){let e=i.keyCode,r=["altKey","ctrlKey","metaKey","shiftKey"].every(o=>!i[o]||this._allowedModifierKeys.indexOf(o)>-1);switch(e){case 9:this.tabOut.next();return;case 40:if(this._vertical&&r){this.setNextItemActive();break}else return;case 38:if(this._vertical&&r){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&r){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&r){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&r){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&r){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&r){let o=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(o>0?o:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&r){let o=this._activeItemIndex+this._pageUpAndDown.delta,a=this._getItemsArray().length;this._setActiveItemByIndex(o<a?o:a-1,-1);break}else return;default:(r||Qe(i,"shiftKey"))&&this._typeahead?.handleKey(i);return}this._typeahead?.reset(),i.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(i){let e=this._getItemsArray(),t=typeof i=="number"?i:e.indexOf(i),r=e[t];this._activeItem.set(r??null),this._activeItemIndex=t,this._typeahead?.setCurrentSelectedItemIndex(t)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(i){this._wrap?this._setActiveInWrapMode(i):this._setActiveInDefaultMode(i)}_setActiveInWrapMode(i){let e=this._getItemsArray();for(let t=1;t<=e.length;t++){let r=(this._activeItemIndex+i*t+e.length)%e.length,o=e[r];if(!this._skipPredicateFn(o)){this.setActiveItem(r);return}}}_setActiveInDefaultMode(i){this._setActiveItemByIndex(this._activeItemIndex+i,i)}_setActiveItemByIndex(i,e){let t=this._getItemsArray();if(t[i]){for(;this._skipPredicateFn(t[i]);)if(i+=e,!t[i])return;this.setActiveItem(i)}}_getItemsArray(){return Ot(this._items)?this._items():this._items instanceof Fo?this._items.toArray():this._items}_itemsChanged(i){this._typeahead?.setItems(i);let e=this._activeItem();if(e){let t=i.indexOf(e);t>-1&&t!==this._activeItemIndex&&(this._activeItemIndex=t,this._typeahead?.setCurrentSelectedItemIndex(t))}}};var An=class extends Jr{setActiveItem(i){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(i),this.activeItem&&this.activeItem.setActiveStyles()}};var Vd=" ";function jd(n,i,e){let t=zd(n,i);e=e.trim(),!t.some(r=>r.trim()===e)&&(t.push(e),n.setAttribute(i,t.join(Vd)))}function ms(n,i,e){let t=zd(n,i);e=e.trim();let r=t.filter(o=>o!==e);r.length?n.setAttribute(i,r.join(Vd)):n.removeAttribute(i)}function zd(n,i){return n.getAttribute(i)?.match(/\S+/g)??[]}var Tp=new b("cdk-dir-doc",{providedIn:"root",factory:kp});function kp(){return l(N)}var Fp=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function Ud(n){let i=n?.toLowerCase()||"";return i==="auto"&&typeof navigator<"u"&&navigator?.language?Fp.test(navigator.language)?"rtl":"ltr":i==="rtl"?"rtl":"ltr"}var Mt=(()=>{class n{value="ltr";change=new Y;constructor(){let e=l(Tp,{optional:!0});if(e){let t=e.body?e.body.dir:null,r=e.documentElement?e.documentElement.dir:null;this.value=Ud(t||r||"ltr")}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var At=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({})}return n})();var X=(()=>{class n{constructor(){l(ls)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[At,At]})}return n})();var Op=["*"];var Pp=new b("MAT_CARD_CONFIG"),Hd=(()=>{class n{appearance;constructor(){let e=l(Pp,{optional:!0});this.appearance=e?.appearance||"raised"}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-card"]],hostAttrs:[1,"mat-mdc-card","mdc-card"],hostVars:4,hostBindings:function(t,r){t&2&&$("mat-mdc-card-outlined",r.appearance==="outlined")("mdc-card--outlined",r.appearance==="outlined")},inputs:{appearance:"appearance"},exportAs:["matCard"],ngContentSelectors:Op,decls:1,vars:0,template:function(t,r){t&1&&(ge(),Z(0))},styles:[`.mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:"";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:""}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}
`],encapsulation:2,changeDetection:0})}return n})();var $d=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["mat-card-content"]],hostAttrs:[1,"mat-mdc-card-content"]})}return n})();var Gd=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,X]})}return n})();function Kt(n){return n!=null&&`${n}`!="false"}function ce(n){return n==null?"":typeof n=="string"?n:`${n}px`}var Xt;function qd(){if(Xt==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return Xt=!1,Xt;if("scrollBehavior"in document.documentElement.style)Xt=!0;else{let n=Element.prototype.scrollTo;n?Xt=!/\{\s*\[native code\]\s*\}/.test(n.toString()):Xt=!1}}return Xt}function hs(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}var Ei,Yd=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function fs(){if(Ei)return Ei;if(typeof document!="object"||!document)return Ei=new Set(Yd),Ei;let n=document.createElement("input");return Ei=new Set(Yd.filter(i=>(n.setAttribute("type",i),n.type===i))),Ei}var ps=class{_box;_destroyed=new E;_resizeSubject=new E;_resizeObserver;_elementObservables=new Map;constructor(i){this._box=i,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(e=>this._resizeSubject.next(e)))}observe(i){return this._elementObservables.has(i)||this._elementObservables.set(i,new st(e=>{let t=this._resizeSubject.subscribe(e);return this._resizeObserver?.observe(i,{box:this._box}),()=>{this._resizeObserver?.unobserve(i),t.unsubscribe(),this._elementObservables.delete(i)}}).pipe(ae(e=>e.some(t=>t.target===i)),Ro({bufferSize:1,refCount:!0}),de(this._destroyed))),this._elementObservables.get(i)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}},Zd=(()=>{class n{_cleanupErrorListener;_observers=new Map;_ngZone=l(V);constructor(){typeof ResizeObserver<"u"}ngOnDestroy(){for(let[,e]of this._observers)e.destroy();this._observers.clear(),this._cleanupErrorListener?.()}observe(e,t){let r=t?.box||"content-box";return this._observers.has(r)||this._observers.set(r,new ps(r)),this._observers.get(r).observe(e)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Lp=["notch"],Bp=["matFormFieldNotchedOutline",""],Vp=["*"],jp=["textField"],zp=["iconPrefixContainer"],Up=["textPrefixContainer"],Hp=["iconSuffixContainer"],$p=["textSuffixContainer"],Gp=["*",[["mat-label"]],[["","matPrefix",""],["","matIconPrefix",""]],[["","matTextPrefix",""]],[["","matTextSuffix",""]],[["","matSuffix",""],["","matIconSuffix",""]],[["mat-error"],["","matError",""]],[["mat-hint",3,"align","end"]],[["mat-hint","align","end"]]],Wp=["*","mat-label","[matPrefix], [matIconPrefix]","[matTextPrefix]","[matTextSuffix]","[matSuffix], [matIconSuffix]","mat-error, [matError]","mat-hint:not([align='end'])","mat-hint[align='end']"];function qp(n,i){n&1&&O(0,"span",20)}function Yp(n,i){if(n&1&&(u(0,"label",19),Z(1,1),y(2,qp,1,0,"span",20),d()),n&2){let e=P(2);g("floating",e._shouldLabelFloat())("monitorResize",e._hasOutline())("id",e._labelId),ne("for",e._control.disableAutomaticLabeling?null:e._control.id),f(2),le(!e.hideRequiredMarker&&e._control.required?2:-1)}}function Zp(n,i){if(n&1&&y(0,Yp,3,5,"label",19),n&2){let e=P();le(e._hasFloatingLabel()?0:-1)}}function Kp(n,i){n&1&&O(0,"div",7)}function Xp(n,i){}function Qp(n,i){if(n&1&&y(0,Xp,0,0,"ng-template",13),n&2){P(2);let e=jt(1);g("ngTemplateOutlet",e)}}function Jp(n,i){if(n&1&&(u(0,"div",9),y(1,Qp,1,1,null,13),d()),n&2){let e=P();g("matFormFieldNotchedOutlineOpen",e._shouldLabelFloat()),f(),le(e._forceDisplayInfixLabel()?-1:1)}}function eg(n,i){n&1&&(u(0,"div",10,2),Z(2,2),d())}function tg(n,i){n&1&&(u(0,"div",11,3),Z(2,3),d())}function ig(n,i){}function ng(n,i){if(n&1&&y(0,ig,0,0,"ng-template",13),n&2){P();let e=jt(1);g("ngTemplateOutlet",e)}}function rg(n,i){n&1&&(u(0,"div",14,4),Z(2,4),d())}function og(n,i){n&1&&(u(0,"div",15,5),Z(2,5),d())}function ag(n,i){n&1&&O(0,"div",16)}function sg(n,i){n&1&&Z(0,6)}function lg(n,i){if(n&1&&(u(0,"mat-hint",21),p(1),d()),n&2){let e=P(2);g("id",e._hintLabelId),f(),zt(e.hintLabel)}}function cg(n,i){if(n&1&&(y(0,lg,2,2,"mat-hint",21),Z(1,7),O(2,"div",22),Z(3,8)),n&2){let e=P();le(e.hintLabel?0:-1)}}var In=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["mat-label"]]})}return n})(),_s=new b("MatError"),vs=(()=>{class n{id=l(Fe).getId("mat-mdc-error-");constructor(){}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["mat-error"],["","matError",""]],hostAttrs:[1,"mat-mdc-form-field-error","mat-mdc-form-field-bottom-align"],hostVars:1,hostBindings:function(t,r){t&2&&Vt("id",r.id)},inputs:{id:"id"},features:[fe([{provide:_s,useExisting:n}])]})}return n})(),gs=(()=>{class n{align="start";id=l(Fe).getId("mat-mdc-hint-");static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["mat-hint"]],hostAttrs:[1,"mat-mdc-form-field-hint","mat-mdc-form-field-bottom-align"],hostVars:4,hostBindings:function(t,r){t&2&&(Vt("id",r.id),ne("align",null),$("mat-mdc-form-field-hint-end",r.align==="end"))},inputs:{align:"align",id:"id"}})}return n})(),iu=new b("MatPrefix");var nu=new b("MatSuffix");var ru=new b("FloatingLabelParent"),Kd=(()=>{class n{_elementRef=l(H);get floating(){return this._floating}set floating(e){this._floating=e,this.monitorResize&&this._handleResize()}_floating=!1;get monitorResize(){return this._monitorResize}set monitorResize(e){this._monitorResize=e,this._monitorResize?this._subscribeToResize():this._resizeSubscription.unsubscribe()}_monitorResize=!1;_resizeObserver=l(Zd);_ngZone=l(V);_parent=l(ru);_resizeSubscription=new ve;constructor(){}ngOnDestroy(){this._resizeSubscription.unsubscribe()}getWidth(){return dg(this._elementRef.nativeElement)}get element(){return this._elementRef.nativeElement}_handleResize(){setTimeout(()=>this._parent._handleLabelResized())}_subscribeToResize(){this._resizeSubscription.unsubscribe(),this._ngZone.runOutsideAngular(()=>{this._resizeSubscription=this._resizeObserver.observe(this._elementRef.nativeElement,{box:"border-box"}).subscribe(()=>this._handleResize())})}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["label","matFormFieldFloatingLabel",""]],hostAttrs:[1,"mdc-floating-label","mat-mdc-floating-label"],hostVars:2,hostBindings:function(t,r){t&2&&$("mdc-floating-label--float-above",r.floating)},inputs:{floating:"floating",monitorResize:"monitorResize"}})}return n})();function dg(n){let i=n;if(i.offsetParent!==null)return i.scrollWidth;let e=i.cloneNode(!0);e.style.setProperty("position","absolute"),e.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(e);let t=e.scrollWidth;return e.remove(),t}var Xd="mdc-line-ripple--active",eo="mdc-line-ripple--deactivating",Qd=(()=>{class n{_elementRef=l(H);_cleanupTransitionEnd;constructor(){let e=l(V),t=l(Se);e.runOutsideAngular(()=>{this._cleanupTransitionEnd=t.listen(this._elementRef.nativeElement,"transitionend",this._handleTransitionEnd)})}activate(){let e=this._elementRef.nativeElement.classList;e.remove(eo),e.add(Xd)}deactivate(){this._elementRef.nativeElement.classList.add(eo)}_handleTransitionEnd=e=>{let t=this._elementRef.nativeElement.classList,r=t.contains(eo);e.propertyName==="opacity"&&r&&t.remove(Xd,eo)};ngOnDestroy(){this._cleanupTransitionEnd()}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["div","matFormFieldLineRipple",""]],hostAttrs:[1,"mdc-line-ripple"]})}return n})(),Jd=(()=>{class n{_elementRef=l(H);_ngZone=l(V);open=!1;_notch;constructor(){}ngAfterViewInit(){let e=this._elementRef.nativeElement.querySelector(".mdc-floating-label");e?(this._elementRef.nativeElement.classList.add("mdc-notched-outline--upgraded"),typeof requestAnimationFrame=="function"&&(e.style.transitionDuration="0s",this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>e.style.transitionDuration="")}))):this._elementRef.nativeElement.classList.add("mdc-notched-outline--no-label")}_setNotchWidth(e){!this.open||!e?this._notch.nativeElement.style.width="":this._notch.nativeElement.style.width=`calc(${e}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + 9px)`}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["div","matFormFieldNotchedOutline",""]],viewQuery:function(t,r){if(t&1&&ue(Lp,5),t&2){let o;Q(o=J())&&(r._notch=o.first)}},hostAttrs:[1,"mdc-notched-outline"],hostVars:2,hostBindings:function(t,r){t&2&&$("mdc-notched-outline--notched",r.open)},inputs:{open:[0,"matFormFieldNotchedOutlineOpen","open"]},attrs:Bp,ngContentSelectors:Vp,decls:5,vars:0,consts:[["notch",""],[1,"mat-mdc-notch-piece","mdc-notched-outline__leading"],[1,"mat-mdc-notch-piece","mdc-notched-outline__notch"],[1,"mat-mdc-notch-piece","mdc-notched-outline__trailing"]],template:function(t,r){t&1&&(ge(),O(0,"div",1),u(1,"div",2,0),Z(3),d(),O(4,"div",3))},encapsulation:2,changeDetection:0})}return n})(),Si=(()=>{class n{value;stateChanges;id;placeholder;ngControl;focused;empty;shouldLabelFloat;required;disabled;errorState;controlType;autofilled;userAriaDescribedBy;disableAutomaticLabeling;static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n})}return n})();var Mi=new b("MatFormField"),ou=new b("MAT_FORM_FIELD_DEFAULT_OPTIONS"),eu="fill",ug="auto",tu="fixed",mg="translateY(-50%)",bs=(()=>{class n{_elementRef=l(H);_changeDetectorRef=l(qe);_dir=l(Mt);_platform=l(ie);_idGenerator=l(Fe);_ngZone=l(V);_injector=l(se);_defaults=l(ou,{optional:!0});_textField;_iconPrefixContainer;_textPrefixContainer;_iconSuffixContainer;_textSuffixContainer;_floatingLabel;_notchedOutline;_lineRipple;_formFieldControl;_prefixChildren;_suffixChildren;_errorChildren;_hintChildren;_labelChild=ll(In);get hideRequiredMarker(){return this._hideRequiredMarker}set hideRequiredMarker(e){this._hideRequiredMarker=Kt(e)}_hideRequiredMarker=!1;color="primary";get floatLabel(){return this._floatLabel||this._defaults?.floatLabel||ug}set floatLabel(e){e!==this._floatLabel&&(this._floatLabel=e,this._changeDetectorRef.markForCheck())}_floatLabel;get appearance(){return this._appearance}set appearance(e){let t=this._appearance,r=e||this._defaults?.appearance||eu;this._appearance=r,this._appearance==="outline"&&this._appearance!==t&&(this._needsOutlineLabelOffsetUpdate=!0)}_appearance=eu;get subscriptSizing(){return this._subscriptSizing||this._defaults?.subscriptSizing||tu}set subscriptSizing(e){this._subscriptSizing=e||this._defaults?.subscriptSizing||tu}_subscriptSizing=null;get hintLabel(){return this._hintLabel}set hintLabel(e){this._hintLabel=e,this._processHints()}_hintLabel="";_hasIconPrefix=!1;_hasTextPrefix=!1;_hasIconSuffix=!1;_hasTextSuffix=!1;_labelId=this._idGenerator.getId("mat-mdc-form-field-label-");_hintLabelId=this._idGenerator.getId("mat-mdc-hint-");get _control(){return this._explicitFormFieldControl||this._formFieldControl}set _control(e){this._explicitFormFieldControl=e}_destroyed=new E;_isFocused=null;_explicitFormFieldControl;_needsOutlineLabelOffsetUpdate=!1;_previousControl=null;_previousControlValidatorFn=null;_stateChanges;_valueChanges;_describedByChanges;_animationsDisabled;constructor(){let e=this._defaults;e&&(e.appearance&&(this.appearance=e.appearance),this._hideRequiredMarker=!!e?.hideRequiredMarker,e.color&&(this.color=e.color)),this._animationsDisabled=l(he,{optional:!0})==="NoopAnimations"}ngAfterViewInit(){this._updateFocusState(),this._animationsDisabled||this._ngZone.runOutsideAngular(()=>{setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-form-field-animations-enabled")},300)}),this._changeDetectorRef.detectChanges()}ngAfterContentInit(){this._assertFormFieldControl(),this._initializeSubscript(),this._initializePrefixAndSuffix(),this._initializeOutlineLabelOffsetSubscriptions()}ngAfterContentChecked(){this._assertFormFieldControl(),this._control!==this._previousControl&&(this._initializeControl(this._previousControl),this._control.ngControl&&this._control.ngControl.control&&(this._previousControlValidatorFn=this._control.ngControl.control.validator),this._previousControl=this._control),this._control.ngControl&&this._control.ngControl.control&&this._control.ngControl.control.validator!==this._previousControlValidatorFn&&this._changeDetectorRef.markForCheck()}ngOnDestroy(){this._stateChanges?.unsubscribe(),this._valueChanges?.unsubscribe(),this._describedByChanges?.unsubscribe(),this._destroyed.next(),this._destroyed.complete()}getLabelId=et(()=>this._hasFloatingLabel()?this._labelId:null);getConnectedOverlayOrigin(){return this._textField||this._elementRef}_animateAndLockLabel(){this._hasFloatingLabel()&&(this.floatLabel="always")}_initializeControl(e){let t=this._control,r="mat-mdc-form-field-type-";e&&this._elementRef.nativeElement.classList.remove(r+e.controlType),t.controlType&&this._elementRef.nativeElement.classList.add(r+t.controlType),this._stateChanges?.unsubscribe(),this._stateChanges=t.stateChanges.subscribe(()=>{this._updateFocusState(),this._changeDetectorRef.markForCheck()}),this._describedByChanges?.unsubscribe(),this._describedByChanges=t.stateChanges.pipe(Ze([void 0,void 0]),M(()=>[t.errorState,t.userAriaDescribedBy]),Io(),ae(([[o,a],[s,c]])=>o!==s||a!==c)).subscribe(()=>this._syncDescribedByIds()),this._valueChanges?.unsubscribe(),t.ngControl&&t.ngControl.valueChanges&&(this._valueChanges=t.ngControl.valueChanges.pipe(de(this._destroyed)).subscribe(()=>this._changeDetectorRef.markForCheck()))}_checkPrefixAndSuffixTypes(){this._hasIconPrefix=!!this._prefixChildren.find(e=>!e._isText),this._hasTextPrefix=!!this._prefixChildren.find(e=>e._isText),this._hasIconSuffix=!!this._suffixChildren.find(e=>!e._isText),this._hasTextSuffix=!!this._suffixChildren.find(e=>e._isText)}_initializePrefixAndSuffix(){this._checkPrefixAndSuffixTypes(),lt(this._prefixChildren.changes,this._suffixChildren.changes).subscribe(()=>{this._checkPrefixAndSuffixTypes(),this._changeDetectorRef.markForCheck()})}_initializeSubscript(){this._hintChildren.changes.subscribe(()=>{this._processHints(),this._changeDetectorRef.markForCheck()}),this._errorChildren.changes.subscribe(()=>{this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),this._validateHints(),this._syncDescribedByIds()}_assertFormFieldControl(){this._control}_updateFocusState(){this._control.focused&&!this._isFocused?(this._isFocused=!0,this._lineRipple?.activate()):!this._control.focused&&(this._isFocused||this._isFocused===null)&&(this._isFocused=!1,this._lineRipple?.deactivate()),this._textField?.nativeElement.classList.toggle("mdc-text-field--focused",this._control.focused)}_initializeOutlineLabelOffsetSubscriptions(){this._prefixChildren.changes.subscribe(()=>this._needsOutlineLabelOffsetUpdate=!0),Wn(()=>{this._needsOutlineLabelOffsetUpdate&&(this._needsOutlineLabelOffsetUpdate=!1,this._updateOutlineLabelOffset())},{injector:this._injector}),this._dir.change.pipe(de(this._destroyed)).subscribe(()=>this._needsOutlineLabelOffsetUpdate=!0)}_shouldAlwaysFloat(){return this.floatLabel==="always"}_hasOutline(){return this.appearance==="outline"}_forceDisplayInfixLabel(){return!this._platform.isBrowser&&this._prefixChildren.length&&!this._shouldLabelFloat()}_hasFloatingLabel=et(()=>!!this._labelChild());_shouldLabelFloat(){return this._hasFloatingLabel()?this._control.shouldLabelFloat||this._shouldAlwaysFloat():!1}_shouldForward(e){let t=this._control?this._control.ngControl:null;return t&&t[e]}_getSubscriptMessageType(){return this._errorChildren&&this._errorChildren.length>0&&this._control.errorState?"error":"hint"}_handleLabelResized(){this._refreshOutlineNotchWidth()}_refreshOutlineNotchWidth(){!this._hasOutline()||!this._floatingLabel||!this._shouldLabelFloat()?this._notchedOutline?._setNotchWidth(0):this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth())}_processHints(){this._validateHints(),this._syncDescribedByIds()}_validateHints(){this._hintChildren}_syncDescribedByIds(){if(this._control){let e=[];if(this._control.userAriaDescribedBy&&typeof this._control.userAriaDescribedBy=="string"&&e.push(...this._control.userAriaDescribedBy.split(" ")),this._getSubscriptMessageType()==="hint"){let t=this._hintChildren?this._hintChildren.find(o=>o.align==="start"):null,r=this._hintChildren?this._hintChildren.find(o=>o.align==="end"):null;t?e.push(t.id):this._hintLabel&&e.push(this._hintLabelId),r&&e.push(r.id)}else this._errorChildren&&e.push(...this._errorChildren.map(t=>t.id));this._control.setDescribedByIds(e)}}_updateOutlineLabelOffset(){if(!this._hasOutline()||!this._floatingLabel)return;let e=this._floatingLabel.element;if(!(this._iconPrefixContainer||this._textPrefixContainer)){e.style.transform="";return}if(!this._isAttachedToDom()){this._needsOutlineLabelOffsetUpdate=!0;return}let t=this._iconPrefixContainer?.nativeElement,r=this._textPrefixContainer?.nativeElement,o=this._iconSuffixContainer?.nativeElement,a=this._textSuffixContainer?.nativeElement,s=t?.getBoundingClientRect().width??0,c=r?.getBoundingClientRect().width??0,m=o?.getBoundingClientRect().width??0,h=a?.getBoundingClientRect().width??0,C=this._dir.value==="rtl"?"-1":"1",x=`${s+c}px`,L=`calc(${C} * (${x} + var(--mat-mdc-form-field-label-offset-x, 0px)))`;e.style.transform=`var(
        --mat-mdc-form-field-label-transform,
        ${mg} translateX(${L})
    )`;let w=s+c+m+h;this._elementRef.nativeElement.style.setProperty("--mat-form-field-notch-max-width",`calc(100% - ${w}px)`)}_isAttachedToDom(){let e=this._elementRef.nativeElement;if(e.getRootNode){let t=e.getRootNode();return t&&t!==e}return document.documentElement.contains(e)}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-form-field"]],contentQueries:function(t,r,o){if(t&1&&(hl(o,r._labelChild,In,5),Ve(o,Si,5),Ve(o,iu,5),Ve(o,nu,5),Ve(o,_s,5),Ve(o,gs,5)),t&2){fl();let a;Q(a=J())&&(r._formFieldControl=a.first),Q(a=J())&&(r._prefixChildren=a),Q(a=J())&&(r._suffixChildren=a),Q(a=J())&&(r._errorChildren=a),Q(a=J())&&(r._hintChildren=a)}},viewQuery:function(t,r){if(t&1&&(ue(jp,5),ue(zp,5),ue(Up,5),ue(Hp,5),ue($p,5),ue(Kd,5),ue(Jd,5),ue(Qd,5)),t&2){let o;Q(o=J())&&(r._textField=o.first),Q(o=J())&&(r._iconPrefixContainer=o.first),Q(o=J())&&(r._textPrefixContainer=o.first),Q(o=J())&&(r._iconSuffixContainer=o.first),Q(o=J())&&(r._textSuffixContainer=o.first),Q(o=J())&&(r._floatingLabel=o.first),Q(o=J())&&(r._notchedOutline=o.first),Q(o=J())&&(r._lineRipple=o.first)}},hostAttrs:[1,"mat-mdc-form-field"],hostVars:40,hostBindings:function(t,r){t&2&&$("mat-mdc-form-field-label-always-float",r._shouldAlwaysFloat())("mat-mdc-form-field-has-icon-prefix",r._hasIconPrefix)("mat-mdc-form-field-has-icon-suffix",r._hasIconSuffix)("mat-form-field-invalid",r._control.errorState)("mat-form-field-disabled",r._control.disabled)("mat-form-field-autofilled",r._control.autofilled)("mat-form-field-appearance-fill",r.appearance=="fill")("mat-form-field-appearance-outline",r.appearance=="outline")("mat-form-field-hide-placeholder",r._hasFloatingLabel()&&!r._shouldLabelFloat())("mat-focused",r._control.focused)("mat-primary",r.color!=="accent"&&r.color!=="warn")("mat-accent",r.color==="accent")("mat-warn",r.color==="warn")("ng-untouched",r._shouldForward("untouched"))("ng-touched",r._shouldForward("touched"))("ng-pristine",r._shouldForward("pristine"))("ng-dirty",r._shouldForward("dirty"))("ng-valid",r._shouldForward("valid"))("ng-invalid",r._shouldForward("invalid"))("ng-pending",r._shouldForward("pending"))},inputs:{hideRequiredMarker:"hideRequiredMarker",color:"color",floatLabel:"floatLabel",appearance:"appearance",subscriptSizing:"subscriptSizing",hintLabel:"hintLabel"},exportAs:["matFormField"],features:[fe([{provide:Mi,useExisting:n},{provide:ru,useExisting:n}])],ngContentSelectors:Wp,decls:20,vars:25,consts:[["labelTemplate",""],["textField",""],["iconPrefixContainer",""],["textPrefixContainer",""],["textSuffixContainer",""],["iconSuffixContainer",""],[1,"mat-mdc-text-field-wrapper","mdc-text-field",3,"click"],[1,"mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-flex"],["matFormFieldNotchedOutline","",3,"matFormFieldNotchedOutlineOpen"],[1,"mat-mdc-form-field-icon-prefix"],[1,"mat-mdc-form-field-text-prefix"],[1,"mat-mdc-form-field-infix"],[3,"ngTemplateOutlet"],[1,"mat-mdc-form-field-text-suffix"],[1,"mat-mdc-form-field-icon-suffix"],["matFormFieldLineRipple",""],[1,"mat-mdc-form-field-subscript-wrapper","mat-mdc-form-field-bottom-align"],["aria-atomic","true","aria-live","polite"],["matFormFieldFloatingLabel","",3,"floating","monitorResize","id"],["aria-hidden","true",1,"mat-mdc-form-field-required-marker","mdc-floating-label--required"],[3,"id"],[1,"mat-mdc-form-field-hint-spacer"]],template:function(t,r){if(t&1){let o=Bt();ge(Gp),y(0,Zp,1,1,"ng-template",null,0,Yn),u(2,"div",6,1),re("click",function(s){return Le(o),Be(r._control.onContainerClick(s))}),y(4,Kp,1,0,"div",7),u(5,"div",8),y(6,Jp,2,2,"div",9)(7,eg,3,0,"div",10)(8,tg,3,0,"div",11),u(9,"div",12),y(10,ng,1,1,null,13),Z(11),d(),y(12,rg,3,0,"div",14)(13,og,3,0,"div",15),d(),y(14,ag,1,0,"div",16),d(),u(15,"div",17),pl(16),u(17,"div",18),y(18,sg,1,0)(19,cg,4,1),d()()}if(t&2){let o;f(2),$("mdc-text-field--filled",!r._hasOutline())("mdc-text-field--outlined",r._hasOutline())("mdc-text-field--no-label",!r._hasFloatingLabel())("mdc-text-field--disabled",r._control.disabled)("mdc-text-field--invalid",r._control.errorState),f(2),le(!r._hasOutline()&&!r._control.disabled?4:-1),f(2),le(r._hasOutline()?6:-1),f(),le(r._hasIconPrefix?7:-1),f(),le(r._hasTextPrefix?8:-1),f(2),le(!r._hasOutline()||r._forceDisplayInfixLabel()?10:-1),f(2),le(r._hasTextSuffix?12:-1),f(),le(r._hasIconSuffix?13:-1),f(),le(r._hasOutline()?-1:14),f(),$("mat-mdc-form-field-subscript-dynamic-size",r.subscriptSizing==="dynamic");let a=r._getSubscriptMessageType();f(2),$("mat-mdc-form-field-error-wrapper",a==="error")("mat-mdc-form-field-hint-wrapper",a==="hint"),f(),le((o=a)==="error"?18:o==="hint"?19:-1)}},dependencies:[Kd,Jd,zi,Qd,gs],styles:[`.mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:"*"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:""}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:"";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:"";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}
`],encapsulation:2,changeDetection:0})}return n})();var It=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,Rd,X]})}return n})();var fg=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["ng-component"]],hostAttrs:["cdk-text-field-style-loader",""],decls:0,vars:0,template:function(t,r){},styles:[`textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}
`],encapsulation:2,changeDetection:0})}return n})(),pg={passive:!0},au=(()=>{class n{_platform=l(ie);_ngZone=l(V);_renderer=l(pe).createRenderer(null,null);_styleLoader=l(He);_monitoredElements=new Map;constructor(){}monitor(e){if(!this._platform.isBrowser)return Ge;this._styleLoader.load(fg);let t=ke(e),r=this._monitoredElements.get(t);if(r)return r.subject;let o=new E,a="cdk-text-field-autofilled",s=m=>{m.animationName==="cdk-text-field-autofill-start"&&!t.classList.contains(a)?(t.classList.add(a),this._ngZone.run(()=>o.next({target:m.target,isAutofilled:!0}))):m.animationName==="cdk-text-field-autofill-end"&&t.classList.contains(a)&&(t.classList.remove(a),this._ngZone.run(()=>o.next({target:m.target,isAutofilled:!1})))},c=this._ngZone.runOutsideAngular(()=>(t.classList.add("cdk-text-field-autofill-monitored"),Me(this._renderer,t,"animationstart",s,pg)));return this._monitoredElements.set(t,{subject:o,unlisten:c}),o}stopMonitoring(e){let t=ke(e),r=this._monitoredElements.get(t);r&&(r.unlisten(),r.subject.complete(),t.classList.remove("cdk-text-field-autofill-monitored"),t.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(t))}ngOnDestroy(){this._monitoredElements.forEach((e,t)=>this.stopMonitoring(t))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var su=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({})}return n})();var lu=new b("MAT_INPUT_VALUE_ACCESSOR");var to=(()=>{class n{isErrorState(e,t){return!!(e&&e.invalid&&(e.touched||t&&t.submitted))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Ai=class{_defaultMatcher;ngControl;_parentFormGroup;_parentForm;_stateChanges;errorState=!1;matcher;constructor(i,e,t,r,o){this._defaultMatcher=i,this.ngControl=e,this._parentFormGroup=t,this._parentForm=r,this._stateChanges=o}updateErrorState(){let i=this.errorState,e=this._parentFormGroup||this._parentForm,t=this.matcher||this._defaultMatcher,r=this.ngControl?this.ngControl.control:null,o=t?.isErrorState(r,e)??!1;o!==i&&(this.errorState=o,this._stateChanges.next())}};var gg=["button","checkbox","file","hidden","image","radio","range","reset","submit"],_g=new b("MAT_INPUT_CONFIG"),cu=(()=>{class n{_elementRef=l(H);_platform=l(ie);ngControl=l(rt,{optional:!0,self:!0});_autofillMonitor=l(au);_ngZone=l(V);_formField=l(Mi,{optional:!0});_renderer=l(Se);_uid=l(Fe).getId("mat-input-");_previousNativeValue;_inputValueAccessor;_signalBasedValueAccessor;_previousPlaceholder;_errorStateTracker;_config=l(_g,{optional:!0});_cleanupIosKeyup;_cleanupWebkitWheel;_formFieldDescribedBy;_isServer;_isNativeSelect;_isTextarea;_isInFormField;focused=!1;stateChanges=new E;controlType="mat-input";autofilled=!1;get disabled(){return this._disabled}set disabled(e){this._disabled=Kt(e),this.focused&&(this.focused=!1,this.stateChanges.next())}_disabled=!1;get id(){return this._id}set id(e){this._id=e||this._uid}_id;placeholder;name;get required(){return this._required??this.ngControl?.control?.hasValidator(W.required)??!1}set required(e){this._required=Kt(e)}_required;get type(){return this._type}set type(e){let t=this._type;this._type=e||"text",this._validateType(),!this._isTextarea&&fs().has(this._type)&&(this._elementRef.nativeElement.type=this._type),this._type!==t&&this._ensureWheelDefaultBehavior()}_type="text";get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}userAriaDescribedBy;get value(){return this._signalBasedValueAccessor?this._signalBasedValueAccessor.value():this._inputValueAccessor.value}set value(e){e!==this.value&&(this._signalBasedValueAccessor?this._signalBasedValueAccessor.value.set(e):this._inputValueAccessor.value=e,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(e){this._readonly=Kt(e)}_readonly=!1;disabledInteractive;get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}_neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(e=>fs().has(e));constructor(){let e=l(yn,{optional:!0}),t=l(ot,{optional:!0}),r=l(to),o=l(lu,{optional:!0,self:!0}),a=this._elementRef.nativeElement,s=a.nodeName.toLowerCase();o?Ot(o.value)?this._signalBasedValueAccessor=o:this._inputValueAccessor=o:this._inputValueAccessor=a,this._previousNativeValue=this.value,this.id=this.id,this._platform.IOS&&this._ngZone.runOutsideAngular(()=>{this._cleanupIosKeyup=this._renderer.listen(a,"keyup",this._iOSKeyupListener)}),this._errorStateTracker=new Ai(r,this.ngControl,t,e,this.stateChanges),this._isServer=!this._platform.isBrowser,this._isNativeSelect=s==="select",this._isTextarea=s==="textarea",this._isInFormField=!!this._formField,this.disabledInteractive=this._config?.disabledInteractive||!1,this._isNativeSelect&&(this.controlType=a.multiple?"mat-native-select-multiple":"mat-native-select"),this._signalBasedValueAccessor&&Zn(()=>{this._signalBasedValueAccessor.value(),this.stateChanges.next()})}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(e=>{this.autofilled=e.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._cleanupIosKeyup?.(),this._cleanupWebkitWheel?.()}ngDoCheck(){this.ngControl&&(this.updateErrorState(),this.ngControl.disabled!==null&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(e){this._elementRef.nativeElement.focus(e)}updateErrorState(){this._errorStateTracker.updateErrorState()}_focusChanged(e){if(e!==this.focused){if(!this._isNativeSelect&&e&&this.disabled&&this.disabledInteractive){let t=this._elementRef.nativeElement;t.type==="number"?(t.type="text",t.setSelectionRange(0,0),t.type="number"):t.setSelectionRange(0,0)}this.focused=e,this.stateChanges.next()}}_onInput(){}_dirtyCheckNativeValue(){let e=this._elementRef.nativeElement.value;this._previousNativeValue!==e&&(this._previousNativeValue=e,this.stateChanges.next())}_dirtyCheckPlaceholder(){let e=this._getPlaceholder();if(e!==this._previousPlaceholder){let t=this._elementRef.nativeElement;this._previousPlaceholder=e,e?t.setAttribute("placeholder",e):t.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){gg.indexOf(this._type)>-1}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let e=this._elementRef.nativeElement.validity;return e&&e.badInput}get empty(){return!this._isNeverEmpty()&&!this._elementRef.nativeElement.value&&!this._isBadInput()&&!this.autofilled}get shouldLabelFloat(){if(this._isNativeSelect){let e=this._elementRef.nativeElement,t=e.options[0];return this.focused||e.multiple||!this.empty||!!(e.selectedIndex>-1&&t&&t.label)}else return this.focused&&!this.disabled||!this.empty}setDescribedByIds(e){let t=this._elementRef.nativeElement,r=t.getAttribute("aria-describedby"),o;if(r){let a=this._formFieldDescribedBy||e;o=e.concat(r.split(" ").filter(s=>s&&!a.includes(s)))}else o=e;this._formFieldDescribedBy=e,o.length?t.setAttribute("aria-describedby",o.join(" ")):t.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){let e=this._elementRef.nativeElement;return this._isNativeSelect&&(e.multiple||e.size>1)}_iOSKeyupListener=e=>{let t=e.target;!t.value&&t.selectionStart===0&&t.selectionEnd===0&&(t.setSelectionRange(1,1),t.setSelectionRange(0,0))};_webkitBlinkWheelListener=()=>{};_ensureWheelDefaultBehavior(){this._cleanupWebkitWheel?.(),this._type==="number"&&(this._platform.BLINK||this._platform.WEBKIT)&&(this._cleanupWebkitWheel=this._renderer.listen(this._elementRef.nativeElement,"wheel",this._webkitBlinkWheelListener))}_getReadonlyAttribute(){return this._isNativeSelect?null:this.readonly||this.disabled&&this.disabledInteractive?"true":null}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:21,hostBindings:function(t,r){t&1&&re("focus",function(){return r._focusChanged(!0)})("blur",function(){return r._focusChanged(!1)})("input",function(){return r._onInput()}),t&2&&(Vt("id",r.id)("disabled",r.disabled&&!r.disabledInteractive)("required",r.required),ne("name",r.name||null)("readonly",r._getReadonlyAttribute())("aria-disabled",r.disabled&&r.disabledInteractive?"true":null)("aria-invalid",r.empty&&r.required?null:r.errorState)("aria-required",r.required)("id",r.id),$("mat-input-server",r._isServer)("mat-mdc-form-field-textarea-control",r._isInFormField&&r._isTextarea)("mat-mdc-form-field-input-control",r._isInFormField)("mat-mdc-input-disabled-interactive",r.disabledInteractive)("mdc-text-field__input",r._isInFormField)("mat-mdc-native-select-inline",r._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly",disabledInteractive:[2,"disabledInteractive","disabledInteractive",q]},exportAs:["matInput"],features:[fe([{provide:Si,useExisting:n}]),Ce]})}return n})(),du=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,It,It,su,X]})}return n})();var $e=function(n){return n[n.FADING_IN=0]="FADING_IN",n[n.VISIBLE=1]="VISIBLE",n[n.FADING_OUT=2]="FADING_OUT",n[n.HIDDEN=3]="HIDDEN",n}($e||{}),ys=class{_renderer;element;config;_animationForciblyDisabledThroughCss;state=$e.HIDDEN;constructor(i,e,t,r=!1){this._renderer=i,this.element=e,this.config=t,this._animationForciblyDisabledThroughCss=r}fadeOut(){this._renderer.fadeOutRipple(this)}},uu=Di({passive:!0,capture:!0}),Cs=class{_events=new Map;addHandler(i,e,t,r){let o=this._events.get(e);if(o){let a=o.get(t);a?a.add(r):o.set(t,new Set([r]))}else this._events.set(e,new Map([[t,new Set([r])]])),i.runOutsideAngular(()=>{document.addEventListener(e,this._delegateEventHandler,uu)})}removeHandler(i,e,t){let r=this._events.get(i);if(!r)return;let o=r.get(e);o&&(o.delete(t),o.size===0&&r.delete(e),r.size===0&&(this._events.delete(i),document.removeEventListener(i,this._delegateEventHandler,uu)))}_delegateEventHandler=i=>{let e=De(i);e&&this._events.get(i.type)?.forEach((t,r)=>{(r===e||r.contains(e))&&t.forEach(o=>o.handleEvent(i))})}},Rn={enterDuration:225,exitDuration:150},bg=800,mu=Di({passive:!0,capture:!0}),hu=["mousedown","touchstart"],fu=["mouseup","mouseleave","touchend","touchcancel"],yg=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["ng-component"]],hostAttrs:["mat-ripple-style-loader",""],decls:0,vars:0,template:function(t,r){},styles:[`.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}
`],encapsulation:2,changeDetection:0})}return n})(),Tn=class n{_target;_ngZone;_platform;_containerElement;_triggerElement;_isPointerDown=!1;_activeRipples=new Map;_mostRecentTransientRipple;_lastTouchStartEvent;_pointerUpEventsRegistered=!1;_containerRect;static _eventManager=new Cs;constructor(i,e,t,r,o){this._target=i,this._ngZone=e,this._platform=r,r.isBrowser&&(this._containerElement=ke(t)),o&&o.get(He).load(yg)}fadeInRipple(i,e,t={}){let r=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),o=_(_({},Rn),t.animation);t.centered&&(i=r.left+r.width/2,e=r.top+r.height/2);let a=t.radius||Cg(i,e,r),s=i-r.left,c=e-r.top,m=o.enterDuration,h=document.createElement("div");h.classList.add("mat-ripple-element"),h.style.left=`${s-a}px`,h.style.top=`${c-a}px`,h.style.height=`${a*2}px`,h.style.width=`${a*2}px`,t.color!=null&&(h.style.backgroundColor=t.color),h.style.transitionDuration=`${m}ms`,this._containerElement.appendChild(h);let C=window.getComputedStyle(h),x=C.transitionProperty,A=C.transitionDuration,L=x==="none"||A==="0s"||A==="0s, 0s"||r.width===0&&r.height===0,w=new ys(this,h,t,L);h.style.transform="scale3d(1, 1, 1)",w.state=$e.FADING_IN,t.persistent||(this._mostRecentTransientRipple=w);let B=null;return!L&&(m||o.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let me=()=>{B&&(B.fallbackTimer=null),clearTimeout(te),this._finishRippleTransition(w)},Ae=()=>this._destroyRipple(w),te=setTimeout(Ae,m+100);h.addEventListener("transitionend",me),h.addEventListener("transitioncancel",Ae),B={onTransitionEnd:me,onTransitionCancel:Ae,fallbackTimer:te}}),this._activeRipples.set(w,B),(L||!m)&&this._finishRippleTransition(w),w}fadeOutRipple(i){if(i.state===$e.FADING_OUT||i.state===$e.HIDDEN)return;let e=i.element,t=_(_({},Rn),i.config.animation);e.style.transitionDuration=`${t.exitDuration}ms`,e.style.opacity="0",i.state=$e.FADING_OUT,(i._animationForciblyDisabledThroughCss||!t.exitDuration)&&this._finishRippleTransition(i)}fadeOutAll(){this._getActiveRipples().forEach(i=>i.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(i=>{i.config.persistent||i.fadeOut()})}setupTriggerEvents(i){let e=ke(i);!this._platform.isBrowser||!e||e===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=e,hu.forEach(t=>{n._eventManager.addHandler(this._ngZone,t,e,this)}))}handleEvent(i){i.type==="mousedown"?this._onMousedown(i):i.type==="touchstart"?this._onTouchStart(i):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{fu.forEach(e=>{this._triggerElement.addEventListener(e,this,mu)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(i){i.state===$e.FADING_IN?this._startFadeOutTransition(i):i.state===$e.FADING_OUT&&this._destroyRipple(i)}_startFadeOutTransition(i){let e=i===this._mostRecentTransientRipple,{persistent:t}=i.config;i.state=$e.VISIBLE,!t&&(!e||!this._isPointerDown)&&i.fadeOut()}_destroyRipple(i){let e=this._activeRipples.get(i)??null;this._activeRipples.delete(i),this._activeRipples.size||(this._containerRect=null),i===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),i.state=$e.HIDDEN,e!==null&&(i.element.removeEventListener("transitionend",e.onTransitionEnd),i.element.removeEventListener("transitioncancel",e.onTransitionCancel),e.fallbackTimer!==null&&clearTimeout(e.fallbackTimer)),i.element.remove()}_onMousedown(i){let e=xn(i),t=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+bg;!this._target.rippleDisabled&&!e&&!t&&(this._isPointerDown=!0,this.fadeInRipple(i.clientX,i.clientY,this._target.rippleConfig))}_onTouchStart(i){if(!this._target.rippleDisabled&&!En(i)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let e=i.changedTouches;if(e)for(let t=0;t<e.length;t++)this.fadeInRipple(e[t].clientX,e[t].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(i=>{let e=i.state===$e.VISIBLE||i.config.terminateOnPointerUp&&i.state===$e.FADING_IN;!i.config.persistent&&e&&i.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let i=this._triggerElement;i&&(hu.forEach(e=>n._eventManager.removeHandler(e,i,this)),this._pointerUpEventsRegistered&&(fu.forEach(e=>i.removeEventListener(e,this,mu)),this._pointerUpEventsRegistered=!1))}};function Cg(n,i,e){let t=Math.max(Math.abs(n-e.left),Math.abs(n-e.right)),r=Math.max(Math.abs(i-e.top),Math.abs(i-e.bottom));return Math.sqrt(t*t+r*r)}var ws=new b("mat-ripple-global-options"),pu=(()=>{class n{_elementRef=l(H);_animationMode=l(he,{optional:!0});color;unbounded;centered;radius=0;animation;get disabled(){return this._disabled}set disabled(e){e&&this.fadeOutAllNonPersistent(),this._disabled=e,this._setupTriggerEventsIfEnabled()}_disabled=!1;get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(e){this._trigger=e,this._setupTriggerEventsIfEnabled()}_trigger;_rippleRenderer;_globalOptions;_isInitialized=!1;constructor(){let e=l(V),t=l(ie),r=l(ws,{optional:!0}),o=l(se);this._globalOptions=r||{},this._rippleRenderer=new Tn(this,e,this._elementRef,t,o)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:_(_(_({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(e,t=0,r){return typeof e=="number"?this._rippleRenderer.fadeInRipple(e,t,_(_({},this.rippleConfig),r)):this._rippleRenderer.fadeInRipple(0,0,_(_({},this.rippleConfig),e))}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(t,r){t&2&&$("mat-ripple-unbounded",r.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return n})();var gu=(()=>{class n{_animationMode=l(he,{optional:!0});state="unchecked";disabled=!1;appearance="full";constructor(){}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-pseudo-checkbox"]],hostAttrs:[1,"mat-pseudo-checkbox"],hostVars:12,hostBindings:function(t,r){t&2&&$("mat-pseudo-checkbox-indeterminate",r.state==="indeterminate")("mat-pseudo-checkbox-checked",r.state==="checked")("mat-pseudo-checkbox-disabled",r.disabled)("mat-pseudo-checkbox-minimal",r.appearance==="minimal")("mat-pseudo-checkbox-full",r.appearance==="full")("_mat-animation-noopable",r._animationMode==="NoopAnimations")},inputs:{state:"state",disabled:"disabled",appearance:"appearance"},decls:0,vars:0,template:function(t,r){},styles:[`.mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:"";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}
`],encapsulation:2,changeDetection:0})}return n})();var io=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["structural-styles"]],decls:0,vars:0,template:function(t,r){},styles:[`.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}
`],encapsulation:2,changeDetection:0})}return n})();var wg=["text"],Dg=[[["mat-icon"]],"*"],xg=["mat-icon","*"];function Eg(n,i){if(n&1&&O(0,"mat-pseudo-checkbox",1),n&2){let e=P();g("disabled",e.disabled)("state",e.selected?"checked":"unchecked")}}function Sg(n,i){if(n&1&&O(0,"mat-pseudo-checkbox",3),n&2){let e=P();g("disabled",e.disabled)}}function Mg(n,i){if(n&1&&(u(0,"span",4),p(1),d()),n&2){let e=P();f(),Ut("(",e.group.label,")")}}var xs=new b("MAT_OPTION_PARENT_COMPONENT"),Es=new b("MatOptgroup");var Ds=class{source;isUserInput;constructor(i,e=!1){this.source=i,this.isUserInput=e}},kn=(()=>{class n{_element=l(H);_changeDetectorRef=l(qe);_parent=l(xs,{optional:!0});group=l(Es,{optional:!0});_signalDisableRipple=!1;_selected=!1;_active=!1;_disabled=!1;_mostRecentViewValue="";get multiple(){return this._parent&&this._parent.multiple}get selected(){return this._selected}value;id=l(Fe).getId("mat-option-");get disabled(){return this.group&&this.group.disabled||this._disabled}set disabled(e){this._disabled=e}get disableRipple(){return this._signalDisableRipple?this._parent.disableRipple():!!this._parent?.disableRipple}get hideSingleSelectionIndicator(){return!!(this._parent&&this._parent.hideSingleSelectionIndicator)}onSelectionChange=new Y;_text;_stateChanges=new E;constructor(){let e=l(He);e.load(io),e.load(Sd),this._signalDisableRipple=!!this._parent&&Ot(this._parent.disableRipple)}get active(){return this._active}get viewValue(){return(this._text?.nativeElement.textContent||"").trim()}select(e=!0){this._selected||(this._selected=!0,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}deselect(e=!0){this._selected&&(this._selected=!1,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}focus(e,t){let r=this._getHostElement();typeof r.focus=="function"&&r.focus(t)}setActiveStyles(){this._active||(this._active=!0,this._changeDetectorRef.markForCheck())}setInactiveStyles(){this._active&&(this._active=!1,this._changeDetectorRef.markForCheck())}getLabel(){return this.viewValue}_handleKeydown(e){(e.keyCode===13||e.keyCode===32)&&!Qe(e)&&(this._selectViaInteraction(),e.preventDefault())}_selectViaInteraction(){this.disabled||(this._selected=this.multiple?!this._selected:!0,this._changeDetectorRef.markForCheck(),this._emitSelectionChangeEvent(!0))}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._element.nativeElement}ngAfterViewChecked(){if(this._selected){let e=this.viewValue;e!==this._mostRecentViewValue&&(this._mostRecentViewValue&&this._stateChanges.next(),this._mostRecentViewValue=e)}}ngOnDestroy(){this._stateChanges.complete()}_emitSelectionChangeEvent(e=!1){this.onSelectionChange.emit(new Ds(this,e))}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-option"]],viewQuery:function(t,r){if(t&1&&ue(wg,7),t&2){let o;Q(o=J())&&(r._text=o.first)}},hostAttrs:["role","option",1,"mat-mdc-option","mdc-list-item"],hostVars:11,hostBindings:function(t,r){t&1&&re("click",function(){return r._selectViaInteraction()})("keydown",function(a){return r._handleKeydown(a)}),t&2&&(Vt("id",r.id),ne("aria-selected",r.selected)("aria-disabled",r.disabled.toString()),$("mdc-list-item--selected",r.selected)("mat-mdc-option-multiple",r.multiple)("mat-mdc-option-active",r.active)("mdc-list-item--disabled",r.disabled))},inputs:{value:"value",id:"id",disabled:[2,"disabled","disabled",q]},outputs:{onSelectionChange:"onSelectionChange"},exportAs:["matOption"],ngContentSelectors:xg,decls:8,vars:5,consts:[["text",""],["aria-hidden","true",1,"mat-mdc-option-pseudo-checkbox",3,"disabled","state"],[1,"mdc-list-item__primary-text"],["state","checked","aria-hidden","true","appearance","minimal",1,"mat-mdc-option-pseudo-checkbox",3,"disabled"],[1,"cdk-visually-hidden"],["aria-hidden","true","mat-ripple","",1,"mat-mdc-option-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled"]],template:function(t,r){t&1&&(ge(Dg),y(0,Eg,1,2,"mat-pseudo-checkbox",1),Z(1),u(2,"span",2,0),Z(4,1),d(),y(5,Sg,1,1,"mat-pseudo-checkbox",3)(6,Mg,2,1,"span",4),O(7,"div",5)),t&2&&(le(r.multiple?0:-1),f(5),le(!r.multiple&&r.selected&&!r.hideSingleSelectionIndicator?5:-1),f(),le(r.group&&r.group._inert?6:-1),f(),g("matRippleTrigger",r._getHostElement())("matRippleDisabled",r.disabled||r.disableRipple))},dependencies:[gu,pu],styles:[`.mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:""}
`],encapsulation:2,changeDetection:0})}return n})();function _u(n,i,e){if(e.length){let t=i.toArray(),r=e.toArray(),o=0;for(let a=0;a<n+1;a++)t[a].group&&t[a].group===r[o]&&o++;return o}return 0}function vu(n,i,e,t){return n<e?n:n+i>e+t?Math.max(0,n-t+i):e}var Ag=20,bu=(()=>{class n{_ngZone=l(V);_platform=l(ie);_renderer=l(pe).createRenderer(null,null);_cleanupGlobalListener;constructor(){}_scrolled=new E;_scrolledCount=0;scrollContainers=new Map;register(e){this.scrollContainers.has(e)||this.scrollContainers.set(e,e.elementScrolled().subscribe(()=>this._scrolled.next(e)))}deregister(e){let t=this.scrollContainers.get(e);t&&(t.unsubscribe(),this.scrollContainers.delete(e))}scrolled(e=Ag){return this._platform.isBrowser?new st(t=>{this._cleanupGlobalListener||(this._cleanupGlobalListener=this._ngZone.runOutsideAngular(()=>this._renderer.listen("document","scroll",()=>this._scrolled.next())));let r=e>0?this._scrolled.pipe(Eo(e)).subscribe(t):this._scrolled.subscribe(t);return this._scrolledCount++,()=>{r.unsubscribe(),this._scrolledCount--,this._scrolledCount||(this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0)}}):D()}ngOnDestroy(){this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0,this.scrollContainers.forEach((e,t)=>this.deregister(t)),this._scrolled.complete()}ancestorScrolled(e,t){let r=this.getAncestorScrollContainers(e);return this.scrolled(t).pipe(ae(o=>!o||r.indexOf(o)>-1))}getAncestorScrollContainers(e){let t=[];return this.scrollContainers.forEach((r,o)=>{this._scrollableContainsElement(o,e)&&t.push(o)}),t}_scrollableContainsElement(e,t){let r=ke(t),o=e.getElementRef().nativeElement;do if(r==o)return!0;while(r=r.parentElement);return!1}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Ig=20,Fn=(()=>{class n{_platform=l(ie);_listeners;_viewportSize;_change=new E;_document=l(N,{optional:!0});constructor(){let e=l(V),t=l(pe).createRenderer(null,null);e.runOutsideAngular(()=>{if(this._platform.isBrowser){let r=o=>this._change.next(o);this._listeners=[t.listen("window","resize",r),t.listen("window","orientationchange",r)]}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){this._listeners?.forEach(e=>e()),this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();let e={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),e}getViewportRect(){let e=this.getViewportScrollPosition(),{width:t,height:r}=this.getViewportSize();return{top:e.top,left:e.left,bottom:e.top+r,right:e.left+t,height:r,width:t}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};let e=this._document,t=this._getWindow(),r=e.documentElement,o=r.getBoundingClientRect(),a=-o.top||e.body.scrollTop||t.scrollY||r.scrollTop||0,s=-o.left||e.body.scrollLeft||t.scrollX||r.scrollLeft||0;return{top:a,left:s}}change(e=Ig){return e>0?this._change.pipe(Eo(e)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){let e=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:e.innerWidth,height:e.innerHeight}:{width:0,height:0}}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var oo=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({})}return n})(),Ss=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[At,oo,At,oo]})}return n})();var On=class{_attachedHost;attach(i){return this._attachedHost=i,i.attach(this)}detach(){let i=this._attachedHost;i!=null&&(this._attachedHost=null,i.detach())}get isAttached(){return this._attachedHost!=null}setAttachedHost(i){this._attachedHost=i}},Ms=class extends On{component;viewContainerRef;injector;componentFactoryResolver;projectableNodes;constructor(i,e,t,r,o){super(),this.component=i,this.viewContainerRef=e,this.injector=t,this.projectableNodes=o}},Pn=class extends On{templateRef;viewContainerRef;context;injector;constructor(i,e,t,r){super(),this.templateRef=i,this.viewContainerRef=e,this.context=t,this.injector=r}get origin(){return this.templateRef.elementRef}attach(i,e=this.context){return this.context=e,super.attach(i)}detach(){return this.context=void 0,super.detach()}},As=class extends On{element;constructor(i){super(),this.element=i instanceof H?i.nativeElement:i}},Is=class{_attachedPortal;_disposeFn;_isDisposed=!1;hasAttached(){return!!this._attachedPortal}attach(i){if(i instanceof Ms)return this._attachedPortal=i,this.attachComponentPortal(i);if(i instanceof Pn)return this._attachedPortal=i,this.attachTemplatePortal(i);if(this.attachDomPortal&&i instanceof As)return this._attachedPortal=i,this.attachDomPortal(i)}attachDomPortal=null;detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(i){this._disposeFn=i}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}};var ao=class extends Is{outletElement;_appRef;_defaultInjector;_document;constructor(i,e,t,r,o){super(),this.outletElement=i,this._appRef=t,this._defaultInjector=r,this._document=o}attachComponentPortal(i){let e;if(i.viewContainerRef){let t=i.injector||i.viewContainerRef.injector,r=t.get(Po,null,{optional:!0})||void 0;e=i.viewContainerRef.createComponent(i.component,{index:i.viewContainerRef.length,injector:t,ngModuleRef:r,projectableNodes:i.projectableNodes||void 0}),this.setDisposeFn(()=>e.destroy())}else{let t=this._appRef,r=i.injector||this._defaultInjector||se.NULL,o=r.get(Ne,t.injector);e=Kn(i.component,{elementInjector:r,environmentInjector:o,projectableNodes:i.projectableNodes||void 0}),t.attachView(e.hostView),this.setDisposeFn(()=>{t.viewCount>0&&t.detachView(e.hostView),e.destroy()})}return this.outletElement.appendChild(this._getComponentRootNode(e)),this._attachedPortal=i,e}attachTemplatePortal(i){let e=i.viewContainerRef,t=e.createEmbeddedView(i.templateRef,i.context,{injector:i.injector});return t.rootNodes.forEach(r=>this.outletElement.appendChild(r)),t.detectChanges(),this.setDisposeFn(()=>{let r=e.indexOf(t);r!==-1&&e.remove(r)}),this._attachedPortal=i,t}attachDomPortal=i=>{let e=i.element;e.parentNode;let t=this._document.createComment("dom-portal");e.parentNode.insertBefore(t,e),this.outletElement.appendChild(e),this._attachedPortal=i,super.setDisposeFn(()=>{t.parentNode&&t.parentNode.replaceChild(e,t)})};dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(i){return i.hostView.rootNodes[0]}};var yu=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({})}return n})();var Cu=qd(),so=class{_viewportRuler;_previousHTMLStyles={top:"",left:""};_previousScrollPosition;_isEnabled=!1;_document;constructor(i,e){this._viewportRuler=i,this._document=e}attach(){}enable(){if(this._canBeEnabled()){let i=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=i.style.left||"",this._previousHTMLStyles.top=i.style.top||"",i.style.left=ce(-this._previousScrollPosition.left),i.style.top=ce(-this._previousScrollPosition.top),i.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){let i=this._document.documentElement,e=this._document.body,t=i.style,r=e.style,o=t.scrollBehavior||"",a=r.scrollBehavior||"";this._isEnabled=!1,t.left=this._previousHTMLStyles.left,t.top=this._previousHTMLStyles.top,i.classList.remove("cdk-global-scrollblock"),Cu&&(t.scrollBehavior=r.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),Cu&&(t.scrollBehavior=o,r.scrollBehavior=a)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;let e=this._document.documentElement,t=this._viewportRuler.getViewportSize();return e.scrollHeight>t.height||e.scrollWidth>t.width}};var lo=class{_scrollDispatcher;_ngZone;_viewportRuler;_config;_scrollSubscription=null;_overlayRef;_initialScrollPosition;constructor(i,e,t,r){this._scrollDispatcher=i,this._ngZone=e,this._viewportRuler=t,this._config=r}attach(i){this._overlayRef,this._overlayRef=i}enable(){if(this._scrollSubscription)return;let i=this._scrollDispatcher.scrolled(0).pipe(ae(e=>!e||!this._overlayRef.overlayElement.contains(e.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=i.subscribe(()=>{let e=this._viewportRuler.getViewportScrollPosition().top;Math.abs(e-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=i.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}_detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}},Nn=class{enable(){}disable(){}attach(){}};function Rs(n,i){return i.some(e=>{let t=n.bottom<e.top,r=n.top>e.bottom,o=n.right<e.left,a=n.left>e.right;return t||r||o||a})}function wu(n,i){return i.some(e=>{let t=n.top<e.top,r=n.bottom>e.bottom,o=n.left<e.left,a=n.right>e.right;return t||r||o||a})}var co=class{_scrollDispatcher;_viewportRuler;_ngZone;_config;_scrollSubscription=null;_overlayRef;constructor(i,e,t,r){this._scrollDispatcher=i,this._viewportRuler=e,this._ngZone=t,this._config=r}attach(i){this._overlayRef,this._overlayRef=i}enable(){if(!this._scrollSubscription){let i=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(i).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){let e=this._overlayRef.overlayElement.getBoundingClientRect(),{width:t,height:r}=this._viewportRuler.getViewportSize();Rs(e,[{width:t,height:r,bottom:r,right:t,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}})}}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},Iu=(()=>{class n{_scrollDispatcher=l(bu);_viewportRuler=l(Fn);_ngZone=l(V);_document=l(N);constructor(){}noop=()=>new Nn;close=e=>new lo(this._scrollDispatcher,this._ngZone,this._viewportRuler,e);block=()=>new so(this._viewportRuler,this._document);reposition=e=>new co(this._scrollDispatcher,this._viewportRuler,this._ngZone,e);static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Ln=class{positionStrategy;scrollStrategy=new Nn;panelClass="";hasBackdrop=!1;backdropClass="cdk-overlay-dark-backdrop";width;height;minWidth;minHeight;maxWidth;maxHeight;direction;disposeOnNavigation=!1;constructor(i){if(i){let e=Object.keys(i);for(let t of e)i[t]!==void 0&&(this[t]=i[t])}}};var uo=class{connectionPair;scrollableViewProperties;constructor(i,e){this.connectionPair=i,this.scrollableViewProperties=e}};var Ru=(()=>{class n{_attachedOverlays=[];_document=l(N);_isAttached;constructor(){}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){let t=this._attachedOverlays.indexOf(e);t>-1&&this._attachedOverlays.splice(t,1),this._attachedOverlays.length===0&&this.detach()}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Tu=(()=>{class n extends Ru{_ngZone=l(V);_renderer=l(pe).createRenderer(null,null);_cleanupKeydown;add(e){super.add(e),this._isAttached||(this._ngZone.runOutsideAngular(()=>{this._cleanupKeydown=this._renderer.listen("body","keydown",this._keydownListener)}),this._isAttached=!0)}detach(){this._isAttached&&(this._cleanupKeydown?.(),this._isAttached=!1)}_keydownListener=e=>{let t=this._attachedOverlays;for(let r=t.length-1;r>-1;r--)if(t[r]._keydownEvents.observers.length>0){this._ngZone.run(()=>t[r]._keydownEvents.next(e));break}};static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),ku=(()=>{class n extends Ru{_platform=l(ie);_ngZone=l(V);_renderer=l(pe).createRenderer(null,null);_cursorOriginalValue;_cursorStyleIsSet=!1;_pointerDownEventTarget;_cleanups;add(e){if(super.add(e),!this._isAttached){let t=this._document.body,r={capture:!0};this._cleanups=this._ngZone.runOutsideAngular(()=>[Me(this._renderer,t,"pointerdown",this._pointerDownListener,r),Me(this._renderer,t,"click",this._clickListener,r),Me(this._renderer,t,"auxclick",this._clickListener,r),Me(this._renderer,t,"contextmenu",this._clickListener,r)]),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=t.style.cursor,t.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){this._isAttached&&(this._cleanups?.forEach(e=>e()),this._cleanups=void 0,this._platform.IOS&&this._cursorStyleIsSet&&(this._document.body.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1)}_pointerDownListener=e=>{this._pointerDownEventTarget=De(e)};_clickListener=e=>{let t=De(e),r=e.type==="click"&&this._pointerDownEventTarget?this._pointerDownEventTarget:t;this._pointerDownEventTarget=null;let o=this._attachedOverlays.slice();for(let a=o.length-1;a>-1;a--){let s=o[a];if(s._outsidePointerEvents.observers.length<1||!s.hasAttached())continue;if(Du(s.overlayElement,t)||Du(s.overlayElement,r))break;let c=s._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>c.next(e)):c.next(e)}};static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Du(n,i){let e=typeof ShadowRoot<"u"&&ShadowRoot,t=i;for(;t;){if(t===n)return!0;t=e&&t instanceof ShadowRoot?t.host:t.parentNode}return!1}var Fu=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["ng-component"]],hostAttrs:["cdk-overlay-style-loader",""],decls:0,vars:0,template:function(t,r){},styles:[`.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}
`],encapsulation:2,changeDetection:0})}return n})(),Ou=(()=>{class n{_platform=l(ie);_containerElement;_document=l(N);_styleLoader=l(He);constructor(){}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._loadStyles(),this._containerElement||this._createContainer(),this._containerElement}_createContainer(){let e="cdk-overlay-container";if(this._platform.isBrowser||hs()){let r=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let o=0;o<r.length;o++)r[o].remove()}let t=this._document.createElement("div");t.classList.add(e),hs()?t.setAttribute("platform","test"):this._platform.isBrowser||t.setAttribute("platform","server"),this._document.body.appendChild(t),this._containerElement=t}_loadStyles(){this._styleLoader.load(Fu)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Ts=class{_renderer;_ngZone;element;_cleanupClick;_cleanupTransitionEnd;_fallbackTimeout;constructor(i,e,t,r){this._renderer=e,this._ngZone=t,this.element=i.createElement("div"),this.element.classList.add("cdk-overlay-backdrop"),this._cleanupClick=e.listen(this.element,"click",r)}detach(){this._ngZone.runOutsideAngular(()=>{let i=this.element;clearTimeout(this._fallbackTimeout),this._cleanupTransitionEnd?.(),this._cleanupTransitionEnd=this._renderer.listen(i,"transitionend",this.dispose),this._fallbackTimeout=setTimeout(this.dispose,500),i.style.pointerEvents="none",i.classList.remove("cdk-overlay-backdrop-showing")})}dispose=()=>{clearTimeout(this._fallbackTimeout),this._cleanupClick?.(),this._cleanupTransitionEnd?.(),this._cleanupClick=this._cleanupTransitionEnd=this._fallbackTimeout=void 0,this.element.remove()}},mo=class{_portalOutlet;_host;_pane;_config;_ngZone;_keyboardDispatcher;_document;_location;_outsideClickDispatcher;_animationsDisabled;_injector;_renderer;_backdropClick=new E;_attachments=new E;_detachments=new E;_positionStrategy;_scrollStrategy;_locationChanges=ve.EMPTY;_backdropRef=null;_previousHostParent;_keydownEvents=new E;_outsidePointerEvents=new E;_renders=new E;_afterRenderRef;_afterNextRenderRef;constructor(i,e,t,r,o,a,s,c,m,h=!1,C,x){this._portalOutlet=i,this._host=e,this._pane=t,this._config=r,this._ngZone=o,this._keyboardDispatcher=a,this._document=s,this._location=c,this._outsideClickDispatcher=m,this._animationsDisabled=h,this._injector=C,this._renderer=x,r.scrollStrategy&&(this._scrollStrategy=r.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=r.positionStrategy,this._afterRenderRef=je(()=>Wn(()=>{this._renders.next()},{injector:this._injector}))}get overlayElement(){return this._pane}get backdropElement(){return this._backdropRef?.element||null}get hostElement(){return this._host}attach(i){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);let e=this._portalOutlet.attach(i);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._afterNextRenderRef?.destroy(),this._afterNextRenderRef=Ni(()=>{this.hasAttached()&&this.updatePosition()},{injector:this._injector}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),typeof e?.onDestroy=="function"&&e.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),e}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();let i=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenEmpty(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),i}dispose(){let i=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._backdropRef?.dispose(),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._afterNextRenderRef?.destroy(),this._previousHostParent=this._pane=this._host=this._backdropRef=null,i&&this._detachments.next(),this._detachments.complete(),this._afterRenderRef.destroy(),this._renders.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(i){i!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=i,this.hasAttached()&&(i.attach(this),this.updatePosition()))}updateSize(i){this._config=_(_({},this._config),i),this._updateElementSize()}setDirection(i){this._config=ee(_({},this._config),{direction:i}),this._updateElementDirection()}addPanelClass(i){this._pane&&this._toggleClasses(this._pane,i,!0)}removePanelClass(i){this._pane&&this._toggleClasses(this._pane,i,!1)}getDirection(){let i=this._config.direction;return i?typeof i=="string"?i:i.value:"ltr"}updateScrollStrategy(i){i!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=i,this.hasAttached()&&(i.attach(this),i.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;let i=this._pane.style;i.width=ce(this._config.width),i.height=ce(this._config.height),i.minWidth=ce(this._config.minWidth),i.minHeight=ce(this._config.minHeight),i.maxWidth=ce(this._config.maxWidth),i.maxHeight=ce(this._config.maxHeight)}_togglePointerEvents(i){this._pane.style.pointerEvents=i?"":"none"}_attachBackdrop(){let i="cdk-overlay-backdrop-showing";this._backdropRef?.dispose(),this._backdropRef=new Ts(this._document,this._renderer,this._ngZone,e=>{this._backdropClick.next(e)}),this._animationsDisabled&&this._backdropRef.element.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropRef.element,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropRef.element,this._host),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>this._backdropRef?.element.classList.add(i))}):this._backdropRef.element.classList.add(i)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){this._animationsDisabled?(this._backdropRef?.dispose(),this._backdropRef=null):this._backdropRef?.detach()}_toggleClasses(i,e,t){let r=xi(e||[]).filter(o=>!!o);r.length&&(t?i.classList.add(...r):i.classList.remove(...r))}_detachContentWhenEmpty(){this._ngZone.runOutsideAngular(()=>{let i=this._renders.pipe(de(lt(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||this._pane.children.length===0)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),i.unsubscribe())})})}_disposeScrollStrategy(){let i=this._scrollStrategy;i?.disable(),i?.detach?.()}},xu="cdk-overlay-connected-position-bounding-box",Rg=/([A-Za-z%]+)$/,ho=class{_viewportRuler;_document;_platform;_overlayContainer;_overlayRef;_isInitialRender;_lastBoundingBoxSize={width:0,height:0};_isPushed=!1;_canPush=!0;_growAfterOpen=!1;_hasFlexibleDimensions=!0;_positionLocked=!1;_originRect;_overlayRect;_viewportRect;_containerRect;_viewportMargin=0;_scrollables=[];_preferredPositions=[];_origin;_pane;_isDisposed;_boundingBox;_lastPosition;_lastScrollVisibility;_positionChanges=new E;_resizeSubscription=ve.EMPTY;_offsetX=0;_offsetY=0;_transformOriginSelector;_appliedPanelClasses=[];_previousPushAmount;positionChanges=this._positionChanges;get positions(){return this._preferredPositions}constructor(i,e,t,r,o){this._viewportRuler=e,this._document=t,this._platform=r,this._overlayContainer=o,this.setOrigin(i)}attach(i){this._overlayRef&&this._overlayRef,this._validatePositions(),i.hostElement.classList.add(xu),this._overlayRef=i,this._boundingBox=i.hostElement,this._pane=i.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition){this.reapplyLastPosition();return}this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let i=this._originRect,e=this._overlayRect,t=this._viewportRect,r=this._containerRect,o=[],a;for(let s of this._preferredPositions){let c=this._getOriginPoint(i,r,s),m=this._getOverlayPoint(c,e,s),h=this._getOverlayFit(m,e,t,s);if(h.isCompletelyWithinViewport){this._isPushed=!1,this._applyPosition(s,c);return}if(this._canFitWithFlexibleDimensions(h,m,t)){o.push({position:s,origin:c,overlayRect:e,boundingBoxRect:this._calculateBoundingBoxRect(c,s)});continue}(!a||a.overlayFit.visibleArea<h.visibleArea)&&(a={overlayFit:h,overlayPoint:m,originPoint:c,position:s,overlayRect:e})}if(o.length){let s=null,c=-1;for(let m of o){let h=m.boundingBoxRect.width*m.boundingBoxRect.height*(m.position.weight||1);h>c&&(c=h,s=m)}this._isPushed=!1,this._applyPosition(s.position,s.origin);return}if(this._canPush){this._isPushed=!0,this._applyPosition(a.position,a.originPoint);return}this._applyPosition(a.position,a.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&Qt(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(xu),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;let i=this._lastPosition;if(i){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let e=this._getOriginPoint(this._originRect,this._containerRect,i);this._applyPosition(i,e)}else this.apply()}withScrollableContainers(i){return this._scrollables=i,this}withPositions(i){return this._preferredPositions=i,i.indexOf(this._lastPosition)===-1&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(i){return this._viewportMargin=i,this}withFlexibleDimensions(i=!0){return this._hasFlexibleDimensions=i,this}withGrowAfterOpen(i=!0){return this._growAfterOpen=i,this}withPush(i=!0){return this._canPush=i,this}withLockedPosition(i=!0){return this._positionLocked=i,this}setOrigin(i){return this._origin=i,this}withDefaultOffsetX(i){return this._offsetX=i,this}withDefaultOffsetY(i){return this._offsetY=i,this}withTransformOriginOn(i){return this._transformOriginSelector=i,this}_getOriginPoint(i,e,t){let r;if(t.originX=="center")r=i.left+i.width/2;else{let a=this._isRtl()?i.right:i.left,s=this._isRtl()?i.left:i.right;r=t.originX=="start"?a:s}e.left<0&&(r-=e.left);let o;return t.originY=="center"?o=i.top+i.height/2:o=t.originY=="top"?i.top:i.bottom,e.top<0&&(o-=e.top),{x:r,y:o}}_getOverlayPoint(i,e,t){let r;t.overlayX=="center"?r=-e.width/2:t.overlayX==="start"?r=this._isRtl()?-e.width:0:r=this._isRtl()?0:-e.width;let o;return t.overlayY=="center"?o=-e.height/2:o=t.overlayY=="top"?0:-e.height,{x:i.x+r,y:i.y+o}}_getOverlayFit(i,e,t,r){let o=Su(e),{x:a,y:s}=i,c=this._getOffset(r,"x"),m=this._getOffset(r,"y");c&&(a+=c),m&&(s+=m);let h=0-a,C=a+o.width-t.width,x=0-s,A=s+o.height-t.height,L=this._subtractOverflows(o.width,h,C),w=this._subtractOverflows(o.height,x,A),B=L*w;return{visibleArea:B,isCompletelyWithinViewport:o.width*o.height===B,fitsInViewportVertically:w===o.height,fitsInViewportHorizontally:L==o.width}}_canFitWithFlexibleDimensions(i,e,t){if(this._hasFlexibleDimensions){let r=t.bottom-e.y,o=t.right-e.x,a=Eu(this._overlayRef.getConfig().minHeight),s=Eu(this._overlayRef.getConfig().minWidth),c=i.fitsInViewportVertically||a!=null&&a<=r,m=i.fitsInViewportHorizontally||s!=null&&s<=o;return c&&m}return!1}_pushOverlayOnScreen(i,e,t){if(this._previousPushAmount&&this._positionLocked)return{x:i.x+this._previousPushAmount.x,y:i.y+this._previousPushAmount.y};let r=Su(e),o=this._viewportRect,a=Math.max(i.x+r.width-o.width,0),s=Math.max(i.y+r.height-o.height,0),c=Math.max(o.top-t.top-i.y,0),m=Math.max(o.left-t.left-i.x,0),h=0,C=0;return r.width<=o.width?h=m||-a:h=i.x<this._viewportMargin?o.left-t.left-i.x:0,r.height<=o.height?C=c||-s:C=i.y<this._viewportMargin?o.top-t.top-i.y:0,this._previousPushAmount={x:h,y:C},{x:i.x+h,y:i.y+C}}_applyPosition(i,e){if(this._setTransformOrigin(i),this._setOverlayElementStyles(e,i),this._setBoundingBoxStyles(e,i),i.panelClass&&this._addPanelClasses(i.panelClass),this._positionChanges.observers.length){let t=this._getScrollVisibility();if(i!==this._lastPosition||!this._lastScrollVisibility||!Tg(this._lastScrollVisibility,t)){let r=new uo(i,t);this._positionChanges.next(r)}this._lastScrollVisibility=t}this._lastPosition=i,this._isInitialRender=!1}_setTransformOrigin(i){if(!this._transformOriginSelector)return;let e=this._boundingBox.querySelectorAll(this._transformOriginSelector),t,r=i.overlayY;i.overlayX==="center"?t="center":this._isRtl()?t=i.overlayX==="start"?"right":"left":t=i.overlayX==="start"?"left":"right";for(let o=0;o<e.length;o++)e[o].style.transformOrigin=`${t} ${r}`}_calculateBoundingBoxRect(i,e){let t=this._viewportRect,r=this._isRtl(),o,a,s;if(e.overlayY==="top")a=i.y,o=t.height-a+this._viewportMargin;else if(e.overlayY==="bottom")s=t.height-i.y+this._viewportMargin*2,o=t.height-s+this._viewportMargin;else{let A=Math.min(t.bottom-i.y+t.top,i.y),L=this._lastBoundingBoxSize.height;o=A*2,a=i.y-A,o>L&&!this._isInitialRender&&!this._growAfterOpen&&(a=i.y-L/2)}let c=e.overlayX==="start"&&!r||e.overlayX==="end"&&r,m=e.overlayX==="end"&&!r||e.overlayX==="start"&&r,h,C,x;if(m)x=t.width-i.x+this._viewportMargin*2,h=i.x-this._viewportMargin;else if(c)C=i.x,h=t.right-i.x;else{let A=Math.min(t.right-i.x+t.left,i.x),L=this._lastBoundingBoxSize.width;h=A*2,C=i.x-A,h>L&&!this._isInitialRender&&!this._growAfterOpen&&(C=i.x-L/2)}return{top:a,left:C,bottom:s,right:x,width:h,height:o}}_setBoundingBoxStyles(i,e){let t=this._calculateBoundingBoxRect(i,e);!this._isInitialRender&&!this._growAfterOpen&&(t.height=Math.min(t.height,this._lastBoundingBoxSize.height),t.width=Math.min(t.width,this._lastBoundingBoxSize.width));let r={};if(this._hasExactPosition())r.top=r.left="0",r.bottom=r.right=r.maxHeight=r.maxWidth="",r.width=r.height="100%";else{let o=this._overlayRef.getConfig().maxHeight,a=this._overlayRef.getConfig().maxWidth;r.height=ce(t.height),r.top=ce(t.top),r.bottom=ce(t.bottom),r.width=ce(t.width),r.left=ce(t.left),r.right=ce(t.right),e.overlayX==="center"?r.alignItems="center":r.alignItems=e.overlayX==="end"?"flex-end":"flex-start",e.overlayY==="center"?r.justifyContent="center":r.justifyContent=e.overlayY==="bottom"?"flex-end":"flex-start",o&&(r.maxHeight=ce(o)),a&&(r.maxWidth=ce(a))}this._lastBoundingBoxSize=t,Qt(this._boundingBox.style,r)}_resetBoundingBoxStyles(){Qt(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){Qt(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(i,e){let t={},r=this._hasExactPosition(),o=this._hasFlexibleDimensions,a=this._overlayRef.getConfig();if(r){let h=this._viewportRuler.getViewportScrollPosition();Qt(t,this._getExactOverlayY(e,i,h)),Qt(t,this._getExactOverlayX(e,i,h))}else t.position="static";let s="",c=this._getOffset(e,"x"),m=this._getOffset(e,"y");c&&(s+=`translateX(${c}px) `),m&&(s+=`translateY(${m}px)`),t.transform=s.trim(),a.maxHeight&&(r?t.maxHeight=ce(a.maxHeight):o&&(t.maxHeight="")),a.maxWidth&&(r?t.maxWidth=ce(a.maxWidth):o&&(t.maxWidth="")),Qt(this._pane.style,t)}_getExactOverlayY(i,e,t){let r={top:"",bottom:""},o=this._getOverlayPoint(e,this._overlayRect,i);if(this._isPushed&&(o=this._pushOverlayOnScreen(o,this._overlayRect,t)),i.overlayY==="bottom"){let a=this._document.documentElement.clientHeight;r.bottom=`${a-(o.y+this._overlayRect.height)}px`}else r.top=ce(o.y);return r}_getExactOverlayX(i,e,t){let r={left:"",right:""},o=this._getOverlayPoint(e,this._overlayRect,i);this._isPushed&&(o=this._pushOverlayOnScreen(o,this._overlayRect,t));let a;if(this._isRtl()?a=i.overlayX==="end"?"left":"right":a=i.overlayX==="end"?"right":"left",a==="right"){let s=this._document.documentElement.clientWidth;r.right=`${s-(o.x+this._overlayRect.width)}px`}else r.left=ce(o.x);return r}_getScrollVisibility(){let i=this._getOriginRect(),e=this._pane.getBoundingClientRect(),t=this._scrollables.map(r=>r.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:wu(i,t),isOriginOutsideView:Rs(i,t),isOverlayClipped:wu(e,t),isOverlayOutsideView:Rs(e,t)}}_subtractOverflows(i,...e){return e.reduce((t,r)=>t-Math.max(r,0),i)}_getNarrowedViewportRect(){let i=this._document.documentElement.clientWidth,e=this._document.documentElement.clientHeight,t=this._viewportRuler.getViewportScrollPosition();return{top:t.top+this._viewportMargin,left:t.left+this._viewportMargin,right:t.left+i-this._viewportMargin,bottom:t.top+e-this._viewportMargin,width:i-2*this._viewportMargin,height:e-2*this._viewportMargin}}_isRtl(){return this._overlayRef.getDirection()==="rtl"}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(i,e){return e==="x"?i.offsetX==null?this._offsetX:i.offsetX:i.offsetY==null?this._offsetY:i.offsetY}_validatePositions(){}_addPanelClasses(i){this._pane&&xi(i).forEach(e=>{e!==""&&this._appliedPanelClasses.indexOf(e)===-1&&(this._appliedPanelClasses.push(e),this._pane.classList.add(e))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(i=>{this._pane.classList.remove(i)}),this._appliedPanelClasses=[])}_getOriginRect(){let i=this._origin;if(i instanceof H)return i.nativeElement.getBoundingClientRect();if(i instanceof Element)return i.getBoundingClientRect();let e=i.width||0,t=i.height||0;return{top:i.y,bottom:i.y+t,left:i.x,right:i.x+e,height:t,width:e}}};function Qt(n,i){for(let e in i)i.hasOwnProperty(e)&&(n[e]=i[e]);return n}function Eu(n){if(typeof n!="number"&&n!=null){let[i,e]=n.split(Rg);return!e||e==="px"?parseFloat(i):null}return n||null}function Su(n){return{top:Math.floor(n.top),right:Math.floor(n.right),bottom:Math.floor(n.bottom),left:Math.floor(n.left),width:Math.floor(n.width),height:Math.floor(n.height)}}function Tg(n,i){return n===i?!0:n.isOriginClipped===i.isOriginClipped&&n.isOriginOutsideView===i.isOriginOutsideView&&n.isOverlayClipped===i.isOverlayClipped&&n.isOverlayOutsideView===i.isOverlayOutsideView}var Mu="cdk-global-overlay-wrapper",fo=class{_overlayRef;_cssPosition="static";_topOffset="";_bottomOffset="";_alignItems="";_xPosition="";_xOffset="";_width="";_height="";_isDisposed=!1;attach(i){let e=i.getConfig();this._overlayRef=i,this._width&&!e.width&&i.updateSize({width:this._width}),this._height&&!e.height&&i.updateSize({height:this._height}),i.hostElement.classList.add(Mu),this._isDisposed=!1}top(i=""){return this._bottomOffset="",this._topOffset=i,this._alignItems="flex-start",this}left(i=""){return this._xOffset=i,this._xPosition="left",this}bottom(i=""){return this._topOffset="",this._bottomOffset=i,this._alignItems="flex-end",this}right(i=""){return this._xOffset=i,this._xPosition="right",this}start(i=""){return this._xOffset=i,this._xPosition="start",this}end(i=""){return this._xOffset=i,this._xPosition="end",this}width(i=""){return this._overlayRef?this._overlayRef.updateSize({width:i}):this._width=i,this}height(i=""){return this._overlayRef?this._overlayRef.updateSize({height:i}):this._height=i,this}centerHorizontally(i=""){return this.left(i),this._xPosition="center",this}centerVertically(i=""){return this.top(i),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;let i=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement.style,t=this._overlayRef.getConfig(),{width:r,height:o,maxWidth:a,maxHeight:s}=t,c=(r==="100%"||r==="100vw")&&(!a||a==="100%"||a==="100vw"),m=(o==="100%"||o==="100vh")&&(!s||s==="100%"||s==="100vh"),h=this._xPosition,C=this._xOffset,x=this._overlayRef.getConfig().direction==="rtl",A="",L="",w="";c?w="flex-start":h==="center"?(w="center",x?L=C:A=C):x?h==="left"||h==="end"?(w="flex-end",A=C):(h==="right"||h==="start")&&(w="flex-start",L=C):h==="left"||h==="start"?(w="flex-start",A=C):(h==="right"||h==="end")&&(w="flex-end",L=C),i.position=this._cssPosition,i.marginLeft=c?"0":A,i.marginTop=m?"0":this._topOffset,i.marginBottom=this._bottomOffset,i.marginRight=c?"0":L,e.justifyContent=w,e.alignItems=m?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;let i=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement,t=e.style;e.classList.remove(Mu),t.justifyContent=t.alignItems=i.marginTop=i.marginBottom=i.marginLeft=i.marginRight=i.position="",this._overlayRef=null,this._isDisposed=!0}},Pu=(()=>{class n{_viewportRuler=l(Fn);_document=l(N);_platform=l(ie);_overlayContainer=l(Ou);constructor(){}global(){return new fo}flexibleConnectedTo(e){return new ho(e,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Rt=(()=>{class n{scrollStrategies=l(Iu);_overlayContainer=l(Ou);_positionBuilder=l(Pu);_keyboardDispatcher=l(Tu);_injector=l(se);_ngZone=l(V);_document=l(N);_directionality=l(Mt);_location=l(_t);_outsideClickDispatcher=l(ku);_animationsModuleType=l(he,{optional:!0});_idGenerator=l(Fe);_renderer=l(pe).createRenderer(null,null);_appRef;_styleLoader=l(He);constructor(){}create(e){this._styleLoader.load(Fu);let t=this._createHostElement(),r=this._createPaneElement(t),o=this._createPortalOutlet(r),a=new Ln(e);return a.direction=a.direction||this._directionality.value,new mo(o,t,r,a,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,this._animationsModuleType==="NoopAnimations",this._injector.get(Ne),this._renderer)}position(){return this._positionBuilder}_createPaneElement(e){let t=this._document.createElement("div");return t.id=this._idGenerator.getId("cdk-overlay-"),t.classList.add("cdk-overlay-pane"),e.appendChild(t),t}_createHostElement(){let e=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(e),e}_createPortalOutlet(e){return this._appRef||(this._appRef=this._injector.get(Lt)),new ao(e,null,this._appRef,this._injector,this._document)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),kg=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],Nu=new b("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{let n=l(Rt);return()=>n.scrollStrategies.reposition()}}),Ii=(()=>{class n{elementRef=l(H);constructor(){}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"]})}return n})(),po=(()=>{class n{_overlay=l(Rt);_dir=l(Mt,{optional:!0});_overlayRef;_templatePortal;_backdropSubscription=ve.EMPTY;_attachSubscription=ve.EMPTY;_detachSubscription=ve.EMPTY;_positionSubscription=ve.EMPTY;_offsetX;_offsetY;_position;_scrollStrategyFactory=l(Nu);_disposeOnNavigation=!1;_ngZone=l(V);origin;positions;positionStrategy;get offsetX(){return this._offsetX}set offsetX(e){this._offsetX=e,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(e){this._offsetY=e,this._position&&this._updatePositionStrategy(this._position)}width;height;minWidth;minHeight;backdropClass;panelClass;viewportMargin=0;scrollStrategy;open=!1;disableClose=!1;transformOriginSelector;hasBackdrop=!1;lockPosition=!1;flexibleDimensions=!1;growAfterOpen=!1;push=!1;get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(e){this._disposeOnNavigation=e}backdropClick=new Y;positionChange=new Y;attach=new Y;detach=new Y;overlayKeydown=new Y;overlayOutsideClick=new Y;constructor(){let e=l(Nt),t=l(Ke);this._templatePortal=new Pn(e,t),this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef?.dispose()}ngOnChanges(e){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef?.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),e.origin&&this.open&&this._position.apply()),e.open&&(this.open?this.attachOverlay():this.detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=kg);let e=this._overlayRef=this._overlay.create(this._buildConfig());this._attachSubscription=e.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=e.detachments().subscribe(()=>this.detach.emit()),e.keydownEvents().subscribe(t=>{this.overlayKeydown.next(t),t.keyCode===27&&!this.disableClose&&!Qe(t)&&(t.preventDefault(),this.detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(t=>{let r=this._getOriginElement(),o=De(t);(!r||r!==o&&!r.contains(o))&&this.overlayOutsideClick.next(t)})}_buildConfig(){let e=this._position=this.positionStrategy||this._createPositionStrategy(),t=new Ln({direction:this._dir||"ltr",positionStrategy:e,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||this.width===0)&&(t.width=this.width),(this.height||this.height===0)&&(t.height=this.height),(this.minWidth||this.minWidth===0)&&(t.minWidth=this.minWidth),(this.minHeight||this.minHeight===0)&&(t.minHeight=this.minHeight),this.backdropClass&&(t.backdropClass=this.backdropClass),this.panelClass&&(t.panelClass=this.panelClass),t}_updatePositionStrategy(e){let t=this.positions.map(r=>({originX:r.originX,originY:r.originY,overlayX:r.overlayX,overlayY:r.overlayY,offsetX:r.offsetX||this.offsetX,offsetY:r.offsetY||this.offsetY,panelClass:r.panelClass||void 0}));return e.setOrigin(this._getOrigin()).withPositions(t).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){let e=this._overlay.position().flexibleConnectedTo(this._getOrigin());return this._updatePositionStrategy(e),e}_getOrigin(){return this.origin instanceof Ii?this.origin.elementRef:this.origin}_getOriginElement(){return this.origin instanceof Ii?this.origin.elementRef.nativeElement:this.origin instanceof H?this.origin.nativeElement:typeof Element<"u"&&this.origin instanceof Element?this.origin:null}attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(e=>{this.backdropClick.emit(e)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe(Gs(()=>this.positionChange.observers.length>0)).subscribe(e=>{this._ngZone.run(()=>this.positionChange.emit(e)),this.positionChange.observers.length===0&&this._positionSubscription.unsubscribe()})),this.open=!0}detachOverlay(){this._overlayRef?.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.open=!1}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[0,"cdkConnectedOverlayOrigin","origin"],positions:[0,"cdkConnectedOverlayPositions","positions"],positionStrategy:[0,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[0,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[0,"cdkConnectedOverlayOffsetY","offsetY"],width:[0,"cdkConnectedOverlayWidth","width"],height:[0,"cdkConnectedOverlayHeight","height"],minWidth:[0,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[0,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[0,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[0,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[0,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[0,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[0,"cdkConnectedOverlayOpen","open"],disableClose:[0,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[0,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[2,"cdkConnectedOverlayHasBackdrop","hasBackdrop",q],lockPosition:[2,"cdkConnectedOverlayLockPosition","lockPosition",q],flexibleDimensions:[2,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",q],growAfterOpen:[2,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",q],push:[2,"cdkConnectedOverlayPush","push",q],disposeOnNavigation:[2,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",q]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],features:[Ce]})}return n})();function Fg(n){return()=>n.scrollStrategies.reposition()}var Og={provide:Nu,deps:[Rt],useFactory:Fg},ks=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({providers:[Rt,Og],imports:[At,yu,Ss,Ss]})}return n})();var Bn=class{_multiple;_emitChanges;compareWith;_selection=new Set;_deselectedToEmit=[];_selectedToEmit=[];_selected;get selected(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected}changed=new E;constructor(i=!1,e,t=!0,r){this._multiple=i,this._emitChanges=t,this.compareWith=r,e&&e.length&&(i?e.forEach(o=>this._markSelected(o)):this._markSelected(e[0]),this._selectedToEmit.length=0)}select(...i){this._verifyValueAssignment(i),i.forEach(t=>this._markSelected(t));let e=this._hasQueuedChanges();return this._emitChangeEvent(),e}deselect(...i){this._verifyValueAssignment(i),i.forEach(t=>this._unmarkSelected(t));let e=this._hasQueuedChanges();return this._emitChangeEvent(),e}setSelection(...i){this._verifyValueAssignment(i);let e=this.selected,t=new Set(i.map(o=>this._getConcreteValue(o)));i.forEach(o=>this._markSelected(o)),e.filter(o=>!t.has(this._getConcreteValue(o,t))).forEach(o=>this._unmarkSelected(o));let r=this._hasQueuedChanges();return this._emitChangeEvent(),r}toggle(i){return this.isSelected(i)?this.deselect(i):this.select(i)}clear(i=!0){this._unmarkAll();let e=this._hasQueuedChanges();return i&&this._emitChangeEvent(),e}isSelected(i){return this._selection.has(this._getConcreteValue(i))}isEmpty(){return this._selection.size===0}hasValue(){return!this.isEmpty()}sort(i){this._multiple&&this.selected&&this._selected.sort(i)}isMultipleSelection(){return this._multiple}_emitChangeEvent(){this._selected=null,(this._selectedToEmit.length||this._deselectedToEmit.length)&&(this.changed.next({source:this,added:this._selectedToEmit,removed:this._deselectedToEmit}),this._deselectedToEmit=[],this._selectedToEmit=[])}_markSelected(i){i=this._getConcreteValue(i),this.isSelected(i)||(this._multiple||this._unmarkAll(),this.isSelected(i)||this._selection.add(i),this._emitChanges&&this._selectedToEmit.push(i))}_unmarkSelected(i){i=this._getConcreteValue(i),this.isSelected(i)&&(this._selection.delete(i),this._emitChanges&&this._deselectedToEmit.push(i))}_unmarkAll(){this.isEmpty()||this._selection.forEach(i=>this._unmarkSelected(i))}_verifyValueAssignment(i){i.length>1&&this._multiple}_hasQueuedChanges(){return!!(this._deselectedToEmit.length||this._selectedToEmit.length)}_getConcreteValue(i,e){if(this.compareWith){e=e??this._selection;for(let t of e)if(this.compareWith(i,t))return t;return i}else return i}};var go=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,X]})}return n})();var Lu=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X]})}return n})();var Fs=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[go,X,Lu]})}return n})();var Pg=["trigger"],Ng=["panel"],Lg=[[["mat-select-trigger"]],"*"],Bg=["mat-select-trigger","*"];function Vg(n,i){if(n&1&&(u(0,"span",4),p(1),d()),n&2){let e=P();f(),zt(e.placeholder)}}function jg(n,i){n&1&&Z(0)}function zg(n,i){if(n&1&&(u(0,"span",11),p(1),d()),n&2){let e=P(2);f(),zt(e.triggerValue)}}function Ug(n,i){if(n&1&&(u(0,"span",5),y(1,jg,1,0)(2,zg,2,1,"span",11),d()),n&2){let e=P();f(),le(e.customTrigger?1:2)}}function Hg(n,i){if(n&1){let e=Bt();u(0,"div",12,1),re("keydown",function(r){Le(e);let o=P();return Be(o._handleKeydown(r))}),Z(2,1),d()}if(n&2){let e=P();ml("mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open ",e._getPanelTheme(),""),$("mat-select-panel-animations-enabled",!e._animationsDisabled),g("ngClass",e.panelClass),ne("id",e.id+"-panel")("aria-multiselectable",e.multiple)("aria-label",e.ariaLabel||null)("aria-labelledby",e._getPanelAriaLabelledby())}}var Os=new b("mat-select-scroll-strategy",{providedIn:"root",factory:()=>{let n=l(Rt);return()=>n.scrollStrategies.reposition()}});function Bu(n){return()=>n.scrollStrategies.reposition()}var Vu=new b("MAT_SELECT_CONFIG"),ju={provide:Os,deps:[Rt],useFactory:Bu},zu=new b("MatSelectTrigger"),_o=class{source;value;constructor(i,e){this.source=i,this.value=e}},Ps=(()=>{class n{_viewportRuler=l(Fn);_changeDetectorRef=l(qe);_elementRef=l(H);_dir=l(Mt,{optional:!0});_idGenerator=l(Fe);_renderer=l(Se);_parentFormField=l(Mi,{optional:!0});ngControl=l(rt,{self:!0,optional:!0});_liveAnnouncer=l(ss);_defaultOptions=l(Vu,{optional:!0});_animationsDisabled=l(he,{optional:!0})==="NoopAnimations";_initialized=new E;_cleanupDetach;options;optionGroups;customTrigger;_positions=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom",panelClass:"mat-mdc-select-panel-above"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom",panelClass:"mat-mdc-select-panel-above"}];_scrollOptionIntoView(e){let t=this.options.toArray()[e];if(t){let r=this.panel.nativeElement,o=_u(e,this.options,this.optionGroups),a=t._getHostElement();e===0&&o===1?r.scrollTop=0:r.scrollTop=vu(a.offsetTop,a.offsetHeight,r.scrollTop,r.offsetHeight)}}_positioningSettled(){this._scrollOptionIntoView(this._keyManager.activeItemIndex||0)}_getChangeEvent(e){return new _o(this,e)}_scrollStrategyFactory=l(Os);_panelOpen=!1;_compareWith=(e,t)=>e===t;_uid=this._idGenerator.getId("mat-select-");_triggerAriaLabelledBy=null;_previousControl;_destroy=new E;_errorStateTracker;stateChanges=new E;disableAutomaticLabeling=!0;userAriaDescribedBy;_selectionModel;_keyManager;_preferredOverlayOrigin;_overlayWidth;_onChange=()=>{};_onTouched=()=>{};_valueId=this._idGenerator.getId("mat-select-value-");_scrollStrategy;_overlayPanelClass=this._defaultOptions?.overlayPanelClass||"";get focused(){return this._focused||this._panelOpen}_focused=!1;controlType="mat-select";trigger;panel;_overlayDir;panelClass;disabled=!1;disableRipple=!1;tabIndex=0;get hideSingleSelectionIndicator(){return this._hideSingleSelectionIndicator}set hideSingleSelectionIndicator(e){this._hideSingleSelectionIndicator=e,this._syncParentProperties()}_hideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1;get placeholder(){return this._placeholder}set placeholder(e){this._placeholder=e,this.stateChanges.next()}_placeholder;get required(){return this._required??this.ngControl?.control?.hasValidator(W.required)??!1}set required(e){this._required=e,this.stateChanges.next()}_required;get multiple(){return this._multiple}set multiple(e){this._selectionModel,this._multiple=e}_multiple=!1;disableOptionCentering=this._defaultOptions?.disableOptionCentering??!1;get compareWith(){return this._compareWith}set compareWith(e){this._compareWith=e,this._selectionModel&&this._initializeSelection()}get value(){return this._value}set value(e){this._assignValue(e)&&this._onChange(e)}_value;ariaLabel="";ariaLabelledby;get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}typeaheadDebounceInterval;sortComparator;get id(){return this._id}set id(e){this._id=e||this._uid,this.stateChanges.next()}_id;get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}panelWidth=this._defaultOptions&&typeof this._defaultOptions.panelWidth<"u"?this._defaultOptions.panelWidth:"auto";canSelectNullableOptions=this._defaultOptions?.canSelectNullableOptions??!1;optionSelectionChanges=ki(()=>{let e=this.options;return e?e.changes.pipe(Ze(e),ye(()=>lt(...e.map(t=>t.onSelectionChange)))):this._initialized.pipe(ye(()=>this.optionSelectionChanges))});openedChange=new Y;_openedStream=this.openedChange.pipe(ae(e=>e),M(()=>{}));_closedStream=this.openedChange.pipe(ae(e=>!e),M(()=>{}));selectionChange=new Y;valueChange=new Y;constructor(){let e=l(to),t=l(yn,{optional:!0}),r=l(ot,{optional:!0}),o=l(new $n("tabindex"),{optional:!0});this.ngControl&&(this.ngControl.valueAccessor=this),this._defaultOptions?.typeaheadDebounceInterval!=null&&(this.typeaheadDebounceInterval=this._defaultOptions.typeaheadDebounceInterval),this._errorStateTracker=new Ai(e,this.ngControl,r,t,this.stateChanges),this._scrollStrategy=this._scrollStrategyFactory(),this.tabIndex=o==null?0:parseInt(o)||0,this.id=this.id}ngOnInit(){this._selectionModel=new Bn(this.multiple),this.stateChanges.next(),this._viewportRuler.change().pipe(de(this._destroy)).subscribe(()=>{this.panelOpen&&(this._overlayWidth=this._getOverlayWidth(this._preferredOverlayOrigin),this._changeDetectorRef.detectChanges())})}ngAfterContentInit(){this._initialized.next(),this._initialized.complete(),this._initKeyManager(),this._selectionModel.changed.pipe(de(this._destroy)).subscribe(e=>{e.added.forEach(t=>t.select()),e.removed.forEach(t=>t.deselect())}),this.options.changes.pipe(Ze(null),de(this._destroy)).subscribe(()=>{this._resetOptions(),this._initializeSelection()})}ngDoCheck(){let e=this._getTriggerAriaLabelledby(),t=this.ngControl;if(e!==this._triggerAriaLabelledBy){let r=this._elementRef.nativeElement;this._triggerAriaLabelledBy=e,e?r.setAttribute("aria-labelledby",e):r.removeAttribute("aria-labelledby")}t&&(this._previousControl!==t.control&&(this._previousControl!==void 0&&t.disabled!==null&&t.disabled!==this.disabled&&(this.disabled=t.disabled),this._previousControl=t.control),this.updateErrorState())}ngOnChanges(e){(e.disabled||e.userAriaDescribedBy)&&this.stateChanges.next(),e.typeaheadDebounceInterval&&this._keyManager&&this._keyManager.withTypeAhead(this.typeaheadDebounceInterval)}ngOnDestroy(){this._cleanupDetach?.(),this._keyManager?.destroy(),this._destroy.next(),this._destroy.complete(),this.stateChanges.complete(),this._clearFromModal()}toggle(){this.panelOpen?this.close():this.open()}open(){this._canOpen()&&(this._parentFormField&&(this._preferredOverlayOrigin=this._parentFormField.getConnectedOverlayOrigin()),this._cleanupDetach?.(),this._overlayWidth=this._getOverlayWidth(this._preferredOverlayOrigin),this._applyModalPanelOwnership(),this._panelOpen=!0,this._overlayDir.positionChange.pipe(Pe(1)).subscribe(()=>{this._changeDetectorRef.detectChanges(),this._positioningSettled()}),this._overlayDir.attachOverlay(),this._keyManager.withHorizontalOrientation(null),this._highlightCorrectOption(),this._changeDetectorRef.markForCheck(),this.stateChanges.next(),Promise.resolve().then(()=>this.openedChange.emit(!0)))}_trackedModal=null;_applyModalPanelOwnership(){let e=this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal="true"]');if(!e)return;let t=`${this.id}-panel`;this._trackedModal&&ms(this._trackedModal,"aria-owns",t),jd(e,"aria-owns",t),this._trackedModal=e}_clearFromModal(){if(!this._trackedModal)return;let e=`${this.id}-panel`;ms(this._trackedModal,"aria-owns",e),this._trackedModal=null}close(){this._panelOpen&&(this._panelOpen=!1,this._exitAndDetach(),this._keyManager.withHorizontalOrientation(this._isRtl()?"rtl":"ltr"),this._changeDetectorRef.markForCheck(),this._onTouched(),this.stateChanges.next(),Promise.resolve().then(()=>this.openedChange.emit(!1)))}_exitAndDetach(){if(this._animationsDisabled||!this.panel){this._detachOverlay();return}this._cleanupDetach?.(),this._cleanupDetach=()=>{t(),clearTimeout(r),this._cleanupDetach=void 0};let e=this.panel.nativeElement,t=this._renderer.listen(e,"animationend",o=>{o.animationName==="_mat-select-exit"&&(this._cleanupDetach?.(),this._detachOverlay())}),r=setTimeout(()=>{this._cleanupDetach?.(),this._detachOverlay()},200);e.classList.add("mat-select-panel-exit")}_detachOverlay(){this._overlayDir.detachOverlay(),this._changeDetectorRef.markForCheck()}writeValue(e){this._assignValue(e)}registerOnChange(e){this._onChange=e}registerOnTouched(e){this._onTouched=e}setDisabledState(e){this.disabled=e,this._changeDetectorRef.markForCheck(),this.stateChanges.next()}get panelOpen(){return this._panelOpen}get selected(){return this.multiple?this._selectionModel?.selected||[]:this._selectionModel?.selected[0]}get triggerValue(){if(this.empty)return"";if(this._multiple){let e=this._selectionModel.selected.map(t=>t.viewValue);return this._isRtl()&&e.reverse(),e.join(", ")}return this._selectionModel.selected[0].viewValue}updateErrorState(){this._errorStateTracker.updateErrorState()}_isRtl(){return this._dir?this._dir.value==="rtl":!1}_handleKeydown(e){this.disabled||(this.panelOpen?this._handleOpenKeydown(e):this._handleClosedKeydown(e))}_handleClosedKeydown(e){let t=e.keyCode,r=t===40||t===38||t===37||t===39,o=t===13||t===32,a=this._keyManager;if(!a.isTyping()&&o&&!Qe(e)||(this.multiple||e.altKey)&&r)e.preventDefault(),this.open();else if(!this.multiple){let s=this.selected;a.onKeydown(e);let c=this.selected;c&&s!==c&&this._liveAnnouncer.announce(c.viewValue,1e4)}}_handleOpenKeydown(e){let t=this._keyManager,r=e.keyCode,o=r===40||r===38,a=t.isTyping();if(o&&e.altKey)e.preventDefault(),this.close();else if(!a&&(r===13||r===32)&&t.activeItem&&!Qe(e))e.preventDefault(),t.activeItem._selectViaInteraction();else if(!a&&this._multiple&&r===65&&e.ctrlKey){e.preventDefault();let s=this.options.some(c=>!c.disabled&&!c.selected);this.options.forEach(c=>{c.disabled||(s?c.select():c.deselect())})}else{let s=t.activeItemIndex;t.onKeydown(e),this._multiple&&o&&e.shiftKey&&t.activeItem&&t.activeItemIndex!==s&&t.activeItem._selectViaInteraction()}}_handleOverlayKeydown(e){e.keyCode===27&&!Qe(e)&&(e.preventDefault(),this.close())}_onFocus(){this.disabled||(this._focused=!0,this.stateChanges.next())}_onBlur(){this._focused=!1,this._keyManager?.cancelTypeahead(),!this.disabled&&!this.panelOpen&&(this._onTouched(),this._changeDetectorRef.markForCheck(),this.stateChanges.next())}_getPanelTheme(){return this._parentFormField?`mat-${this._parentFormField.color}`:""}get empty(){return!this._selectionModel||this._selectionModel.isEmpty()}_initializeSelection(){Promise.resolve().then(()=>{this.ngControl&&(this._value=this.ngControl.value),this._setSelectionByValue(this._value),this.stateChanges.next()})}_setSelectionByValue(e){if(this.options.forEach(t=>t.setInactiveStyles()),this._selectionModel.clear(),this.multiple&&e)Array.isArray(e),e.forEach(t=>this._selectOptionByValue(t)),this._sortValues();else{let t=this._selectOptionByValue(e);t?this._keyManager.updateActiveItem(t):this.panelOpen||this._keyManager.updateActiveItem(-1)}this._changeDetectorRef.markForCheck()}_selectOptionByValue(e){let t=this.options.find(r=>{if(this._selectionModel.isSelected(r))return!1;try{return(r.value!=null||this.canSelectNullableOptions)&&this._compareWith(r.value,e)}catch{return!1}});return t&&this._selectionModel.select(t),t}_assignValue(e){return e!==this._value||this._multiple&&Array.isArray(e)?(this.options&&this._setSelectionByValue(e),this._value=e,!0):!1}_skipPredicate=e=>this.panelOpen?!1:e.disabled;_getOverlayWidth(e){return this.panelWidth==="auto"?(e instanceof Ii?e.elementRef:e||this._elementRef).nativeElement.getBoundingClientRect().width:this.panelWidth===null?"":this.panelWidth}_syncParentProperties(){if(this.options)for(let e of this.options)e._changeDetectorRef.markForCheck()}_initKeyManager(){this._keyManager=new An(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl()?"rtl":"ltr").withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(["shiftKey"]).skipPredicate(this._skipPredicate),this._keyManager.tabOut.subscribe(()=>{this.panelOpen&&(!this.multiple&&this._keyManager.activeItem&&this._keyManager.activeItem._selectViaInteraction(),this.focus(),this.close())}),this._keyManager.change.subscribe(()=>{this._panelOpen&&this.panel?this._scrollOptionIntoView(this._keyManager.activeItemIndex||0):!this._panelOpen&&!this.multiple&&this._keyManager.activeItem&&this._keyManager.activeItem._selectViaInteraction()})}_resetOptions(){let e=lt(this.options.changes,this._destroy);this.optionSelectionChanges.pipe(de(e)).subscribe(t=>{this._onSelect(t.source,t.isUserInput),t.isUserInput&&!this.multiple&&this._panelOpen&&(this.close(),this.focus())}),lt(...this.options.map(t=>t._stateChanges)).pipe(de(e)).subscribe(()=>{this._changeDetectorRef.detectChanges(),this.stateChanges.next()})}_onSelect(e,t){let r=this._selectionModel.isSelected(e);!this.canSelectNullableOptions&&e.value==null&&!this._multiple?(e.deselect(),this._selectionModel.clear(),this.value!=null&&this._propagateChanges(e.value)):(r!==e.selected&&(e.selected?this._selectionModel.select(e):this._selectionModel.deselect(e)),t&&this._keyManager.setActiveItem(e),this.multiple&&(this._sortValues(),t&&this.focus())),r!==this._selectionModel.isSelected(e)&&this._propagateChanges(),this.stateChanges.next()}_sortValues(){if(this.multiple){let e=this.options.toArray();this._selectionModel.sort((t,r)=>this.sortComparator?this.sortComparator(t,r,e):e.indexOf(t)-e.indexOf(r)),this.stateChanges.next()}}_propagateChanges(e){let t;this.multiple?t=this.selected.map(r=>r.value):t=this.selected?this.selected.value:e,this._value=t,this.valueChange.emit(t),this._onChange(t),this.selectionChange.emit(this._getChangeEvent(t)),this._changeDetectorRef.markForCheck()}_highlightCorrectOption(){if(this._keyManager)if(this.empty){let e=-1;for(let t=0;t<this.options.length;t++)if(!this.options.get(t).disabled){e=t;break}this._keyManager.setActiveItem(e)}else this._keyManager.setActiveItem(this._selectionModel.selected[0])}_canOpen(){return!this._panelOpen&&!this.disabled&&this.options?.length>0&&!!this._overlayDir}focus(e){this._elementRef.nativeElement.focus(e)}_getPanelAriaLabelledby(){if(this.ariaLabel)return null;let e=this._parentFormField?.getLabelId()||null,t=e?e+" ":"";return this.ariaLabelledby?t+this.ariaLabelledby:e}_getAriaActiveDescendant(){return this.panelOpen&&this._keyManager&&this._keyManager.activeItem?this._keyManager.activeItem.id:null}_getTriggerAriaLabelledby(){if(this.ariaLabel)return null;let e=this._parentFormField?.getLabelId()||"";return this.ariaLabelledby&&(e+=" "+this.ariaLabelledby),e||(e=this._valueId),e}setDescribedByIds(e){e.length?this._elementRef.nativeElement.setAttribute("aria-describedby",e.join(" ")):this._elementRef.nativeElement.removeAttribute("aria-describedby")}onContainerClick(){this.focus(),this.open()}get shouldLabelFloat(){return this.panelOpen||!this.empty||this.focused&&!!this.placeholder}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-select"]],contentQueries:function(t,r,o){if(t&1&&(Ve(o,zu,5),Ve(o,kn,5),Ve(o,Es,5)),t&2){let a;Q(a=J())&&(r.customTrigger=a.first),Q(a=J())&&(r.options=a),Q(a=J())&&(r.optionGroups=a)}},viewQuery:function(t,r){if(t&1&&(ue(Pg,5),ue(Ng,5),ue(po,5)),t&2){let o;Q(o=J())&&(r.trigger=o.first),Q(o=J())&&(r.panel=o.first),Q(o=J())&&(r._overlayDir=o.first)}},hostAttrs:["role","combobox","aria-haspopup","listbox",1,"mat-mdc-select"],hostVars:19,hostBindings:function(t,r){t&1&&re("keydown",function(a){return r._handleKeydown(a)})("focus",function(){return r._onFocus()})("blur",function(){return r._onBlur()}),t&2&&(ne("id",r.id)("tabindex",r.disabled?-1:r.tabIndex)("aria-controls",r.panelOpen?r.id+"-panel":null)("aria-expanded",r.panelOpen)("aria-label",r.ariaLabel||null)("aria-required",r.required.toString())("aria-disabled",r.disabled.toString())("aria-invalid",r.errorState)("aria-activedescendant",r._getAriaActiveDescendant()),$("mat-mdc-select-disabled",r.disabled)("mat-mdc-select-invalid",r.errorState)("mat-mdc-select-required",r.required)("mat-mdc-select-empty",r.empty)("mat-mdc-select-multiple",r.multiple))},inputs:{userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],panelClass:"panelClass",disabled:[2,"disabled","disabled",q],disableRipple:[2,"disableRipple","disableRipple",q],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:pt(e)],hideSingleSelectionIndicator:[2,"hideSingleSelectionIndicator","hideSingleSelectionIndicator",q],placeholder:"placeholder",required:[2,"required","required",q],multiple:[2,"multiple","multiple",q],disableOptionCentering:[2,"disableOptionCentering","disableOptionCentering",q],compareWith:"compareWith",value:"value",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],errorStateMatcher:"errorStateMatcher",typeaheadDebounceInterval:[2,"typeaheadDebounceInterval","typeaheadDebounceInterval",pt],sortComparator:"sortComparator",id:"id",panelWidth:"panelWidth",canSelectNullableOptions:[2,"canSelectNullableOptions","canSelectNullableOptions",q]},outputs:{openedChange:"openedChange",_openedStream:"opened",_closedStream:"closed",selectionChange:"selectionChange",valueChange:"valueChange"},exportAs:["matSelect"],features:[fe([{provide:Si,useExisting:n},{provide:xs,useExisting:n}]),Ce],ngContentSelectors:Bg,decls:11,vars:9,consts:[["fallbackOverlayOrigin","cdkOverlayOrigin","trigger",""],["panel",""],["cdk-overlay-origin","",1,"mat-mdc-select-trigger",3,"click"],[1,"mat-mdc-select-value"],[1,"mat-mdc-select-placeholder","mat-mdc-select-min-line"],[1,"mat-mdc-select-value-text"],[1,"mat-mdc-select-arrow-wrapper"],[1,"mat-mdc-select-arrow"],["viewBox","0 0 24 24","width","24px","height","24px","focusable","false","aria-hidden","true"],["d","M7 10l5 5 5-5z"],["cdk-connected-overlay","","cdkConnectedOverlayLockPosition","","cdkConnectedOverlayHasBackdrop","","cdkConnectedOverlayBackdropClass","cdk-overlay-transparent-backdrop",3,"detach","backdropClick","overlayKeydown","cdkConnectedOverlayDisableClose","cdkConnectedOverlayPanelClass","cdkConnectedOverlayScrollStrategy","cdkConnectedOverlayOrigin","cdkConnectedOverlayPositions","cdkConnectedOverlayWidth","cdkConnectedOverlayFlexibleDimensions"],[1,"mat-mdc-select-min-line"],["role","listbox","tabindex","-1",3,"keydown","ngClass"]],template:function(t,r){if(t&1){let o=Bt();ge(Lg),u(0,"div",2,0),re("click",function(){return Le(o),Be(r.open())}),u(3,"div",3),y(4,Vg,2,1,"span",4)(5,Ug,3,1,"span",5),d(),u(6,"div",6)(7,"div",7),Fi(),u(8,"svg",8),O(9,"path",9),d()()()(),y(10,Hg,3,10,"ng-template",10),re("detach",function(){return Le(o),Be(r.close())})("backdropClick",function(){return Le(o),Be(r.close())})("overlayKeydown",function(s){return Le(o),Be(r._handleOverlayKeydown(s))})}if(t&2){let o=jt(1);f(3),ne("id",r._valueId),f(),le(r.empty?4:5),f(6),g("cdkConnectedOverlayDisableClose",!0)("cdkConnectedOverlayPanelClass",r._overlayPanelClass)("cdkConnectedOverlayScrollStrategy",r._scrollStrategy)("cdkConnectedOverlayOrigin",r._preferredOverlayOrigin||o)("cdkConnectedOverlayPositions",r._positions)("cdkConnectedOverlayWidth",r._overlayWidth)("cdkConnectedOverlayFlexibleDimensions",!0)}},dependencies:[Ii,po,Ho],styles:[`@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:" ";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}
`],encapsulation:2,changeDetection:0})}return n})();var Ns=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({providers:[ju],imports:[ks,Fs,X,oo,It,Fs,X]})}return n})();var Gg={capture:!0},Wg=["focus","mousedown","mouseenter","touchstart"],Ls="mat-ripple-loader-uninitialized",Bs="mat-ripple-loader-class-name",Uu="mat-ripple-loader-centered",vo="mat-ripple-loader-disabled",Hu=(()=>{class n{_document=l(N);_animationMode=l(he,{optional:!0});_globalRippleOptions=l(ws,{optional:!0});_platform=l(ie);_ngZone=l(V);_injector=l(se);_eventCleanups;_hosts=new Map;constructor(){let e=l(pe).createRenderer(null,null);this._eventCleanups=this._ngZone.runOutsideAngular(()=>Wg.map(t=>Me(e,this._document,t,this._onInteraction,Gg)))}ngOnDestroy(){let e=this._hosts.keys();for(let t of e)this.destroyRipple(t);this._eventCleanups.forEach(t=>t())}configureRipple(e,t){e.setAttribute(Ls,this._globalRippleOptions?.namespace??""),(t.className||!e.hasAttribute(Bs))&&e.setAttribute(Bs,t.className||""),t.centered&&e.setAttribute(Uu,""),t.disabled&&e.setAttribute(vo,"")}setDisabled(e,t){let r=this._hosts.get(e);r?(r.target.rippleDisabled=t,!t&&!r.hasSetUpEvents&&(r.hasSetUpEvents=!0,r.renderer.setupTriggerEvents(e))):t?e.setAttribute(vo,""):e.removeAttribute(vo)}_onInteraction=e=>{let t=De(e);if(t instanceof HTMLElement){let r=t.closest(`[${Ls}="${this._globalRippleOptions?.namespace??""}"]`);r&&this._createRipple(r)}};_createRipple(e){if(!this._document||this._hosts.has(e))return;e.querySelector(".mat-ripple")?.remove();let t=this._document.createElement("span");t.classList.add("mat-ripple",e.getAttribute(Bs)),e.append(t);let r=this._animationMode==="NoopAnimations",o=this._globalRippleOptions,a=r?0:o?.animation?.enterDuration??Rn.enterDuration,s=r?0:o?.animation?.exitDuration??Rn.exitDuration,c={rippleDisabled:r||o?.disabled||e.hasAttribute(vo),rippleConfig:{centered:e.hasAttribute(Uu),terminateOnPointerUp:o?.terminateOnPointerUp,animation:{enterDuration:a,exitDuration:s}}},m=new Tn(c,this._ngZone,t,this._platform,this._injector),h=!c.rippleDisabled;h&&m.setupTriggerEvents(e),this._hosts.set(e,{target:c,renderer:m,hasSetUpEvents:h}),e.removeAttribute(Ls)}destroyRipple(e){let t=this._hosts.get(e);t&&(t.renderer._removeTriggerEvents(),this._hosts.delete(e))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var qg=new b("MAT_BUTTON_CONFIG");var Yg=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}],$u=(()=>{class n{_elementRef=l(H);_ngZone=l(V);_animationMode=l(he,{optional:!0});_focusMonitor=l(os);_rippleLoader=l(Hu);_isFab=!1;color;get disableRipple(){return this._disableRipple}set disableRipple(e){this._disableRipple=e,this._updateRippleDisabled()}_disableRipple=!1;get disabled(){return this._disabled}set disabled(e){this._disabled=e,this._updateRippleDisabled()}_disabled=!1;ariaDisabled;disabledInteractive;constructor(){l(He).load(io);let e=l(qg,{optional:!0}),t=this._elementRef.nativeElement,r=t.classList;this.disabledInteractive=e?.disabledInteractive??!1,this.color=e?.color??null,this._rippleLoader?.configureRipple(t,{className:"mat-mdc-button-ripple"});for(let{attribute:o,mdcClasses:a}of Yg)t.hasAttribute(o)&&r.add(...a)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(e="program",t){e?this._focusMonitor.focusVia(this._elementRef.nativeElement,e,t):this._elementRef.nativeElement.focus(t)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",q],disabled:[2,"disabled","disabled",q],ariaDisabled:[2,"aria-disabled","ariaDisabled",q],disabledInteractive:[2,"disabledInteractive","disabledInteractive",q]}})}return n})();var Zg=["mat-button",""],Kg=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],Xg=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var Gu=(()=>{class n extends $u{static \u0275fac=(()=>{let e;return function(r){return(e||(e=Ee(n)))(r||n)}})();static \u0275cmp=U({type:n,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(t,r){t&2&&(ne("disabled",r._getDisabledAttribute())("aria-disabled",r._getAriaDisabled()),Je(r.color?"mat-"+r.color:""),$("mat-mdc-button-disabled",r.disabled)("mat-mdc-button-disabled-interactive",r.disabledInteractive)("_mat-animation-noopable",r._animationMode==="NoopAnimations")("mat-unthemed",!r.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[we],attrs:Zg,ngContentSelectors:Xg,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(t,r){t&1&&(ge(Kg),O(0,"span",0),Z(1),u(2,"span",1),Z(3,1),d(),Z(4,2),O(5,"span",2)(6,"span",3)),t&2&&$("mdc-button__ripple",!r._isFab)("mdc-fab__ripple",r._isFab)},styles:[`.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:"";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return n})();var Wu=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,go,X]})}return n})();var Jg=["determinateSpinner"];function e_(n,i){if(n&1&&(Fi(),u(0,"svg",11),O(1,"circle",12),d()),n&2){let e=P();ne("viewBox",e._viewBox()),f(),Vi("stroke-dasharray",e._strokeCircumference(),"px")("stroke-dashoffset",e._strokeCircumference()/2,"px")("stroke-width",e._circleStrokeWidth(),"%"),ne("r",e._circleRadius())}}var t_=new b("mat-progress-spinner-default-options",{providedIn:"root",factory:i_});function i_(){return{diameter:qu}}var qu=100,n_=10,Yu=(()=>{class n{_elementRef=l(H);_noopAnimations;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;_defaultColor="primary";_determinateCircle;constructor(){let e=l(he,{optional:!0}),t=l(t_);this._noopAnimations=e==="NoopAnimations"&&!!t&&!t._forceAnimations,this.mode=this._elementRef.nativeElement.nodeName.toLowerCase()==="mat-spinner"?"indeterminate":"determinate",t&&(t.color&&(this.color=this._defaultColor=t.color),t.diameter&&(this.diameter=t.diameter),t.strokeWidth&&(this.strokeWidth=t.strokeWidth))}mode;get value(){return this.mode==="determinate"?this._value:0}set value(e){this._value=Math.max(0,Math.min(100,e||0))}_value=0;get diameter(){return this._diameter}set diameter(e){this._diameter=e||0}_diameter=qu;get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(e){this._strokeWidth=e||0}_strokeWidth;_circleRadius(){return(this.diameter-n_)/2}_viewBox(){let e=this._circleRadius()*2+this.strokeWidth;return`0 0 ${e} ${e}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return this.mode==="determinate"?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(t,r){if(t&1&&ue(Jg,5),t&2){let o;Q(o=J())&&(r._determinateCircle=o.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(t,r){t&2&&(ne("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow",r.mode==="determinate"?r.value:null)("mode",r.mode),Je("mat-"+r.color),Vi("width",r.diameter,"px")("height",r.diameter,"px")("--mdc-circular-progress-size",r.diameter+"px")("--mdc-circular-progress-active-indicator-width",r.diameter+"px"),$("_mat-animation-noopable",r._noopAnimations)("mdc-circular-progress--indeterminate",r.mode==="indeterminate"))},inputs:{color:"color",mode:"mode",value:[2,"value","value",pt],diameter:[2,"diameter","diameter",pt],strokeWidth:[2,"strokeWidth","strokeWidth",pt]},exportAs:["matProgressSpinner"],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(t,r){if(t&1&&(y(0,e_,2,8,"ng-template",null,0,Yn),u(2,"div",2,1),Fi(),u(4,"svg",3),O(5,"circle",4),d()(),Ys(),u(6,"div",5)(7,"div",6)(8,"div",7),qn(9,8),d(),u(10,"div",9),qn(11,8),d(),u(12,"div",10),qn(13,8),d()()()),t&2){let o=jt(1);f(4),ne("viewBox",r._viewBox()),f(),Vi("stroke-dasharray",r._strokeCircumference(),"px")("stroke-dashoffset",r._strokeDashOffset(),"px")("stroke-width",r._circleStrokeWidth(),"%"),ne("r",r._circleRadius()),f(4),g("ngTemplateOutlet",o),f(2),g("ngTemplateOutlet",o),f(2),g("ngTemplateOutlet",o)}},dependencies:[zi],styles:[`.mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}
`],encapsulation:2,changeDetection:0})}return n})();var Zu=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X]})}return n})();var o_=["*",[["mat-toolbar-row"]]],a_=["*","mat-toolbar-row"],Vs=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275dir=I({type:n,selectors:[["mat-toolbar-row"]],hostAttrs:[1,"mat-toolbar-row"],exportAs:["matToolbarRow"]})}return n})(),Ku=(()=>{class n{_elementRef=l(H);_platform=l(ie);_document=l(N);color;_toolbarRows;constructor(){}ngAfterViewInit(){this._platform.isBrowser&&(this._checkToolbarMixedModes(),this._toolbarRows.changes.subscribe(()=>this._checkToolbarMixedModes()))}_checkToolbarMixedModes(){this._toolbarRows.length}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-toolbar"]],contentQueries:function(t,r,o){if(t&1&&Ve(o,Vs,5),t&2){let a;Q(a=J())&&(r._toolbarRows=a)}},hostAttrs:[1,"mat-toolbar"],hostVars:6,hostBindings:function(t,r){t&2&&(Je(r.color?"mat-"+r.color:""),$("mat-toolbar-multiple-rows",r._toolbarRows.length>0)("mat-toolbar-single-row",r._toolbarRows.length===0))},inputs:{color:"color"},exportAs:["matToolbar"],ngContentSelectors:a_,decls:2,vars:0,template:function(t,r){t&1&&(ge(o_),Z(0),Z(1,1))},styles:[`.mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mdc-text-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mdc-outlined-button-label-text-color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}
`],encapsulation:2,changeDetection:0})}return n})();var Xu=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,X]})}return n})();var bo;function l_(){if(bo===void 0&&(bo=null,typeof window<"u")){let n=window;n.trustedTypes!==void 0&&(bo=n.trustedTypes.createPolicy("angular#components",{createHTML:i=>i}))}return bo}function Vn(n){return l_()?.createHTML(n)||n}function Qu(n){return Error(`Unable to find icon with the name "${n}"`)}function c_(){return Error("Could not find HttpClient for use with Angular Material icons. Please add provideHttpClient() to your providers.")}function Ju(n){return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL via Angular's DomSanitizer. Attempted URL was "${n}".`)}function em(n){return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by Angular's DomSanitizer. Attempted literal was "${n}".`)}var at=class{url;svgText;options;svgElement;constructor(i,e,t){this.url=i,this.svgText=e,this.options=t}},im=(()=>{class n{_httpClient;_sanitizer;_errorHandler;_document;_svgIconConfigs=new Map;_iconSetConfigs=new Map;_cachedIconsByUrl=new Map;_inProgressUrlFetches=new Map;_fontCssClassesByAlias=new Map;_resolvers=[];_defaultFontSetClass=["material-icons","mat-ligature-font"];constructor(e,t,r,o){this._httpClient=e,this._sanitizer=t,this._errorHandler=o,this._document=r}addSvgIcon(e,t,r){return this.addSvgIconInNamespace("",e,t,r)}addSvgIconLiteral(e,t,r){return this.addSvgIconLiteralInNamespace("",e,t,r)}addSvgIconInNamespace(e,t,r,o){return this._addSvgIconConfig(e,t,new at(r,null,o))}addSvgIconResolver(e){return this._resolvers.push(e),this}addSvgIconLiteralInNamespace(e,t,r,o){let a=this._sanitizer.sanitize(Ie.HTML,r);if(!a)throw em(r);let s=Vn(a);return this._addSvgIconConfig(e,t,new at("",s,o))}addSvgIconSet(e,t){return this.addSvgIconSetInNamespace("",e,t)}addSvgIconSetLiteral(e,t){return this.addSvgIconSetLiteralInNamespace("",e,t)}addSvgIconSetInNamespace(e,t,r){return this._addSvgIconSetConfig(e,new at(t,null,r))}addSvgIconSetLiteralInNamespace(e,t,r){let o=this._sanitizer.sanitize(Ie.HTML,t);if(!o)throw em(t);let a=Vn(o);return this._addSvgIconSetConfig(e,new at("",a,r))}registerFontClassAlias(e,t=e){return this._fontCssClassesByAlias.set(e,t),this}classNameForFontAlias(e){return this._fontCssClassesByAlias.get(e)||e}setDefaultFontSetClass(...e){return this._defaultFontSetClass=e,this}getDefaultFontSetClass(){return this._defaultFontSetClass}getSvgIconFromUrl(e){let t=this._sanitizer.sanitize(Ie.RESOURCE_URL,e);if(!t)throw Ju(e);let r=this._cachedIconsByUrl.get(t);return r?D(yo(r)):this._loadSvgIconFromConfig(new at(e,null)).pipe(oe(o=>this._cachedIconsByUrl.set(t,o)),M(o=>yo(o)))}getNamedSvgIcon(e,t=""){let r=tm(t,e),o=this._svgIconConfigs.get(r);if(o)return this._getSvgFromConfig(o);if(o=this._getIconConfigFromResolvers(t,e),o)return this._svgIconConfigs.set(r,o),this._getSvgFromConfig(o);let a=this._iconSetConfigs.get(t);return a?this._getSvgFromIconSetConfigs(e,a):Tt(Qu(r))}ngOnDestroy(){this._resolvers=[],this._svgIconConfigs.clear(),this._iconSetConfigs.clear(),this._cachedIconsByUrl.clear()}_getSvgFromConfig(e){return e.svgText?D(yo(this._svgElementFromConfig(e))):this._loadSvgIconFromConfig(e).pipe(M(t=>yo(t)))}_getSvgFromIconSetConfigs(e,t){let r=this._extractIconWithNameFromAnySet(e,t);if(r)return D(r);let o=t.filter(a=>!a.svgText).map(a=>this._loadSvgIconSetFromConfig(a).pipe(ct(s=>{let m=`Loading icon set URL: ${this._sanitizer.sanitize(Ie.RESOURCE_URL,a.url)} failed: ${s.message}`;return this._errorHandler.handleError(new Error(m)),D(null)})));return zn(o).pipe(M(()=>{let a=this._extractIconWithNameFromAnySet(e,t);if(!a)throw Qu(e);return a}))}_extractIconWithNameFromAnySet(e,t){for(let r=t.length-1;r>=0;r--){let o=t[r];if(o.svgText&&o.svgText.toString().indexOf(e)>-1){let a=this._svgElementFromConfig(o),s=this._extractSvgIconFromSet(a,e,o.options);if(s)return s}}return null}_loadSvgIconFromConfig(e){return this._fetchIcon(e).pipe(oe(t=>e.svgText=t),M(()=>this._svgElementFromConfig(e)))}_loadSvgIconSetFromConfig(e){return e.svgText?D(null):this._fetchIcon(e).pipe(oe(t=>e.svgText=t))}_extractSvgIconFromSet(e,t,r){let o=e.querySelector(`[id="${t}"]`);if(!o)return null;let a=o.cloneNode(!0);if(a.removeAttribute("id"),a.nodeName.toLowerCase()==="svg")return this._setSvgAttributes(a,r);if(a.nodeName.toLowerCase()==="symbol")return this._setSvgAttributes(this._toSvgElement(a),r);let s=this._svgElementFromString(Vn("<svg></svg>"));return s.appendChild(a),this._setSvgAttributes(s,r)}_svgElementFromString(e){let t=this._document.createElement("DIV");t.innerHTML=e;let r=t.querySelector("svg");if(!r)throw Error("<svg> tag not found");return r}_toSvgElement(e){let t=this._svgElementFromString(Vn("<svg></svg>")),r=e.attributes;for(let o=0;o<r.length;o++){let{name:a,value:s}=r[o];a!=="id"&&t.setAttribute(a,s)}for(let o=0;o<e.childNodes.length;o++)e.childNodes[o].nodeType===this._document.ELEMENT_NODE&&t.appendChild(e.childNodes[o].cloneNode(!0));return t}_setSvgAttributes(e,t){return e.setAttribute("fit",""),e.setAttribute("height","100%"),e.setAttribute("width","100%"),e.setAttribute("preserveAspectRatio","xMidYMid meet"),e.setAttribute("focusable","false"),t&&t.viewBox&&e.setAttribute("viewBox",t.viewBox),e}_fetchIcon(e){let{url:t,options:r}=e,o=r?.withCredentials??!1;if(!this._httpClient)throw c_();if(t==null)throw Error(`Cannot fetch icon from URL "${t}".`);let a=this._sanitizer.sanitize(Ie.RESOURCE_URL,t);if(!a)throw Ju(t);let s=this._inProgressUrlFetches.get(a);if(s)return s;let c=this._httpClient.get(a,{responseType:"text",withCredentials:o}).pipe(M(m=>Vn(m)),dt(()=>this._inProgressUrlFetches.delete(a)),$s());return this._inProgressUrlFetches.set(a,c),c}_addSvgIconConfig(e,t,r){return this._svgIconConfigs.set(tm(e,t),r),this}_addSvgIconSetConfig(e,t){let r=this._iconSetConfigs.get(e);return r?r.push(t):this._iconSetConfigs.set(e,[t]),this}_svgElementFromConfig(e){if(!e.svgElement){let t=this._svgElementFromString(e.svgText);this._setSvgAttributes(t,e.options),e.svgElement=t}return e.svgElement}_getIconConfigFromResolvers(e,t){for(let r=0;r<this._resolvers.length;r++){let o=this._resolvers[r](t,e);if(o)return d_(o)?new at(o.url,null,o.options):new at(o,null)}}static \u0275fac=function(t){return new(t||n)(F(yt,8),F(ra),F(N,8),F(ht))};static \u0275prov=v({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function yo(n){return n.cloneNode(!0)}function tm(n,i){return n+":"+i}function d_(n){return!!(n.url&&n.options)}var u_=["*"],m_=new b("MAT_ICON_DEFAULT_OPTIONS"),h_=new b("mat-icon-location",{providedIn:"root",factory:f_});function f_(){let n=l(N),i=n?n.location:null;return{getPathname:()=>i?i.pathname+i.search:""}}var nm=["clip-path","color-profile","src","cursor","fill","filter","marker","marker-start","marker-mid","marker-end","mask","stroke"],p_=nm.map(n=>`[${n}]`).join(", "),g_=/^url\(['"]?#(.*?)['"]?\)$/,rm=(()=>{class n{_elementRef=l(H);_iconRegistry=l(im);_location=l(h_);_errorHandler=l(ht);_defaultColor;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;inline=!1;get svgIcon(){return this._svgIcon}set svgIcon(e){e!==this._svgIcon&&(e?this._updateSvgIcon(e):this._svgIcon&&this._clearSvgElement(),this._svgIcon=e)}_svgIcon;get fontSet(){return this._fontSet}set fontSet(e){let t=this._cleanupFontValue(e);t!==this._fontSet&&(this._fontSet=t,this._updateFontIconClasses())}_fontSet;get fontIcon(){return this._fontIcon}set fontIcon(e){let t=this._cleanupFontValue(e);t!==this._fontIcon&&(this._fontIcon=t,this._updateFontIconClasses())}_fontIcon;_previousFontSetClass=[];_previousFontIconClass;_svgName;_svgNamespace;_previousPath;_elementsWithExternalReferences;_currentIconFetch=ve.EMPTY;constructor(){let e=l(new $n("aria-hidden"),{optional:!0}),t=l(m_,{optional:!0});t&&(t.color&&(this.color=this._defaultColor=t.color),t.fontSet&&(this.fontSet=t.fontSet)),e||this._elementRef.nativeElement.setAttribute("aria-hidden","true")}_splitIconName(e){if(!e)return["",""];let t=e.split(":");switch(t.length){case 1:return["",t[0]];case 2:return t;default:throw Error(`Invalid icon name: "${e}"`)}}ngOnInit(){this._updateFontIconClasses()}ngAfterViewChecked(){let e=this._elementsWithExternalReferences;if(e&&e.size){let t=this._location.getPathname();t!==this._previousPath&&(this._previousPath=t,this._prependPathToReferences(t))}}ngOnDestroy(){this._currentIconFetch.unsubscribe(),this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear()}_usingFontIcon(){return!this.svgIcon}_setSvgElement(e){this._clearSvgElement();let t=this._location.getPathname();this._previousPath=t,this._cacheChildrenWithExternalReferences(e),this._prependPathToReferences(t),this._elementRef.nativeElement.appendChild(e)}_clearSvgElement(){let e=this._elementRef.nativeElement,t=e.childNodes.length;for(this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear();t--;){let r=e.childNodes[t];(r.nodeType!==1||r.nodeName.toLowerCase()==="svg")&&r.remove()}}_updateFontIconClasses(){if(!this._usingFontIcon())return;let e=this._elementRef.nativeElement,t=(this.fontSet?this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/):this._iconRegistry.getDefaultFontSetClass()).filter(r=>r.length>0);this._previousFontSetClass.forEach(r=>e.classList.remove(r)),t.forEach(r=>e.classList.add(r)),this._previousFontSetClass=t,this.fontIcon!==this._previousFontIconClass&&!t.includes("mat-ligature-font")&&(this._previousFontIconClass&&e.classList.remove(this._previousFontIconClass),this.fontIcon&&e.classList.add(this.fontIcon),this._previousFontIconClass=this.fontIcon)}_cleanupFontValue(e){return typeof e=="string"?e.trim().split(" ")[0]:e}_prependPathToReferences(e){let t=this._elementsWithExternalReferences;t&&t.forEach((r,o)=>{r.forEach(a=>{o.setAttribute(a.name,`url('${e}#${a.value}')`)})})}_cacheChildrenWithExternalReferences(e){let t=e.querySelectorAll(p_),r=this._elementsWithExternalReferences=this._elementsWithExternalReferences||new Map;for(let o=0;o<t.length;o++)nm.forEach(a=>{let s=t[o],c=s.getAttribute(a),m=c?c.match(g_):null;if(m){let h=r.get(s);h||(h=[],r.set(s,h)),h.push({name:a,value:m[1]})}})}_updateSvgIcon(e){if(this._svgNamespace=null,this._svgName=null,this._currentIconFetch.unsubscribe(),e){let[t,r]=this._splitIconName(e);t&&(this._svgNamespace=t),r&&(this._svgName=r),this._currentIconFetch=this._iconRegistry.getNamedSvgIcon(r,t).pipe(Pe(1)).subscribe(o=>this._setSvgElement(o),o=>{let a=`Error retrieving icon ${t}:${r}! ${o.message}`;this._errorHandler.handleError(new Error(a))})}}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=U({type:n,selectors:[["mat-icon"]],hostAttrs:["role","img",1,"mat-icon","notranslate"],hostVars:10,hostBindings:function(t,r){t&2&&(ne("data-mat-icon-type",r._usingFontIcon()?"font":"svg")("data-mat-icon-name",r._svgName||r.fontIcon)("data-mat-icon-namespace",r._svgNamespace||r.fontSet)("fontIcon",r._usingFontIcon()?r.fontIcon:null),Je(r.color?"mat-"+r.color:""),$("mat-icon-inline",r.inline)("mat-icon-no-color",r.color!=="primary"&&r.color!=="accent"&&r.color!=="warn"))},inputs:{color:"color",inline:[2,"inline","inline",q],svgIcon:"svgIcon",fontSet:"fontSet",fontIcon:"fontIcon"},exportAs:["matIcon"],ngContentSelectors:u_,decls:1,vars:0,template:function(t,r){t&1&&(ge(),Z(0))},styles:[`mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}
`],encapsulation:2,changeDetection:0})}return n})(),om=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=k({type:n});static \u0275inj=T({imports:[X,X]})}return n})();function v_(n,i){n&1&&(u(0,"mat-error"),p(1,"This is required."),d())}function b_(n,i){n&1&&(u(0,"mat-error"),p(1,"Max length 50."),d())}function y_(n,i){n&1&&(u(0,"mat-error"),p(1,"This is required."),d())}function C_(n,i){n&1&&(u(0,"mat-error"),p(1,"Max length 100."),d())}function w_(n,i){n&1&&(u(0,"mat-error"),p(1,"This is required."),d())}function D_(n,i){n&1&&(u(0,"mat-error"),p(1,"Max length 20."),d())}function x_(n,i){n&1&&(u(0,"mat-error"),p(1,"This is required."),d())}function E_(n,i){n&1&&(u(0,"mat-error"),p(1,"This is required."),d())}function S_(n,i){n&1&&(u(0,"mat-error"),p(1,"Max length 500."),d())}function M_(n,i){n&1&&O(0,"mat-spinner",30)}function A_(n,i){n&1&&(u(0,"span"),p(1,"Save"),d())}function I_(n,i){n&1&&(u(0,"span"),p(1,"Saving..."),d())}function R_(n,i){if(n&1&&(u(0,"mat-card",4)(1,"mat-toolbar",5)(2,"mat-toolbar-row")(3,"mat-icon",6),p(4,"check_circle"),d(),u(5,"span"),p(6,"Submitted Data"),d()()(),u(7,"mat-card-content")(8,"pre"),p(9),gl(10,"json"),d()()()),n&2){let e=P();f(9),zt(_l(10,1,e.submittedData))}}var Jt=class n{constructor(i,e){this.fb=i;this.http=e}truckForm;submittedData=null;isLoading=!1;ngOnInit(){this.initializeForm()}initializeForm(){this.truckForm=this.fb.group({truckNumber:["",[W.required]],driverName:["",[W.required]],licensePlate:["",[W.required]],truckType:["",[W.required]],status:["active"],notes:[""]})}onSubmit(){if(this.truckForm.valid){this.isLoading=!0;let i=this.truckForm.value,e=new URLSearchParams;e.append("ajax","SaveTruck"),Object.keys(i).forEach(t=>{i[t]!==null&&i[t]!==""&&e.append(t,i[t])}),this.http.post("includes/truck_submit.php",e.toString(),{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).subscribe({next:t=>{this.isLoading=!1,t.Success?(this.submittedData=i,alert("Truck saved successfully!"),this.onReset()):alert("Error: "+t.Result)},error:t=>{this.isLoading=!1,console.error("Error saving truck:",t),alert("Error saving truck. Please try again.")}})}}onReset(){this.truckForm.reset(),this.truckForm.patchValue({status:"active"}),this.submittedData=null}static \u0275fac=function(e){return new(e||n)(G(Yr),G(yt))};static \u0275cmp=U({type:n,selectors:[["app-truck-form"]],decls:73,vars:15,consts:[[1,"page"],[1,"row","ui-section"],[1,"col-md-12"],[1,"article"],[1,"no-margin-h"],[1,"md-table-toolbar","md-default"],[1,"md-primary"],["name","truckFormValidation",1,"form-validation",3,"ngSubmit","formGroup"],[1,"row"],[1,"col-md-3"],["appearance","outline",1,"full-width"],["matInput","","name","truckNumber","formControlName","truckNumber","required","","maxlength","50"],[4,"ngIf"],["matInput","","name","driverName","formControlName","driverName","required","","maxlength","100"],["matInput","","name","licensePlate","formControlName","licensePlate","required","","maxlength","20"],["name","truckType","formControlName","truckType","required",""],["value","delivery"],["value","pickup"],["value","transport"],["name","status","formControlName","status","required",""],["value","active"],["value","inactive"],["value","maintenance"],[1,"col-md-6"],["matInput","","name","notes","formControlName","notes","maxlength","500","rows","3"],[1,"col-md-12","btns-row"],["mat-raised-button","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],["class","no-margin-h",4,"ngIf"],["diameter","20"]],template:function(e,t){if(e&1&&(u(0,"div",0)(1,"div",1)(2,"div",2)(3,"article",3)(4,"mat-card",4)(5,"mat-toolbar",5)(6,"mat-toolbar-row")(7,"mat-icon",6),p(8,"local_shipping"),d(),u(9,"span"),p(10,"Add New Truck"),d()()(),u(11,"mat-card-content")(12,"form",7),re("ngSubmit",function(){return t.onSubmit()}),u(13,"div",8)(14,"div",9)(15,"mat-form-field",10)(16,"mat-label"),p(17,"Truck Number"),d(),O(18,"input",11),y(19,v_,2,0,"mat-error",12)(20,b_,2,0,"mat-error",12),d()(),u(21,"div",9)(22,"mat-form-field",10)(23,"mat-label"),p(24,"Driver Name"),d(),O(25,"input",13),y(26,y_,2,0,"mat-error",12)(27,C_,2,0,"mat-error",12),d()(),u(28,"div",9)(29,"mat-form-field",10)(30,"mat-label"),p(31,"License Plate"),d(),O(32,"input",14),y(33,w_,2,0,"mat-error",12)(34,D_,2,0,"mat-error",12),d()(),u(35,"div",9)(36,"mat-form-field",10)(37,"mat-label"),p(38,"Truck Type"),d(),u(39,"mat-select",15)(40,"mat-option",16),p(41,"Delivery"),d(),u(42,"mat-option",17),p(43,"Pickup"),d(),u(44,"mat-option",18),p(45,"Transport"),d()(),y(46,x_,2,0,"mat-error",12),d()(),u(47,"div",9)(48,"mat-form-field",10)(49,"mat-label"),p(50,"Status"),d(),u(51,"mat-select",19)(52,"mat-option",20),p(53,"Active"),d(),u(54,"mat-option",21),p(55,"Inactive"),d(),u(56,"mat-option",22),p(57,"Maintenance"),d()(),y(58,E_,2,0,"mat-error",12),d()(),u(59,"div",23)(60,"mat-form-field",10)(61,"mat-label"),p(62,"Notes"),d(),O(63,"textarea",24),y(64,S_,2,0,"mat-error",12),d()(),u(65,"div",25)(66,"button",26),re("click",function(){return t.onReset()}),p(67,"Cancel"),d(),u(68,"button",27),y(69,M_,1,0,"mat-spinner",28)(70,A_,2,0,"span",12)(71,I_,2,0,"span",12),d()()()()()(),y(72,R_,11,3,"mat-card",29),d()()()()),e&2){let r,o,a,s,c,m,h,C,x;f(12),g("formGroup",t.truckForm),f(7),g("ngIf",(r=t.truckForm.get("truckNumber"))==null?null:r.hasError("required")),f(),g("ngIf",(o=t.truckForm.get("truckNumber"))==null?null:o.hasError("maxlength")),f(6),g("ngIf",(a=t.truckForm.get("driverName"))==null?null:a.hasError("required")),f(),g("ngIf",(s=t.truckForm.get("driverName"))==null?null:s.hasError("maxlength")),f(6),g("ngIf",(c=t.truckForm.get("licensePlate"))==null?null:c.hasError("required")),f(),g("ngIf",(m=t.truckForm.get("licensePlate"))==null?null:m.hasError("maxlength")),f(12),g("ngIf",(h=t.truckForm.get("truckType"))==null?null:h.hasError("required")),f(12),g("ngIf",(C=t.truckForm.get("status"))==null?null:C.hasError("required")),f(6),g("ngIf",(x=t.truckForm.get("notes"))==null?null:x.hasError("maxlength")),f(4),g("disabled",t.truckForm.invalid||t.isLoading),f(),g("ngIf",t.isLoading),f(),g("ngIf",!t.isLoading),f(),g("ngIf",t.isLoading),f(),g("ngIf",t.submittedData)}},dependencies:[vt,Ht,$o,Zr,qr,wi,$r,Gr,wn,Dn,ot,Cn,Gd,Hd,$d,It,bs,In,vs,du,cu,Ns,Ps,kn,Wu,Gu,Zu,Yu,Xu,Ku,Vs,om,rm],styles:[".page[_ngcontent-%COMP%]{padding:20px}.full-width[_ngcontent-%COMP%]{width:100%}.btns-row[_ngcontent-%COMP%]{display:flex;gap:12px;margin-top:20px;justify-content:flex-end}.no-margin-h[_ngcontent-%COMP%]{margin:0}mat-card-content[_ngcontent-%COMP%]{padding:16px}mat-toolbar[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#333}mat-toolbar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:4px;overflow-x:auto;margin:0}@media (max-width: 768px){.btns-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}}"]})};function T_(n,i){n&1&&(u(0,"div",9)(1,"div",10),O(2,"md-progress-circular",11),u(3,"p"),p(4,"Loading form data..."),d()()())}function k_(n,i){if(n&1&&(u(0,"md-option",49),p(1),d()),n&2){let e=i.$implicit;g("value",e.FacilityID),f(),Ut(" ",e.FacilityName," ")}}function F_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function O_(n,i){if(n&1&&(u(0,"div"),y(1,F_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("FacilityID"))==null?null:e.hasError("required"))}}function P_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function N_(n,i){if(n&1&&(u(0,"div"),y(1,P_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("ArrivalType"))==null?null:e.hasError("required"))}}function L_(n,i){if(n&1&&(u(0,"md-option",49),p(1),d()),n&2){let e=i.$implicit;g("value",e.ParkingLocationID),f(),Ut(" ",e.ParkingLocationName," ")}}function B_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function V_(n,i){if(n&1&&(u(0,"div"),y(1,B_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("ParkingLocationID"))==null?null:e.hasError("required"))}}function j_(n,i){if(n&1&&(u(0,"md-option",49),p(1),d()),n&2){let e=i.$implicit;g("value",e.CarrierID),f(),Ut(" ",e.CarrierName," ")}}function z_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function U_(n,i){if(n&1&&(u(0,"div"),y(1,z_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("CarrierID"))==null?null:e.hasError("required"))}}function H_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function $_(n,i){if(n&1&&(u(0,"div"),y(1,H_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("ArrivalDate"))==null?null:e.hasError("required"))}}function G_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function W_(n,i){if(n&1&&(u(0,"div"),y(1,G_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("ArrivalTime"))==null?null:e.hasError("required"))}}function q_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function Y_(n,i){if(n&1&&(u(0,"div"),y(1,q_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("LoadType"))==null?null:e.hasError("required"))}}function Z_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function K_(n,i){n&1&&(u(0,"div"),p(1,"Max length 50."),d())}function X_(n,i){if(n&1&&(u(0,"div"),y(1,Z_,2,0,"div",18)(2,K_,2,0,"div",18),d()),n&2){let e,t,r=P(2);f(),g("ngIf",(e=r.truckForm.get("LoadNumber"))==null?null:e.hasError("required")),f(),g("ngIf",(t=r.truckForm.get("LoadNumber"))==null?null:t.hasError("maxlength"))}}function Q_(n,i){if(n&1&&(u(0,"md-option",49),p(1),d()),n&2){let e=i.$implicit;g("value",e.TruckTypeID),f(),Ut(" ",e.TruckTypeName," ")}}function J_(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function ev(n,i){if(n&1&&(u(0,"div"),y(1,J_,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("TruckTypeID"))==null?null:e.hasError("required"))}}function tv(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function iv(n,i){n&1&&(u(0,"div"),p(1,"Max length 50."),d())}function nv(n,i){if(n&1&&(u(0,"div"),y(1,tv,2,0,"div",18)(2,iv,2,0,"div",18),d()),n&2){let e,t,r=P(2);f(),g("ngIf",(e=r.truckForm.get("TruckReg"))==null?null:e.hasError("required")),f(),g("ngIf",(t=r.truckForm.get("TruckReg"))==null?null:t.hasError("maxlength"))}}function rv(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function ov(n,i){n&1&&(u(0,"div"),p(1,"Max length 50."),d())}function av(n,i){if(n&1&&(u(0,"div"),y(1,rv,2,0,"div",18)(2,ov,2,0,"div",18),d()),n&2){let e,t,r=P(2);f(),g("ngIf",(e=r.truckForm.get("TrailerNumber"))==null?null:e.hasError("required")),f(),g("ngIf",(t=r.truckForm.get("TrailerNumber"))==null?null:t.hasError("maxlength"))}}function sv(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function lv(n,i){n&1&&(u(0,"div"),p(1,"Max length 50."),d())}function cv(n,i){if(n&1&&(u(0,"div"),y(1,sv,2,0,"div",18)(2,lv,2,0,"div",18),d()),n&2){let e,t,r=P(2);f(),g("ngIf",(e=r.truckForm.get("DriverName"))==null?null:e.hasError("required")),f(),g("ngIf",(t=r.truckForm.get("DriverName"))==null?null:t.hasError("maxlength"))}}function dv(n,i){n&1&&(u(0,"div"),p(1,"Max length 50."),d())}function uv(n,i){if(n&1&&(u(0,"div"),y(1,dv,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("DriverID"))==null?null:e.hasError("maxlength"))}}function mv(n,i){n&1&&(u(0,"div"),p(1,"Max length 50."),d())}function hv(n,i){if(n&1&&(u(0,"div"),y(1,mv,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("ShipmentTicketID"))==null?null:e.hasError("maxlength"))}}function fv(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function pv(n,i){if(n&1&&(u(0,"div"),y(1,fv,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("ClassificationType"))==null?null:e.hasError("required"))}}function gv(n,i){n&1&&(u(0,"div"),p(1,"Max length 250."),d())}function _v(n,i){if(n&1&&(u(0,"div"),y(1,gv,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("WasteCollectionPermit"))==null?null:e.hasError("maxlength"))}}function vv(n,i){n&1&&(u(0,"div"),p(1,"Max length 250."),d())}function bv(n,i){if(n&1&&(u(0,"div"),y(1,vv,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("Notes"))==null?null:e.hasError("maxlength"))}}function yv(n,i){n&1&&(u(0,"div"),p(1,"This is required."),d())}function Cv(n,i){if(n&1&&(u(0,"div"),y(1,yv,2,0,"div",18),d()),n&2){let e,t=P(2);f(),g("ngIf",(e=t.truckForm.get("Status"))==null?null:e.hasError("required"))}}function wv(n,i){n&1&&(u(0,"span"),p(1,"Save"),d())}function Dv(n,i){n&1&&(u(0,"span"),O(1,"md-progress-circular",50),d())}function xv(n,i){if(n&1){let e=Bt();u(0,"div",9)(1,"form",12),re("ngSubmit",function(){Le(e);let r=P();return Be(r.onSave())}),u(2,"div",2)(3,"div",13)(4,"md-input-container",14)(5,"label"),p(6,"Facility"),d(),u(7,"md-select",15),y(8,k_,2,2,"md-option",16),d(),u(9,"div",17),y(10,O_,2,1,"div",18),d()()(),u(11,"div",13)(12,"md-input-container",14)(13,"label"),p(14,"Arrival Type"),d(),u(15,"md-select",19)(16,"md-option",20),p(17,"Inbound"),d(),u(18,"md-option",21),p(19,"Outbound"),d()(),u(20,"div",17),y(21,N_,2,1,"div",18),d()()(),u(22,"div",13)(23,"md-input-container",14)(24,"label"),p(25,"Parking Location"),d(),u(26,"md-select",22),y(27,L_,2,2,"md-option",16),d(),u(28,"div",17),y(29,V_,2,1,"div",18),d()()(),u(30,"div",13)(31,"md-input-container",14)(32,"label"),p(33,"Carrier"),d(),u(34,"md-select",23),re("change",function(){Le(e);let r=P();return Be(r.onCarrierChange())}),y(35,j_,2,2,"md-option",16),d(),u(36,"div",17),y(37,U_,2,1,"div",18),d()()(),u(38,"div",13)(39,"md-input-container",14)(40,"label"),p(41,"Date"),d(),O(42,"input",24),u(43,"div",17),y(44,$_,2,1,"div",18),d()()(),u(45,"div",13)(46,"md-input-container",14)(47,"label"),p(48,"Expected Arrival Time"),d(),O(49,"input",25),u(50,"div",17),y(51,W_,2,1,"div",18),d()()(),u(52,"div",13)(53,"md-input-container",14)(54,"label"),p(55,"Load Type"),d(),u(56,"md-select",26)(57,"md-option",27),p(58,"Pallet"),d(),u(59,"md-option",28),p(60,"Rack"),d()(),u(61,"div",17),y(62,Y_,2,1,"div",18),d()()(),u(63,"div",13)(64,"md-input-container",14)(65,"label"),p(66,"Load Number"),d(),O(67,"input",29),u(68,"div",17),y(69,X_,3,2,"div",18),d()()(),u(70,"div",13)(71,"md-input-container",14)(72,"label"),p(73,"Vehicle Type"),d(),u(74,"md-select",30),y(75,Q_,2,2,"md-option",16),d(),u(76,"div",17),y(77,ev,2,1,"div",18),d()()(),u(78,"div",13)(79,"md-input-container",14)(80,"label"),p(81,"Truck Reg"),d(),O(82,"input",31),u(83,"div",17),y(84,nv,3,2,"div",18),d()()(),u(85,"div",13)(86,"md-input-container",14)(87,"label"),p(88,"Trailer Number"),d(),O(89,"input",32),u(90,"div",17),y(91,av,3,2,"div",18),d()()(),u(92,"div",13)(93,"md-input-container",14)(94,"label"),p(95,"Driver"),d(),O(96,"input",33),u(97,"div",17),y(98,cv,3,2,"div",18),d()()(),u(99,"div",13)(100,"md-input-container",14)(101,"label"),p(102,"Driver ID"),d(),O(103,"input",34),u(104,"div",17),y(105,uv,2,1,"div",18),d()()(),u(106,"div",13)(107,"md-input-container",14)(108,"label"),p(109,"Shipment Ticket ID's"),d(),O(110,"input",35),u(111,"div",17),y(112,hv,2,1,"div",18),d()()(),u(113,"div",13)(114,"md-input-container",14)(115,"label"),p(116,"Classification Type"),d(),u(117,"md-select",36)(118,"md-option",37),p(119,"All"),d(),u(120,"md-option",38),p(121,"UEEE"),d(),u(122,"md-option",39),p(123,"WEEE"),d()(),u(124,"div",17),y(125,pv,2,1,"div",18),d()()(),u(126,"div",13)(127,"md-input-container",14)(128,"label"),p(129,"Waste Collection Permit"),d(),O(130,"input",40),u(131,"div",17),y(132,_v,2,1,"div",18),d()()(),u(133,"div",41)(134,"md-input-container",14)(135,"label"),p(136,"Notes"),d(),O(137,"input",42),u(138,"div",17),y(139,bv,2,1,"div",18),d()()(),u(140,"div",13)(141,"md-input-container",14)(142,"label"),p(143,"Status"),d(),u(144,"md-select",43)(145,"md-option",44),p(146,"Reserved"),d(),u(147,"md-option",45),p(148,"Requested"),d()(),u(149,"div",17),y(150,Cv,2,1,"div",18),d()()(),u(151,"div",46)(152,"button",47),re("click",function(){Le(e);let r=P();return Be(r.onReset())}),p(153,"Cancel"),d(),u(154,"button",48),y(155,wv,2,0,"span",18)(156,Dv,2,0,"span",18),d()()()()()}if(n&2){let e,t,r,o,a,s,c,m,h,C,x,A,L,w,B,me,Ae,te,S=P();f(),g("formGroup",S.truckForm),f(6),g("disabled",!0),f(),g("ngForOf",S.facilities),f(2),g("ngIf",((e=S.truckForm.get("FacilityID"))==null?null:e.invalid)&&((e=S.truckForm.get("FacilityID"))==null?null:e.touched)),f(11),g("ngIf",((t=S.truckForm.get("ArrivalType"))==null?null:t.invalid)&&((t=S.truckForm.get("ArrivalType"))==null?null:t.touched)),f(6),g("ngForOf",S.parkingLocations),f(2),g("ngIf",((r=S.truckForm.get("ParkingLocationID"))==null?null:r.invalid)&&((r=S.truckForm.get("ParkingLocationID"))==null?null:r.touched)),f(6),g("ngForOf",S.carriers),f(2),g("ngIf",((o=S.truckForm.get("CarrierID"))==null?null:o.invalid)&&((o=S.truckForm.get("CarrierID"))==null?null:o.touched)),f(7),g("ngIf",((a=S.truckForm.get("ArrivalDate"))==null?null:a.invalid)&&((a=S.truckForm.get("ArrivalDate"))==null?null:a.touched)),f(7),g("ngIf",((s=S.truckForm.get("ArrivalTime"))==null?null:s.invalid)&&((s=S.truckForm.get("ArrivalTime"))==null?null:s.touched)),f(11),g("ngIf",((c=S.truckForm.get("LoadType"))==null?null:c.invalid)&&((c=S.truckForm.get("LoadType"))==null?null:c.touched)),f(7),g("ngIf",((m=S.truckForm.get("LoadNumber"))==null?null:m.invalid)&&((m=S.truckForm.get("LoadNumber"))==null?null:m.touched)),f(6),g("ngForOf",S.truckTypes),f(2),g("ngIf",((h=S.truckForm.get("TruckTypeID"))==null?null:h.invalid)&&((h=S.truckForm.get("TruckTypeID"))==null?null:h.touched)),f(7),g("ngIf",((C=S.truckForm.get("TruckReg"))==null?null:C.invalid)&&((C=S.truckForm.get("TruckReg"))==null?null:C.touched)),f(7),g("ngIf",((x=S.truckForm.get("TrailerNumber"))==null?null:x.invalid)&&((x=S.truckForm.get("TrailerNumber"))==null?null:x.touched)),f(7),g("ngIf",((A=S.truckForm.get("DriverName"))==null?null:A.invalid)&&((A=S.truckForm.get("DriverName"))==null?null:A.touched)),f(7),g("ngIf",((L=S.truckForm.get("DriverID"))==null?null:L.invalid)&&((L=S.truckForm.get("DriverID"))==null?null:L.touched)),f(7),g("ngIf",((w=S.truckForm.get("ShipmentTicketID"))==null?null:w.invalid)&&((w=S.truckForm.get("ShipmentTicketID"))==null?null:w.touched)),f(10),g("disabled",S.isWeeeDisabled()),f(3),g("ngIf",((B=S.truckForm.get("ClassificationType"))==null?null:B.invalid)&&((B=S.truckForm.get("ClassificationType"))==null?null:B.touched)),f(7),g("ngIf",((me=S.truckForm.get("WasteCollectionPermit"))==null?null:me.invalid)&&((me=S.truckForm.get("WasteCollectionPermit"))==null?null:me.touched)),f(7),g("ngIf",((Ae=S.truckForm.get("Notes"))==null?null:Ae.invalid)&&((Ae=S.truckForm.get("Notes"))==null?null:Ae.touched)),f(11),g("ngIf",((te=S.truckForm.get("Status"))==null?null:te.invalid)&&((te=S.truckForm.get("Status"))==null?null:te.touched)),f(4),g("disabled",S.truckForm.invalid||S.isSaving),f(),g("ngIf",!S.isSaving),f(),g("ngIf",S.isSaving)}}var Ri=class n{constructor(i,e){this.fb=i;this.http=e;this.truckForm=this.createForm()}truckForm;isLoading=!1;isSaving=!1;facilities=[];parkingLocations=[];carriers=[];truckTypes=[];selectedCarrier=null;ngOnInit(){this.loadData()}createForm(){let i=new Date;return this.fb.group({FacilityID:["",W.required],ArrivalType:["",W.required],ParkingLocationID:["",W.required],CarrierID:["",W.required],ArrivalDate:[i,W.required],ArrivalTime:["",W.required],LoadType:["",W.required],LoadNumber:["",[W.required,W.maxLength(50)]],TruckTypeID:["",W.required],TruckReg:["",[W.required,W.maxLength(50)]],TrailerNumber:["",[W.required,W.maxLength(50)]],DriverName:["",[W.required,W.maxLength(50)]],DriverID:["",W.maxLength(50)],ShipmentTicketID:["",W.maxLength(50)],ClassificationType:["",W.required],WasteCollectionPermit:[{value:"",disabled:!0},W.maxLength(250)],Notes:["",W.maxLength(250)],Status:["",W.required]})}loadData(){this.isLoading=!0,Promise.all([this.loadFacilities(),this.loadParkingLocations(),this.loadCarriers(),this.loadTruckTypes()]).finally(()=>{this.isLoading=!1})}loadFacilities(){return new Promise(i=>{let e=window.sessionData;e?.facilityId&&this.truckForm.patchValue({FacilityID:e.facilityId}),this.http.post("shipping/includes/shipping_submit.php","ajax=GetFacilities",{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).subscribe({next:t=>{t.Success&&(this.facilities=t.Result),i()},error:()=>i()})})}loadParkingLocations(){return new Promise(i=>{this.http.post("Truck/includes/truck_submit.php","ajax=GetParkingLocations",{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).subscribe({next:e=>{e.Success&&(this.parkingLocations=e.Result),i()},error:()=>i()})})}loadCarriers(){return new Promise(i=>{this.http.post("Truck/includes/truck_submit.php","ajax=GetCarriers",{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).subscribe({next:e=>{e.Success&&(this.carriers=e.Result),i()},error:()=>i()})})}loadTruckTypes(){return new Promise(i=>{this.http.post("Truck/includes/truck_submit.php","ajax=GetTruckTypes",{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).subscribe({next:e=>{e.Success&&(this.truckTypes=e.Result),i()},error:()=>i()})})}onCarrierChange(){let i=this.truckForm.get("CarrierID")?.value;this.selectedCarrier=this.carriers.find(t=>t.CarrierID===i)||null,this.selectedCarrier?.WasteCollectionPermit?this.truckForm.patchValue({WasteCollectionPermit:this.selectedCarrier.WasteCollectionPermit}):this.truckForm.patchValue({WasteCollectionPermit:""}),this.truckForm.get("ClassificationType")?.value==="WEEE"&&(!this.selectedCarrier||this.selectedCarrier.WasteCollectionEligible!==1)&&this.truckForm.patchValue({ClassificationType:""})}onSave(){if(this.truckForm.valid){this.isSaving=!0;let i=this.truckForm.value;i.ArrivalDate&&(i.ArrivalDate=i.ArrivalDate.toISOString().substring(0,10));let e=new URLSearchParams;e.append("ajax","TruckSave"),Object.keys(i).forEach(t=>{i[t]!==null&&i[t]!==""&&e.append(t,i[t])}),this.http.post("Truck/includes/truck_submit.php",e.toString(),{headers:{"Content-Type":"application/x-www-form-urlencoded"}}).subscribe({next:t=>{this.isSaving=!1,t.Success?(alert("Truck booking saved successfully!"),this.truckForm.reset(),this.truckForm.patchValue({ArrivalDate:new Date})):alert("Error: "+t.Result)},error:()=>{this.isSaving=!1,alert("Error saving truck booking. Please try again.")}})}else this.markFormGroupTouched()}onReset(){this.truckForm.reset(),this.truckForm.patchValue({ArrivalDate:new Date}),this.selectedCarrier=null}markFormGroupTouched(){Object.keys(this.truckForm.controls).forEach(i=>{this.truckForm.get(i)?.markAsTouched()})}isWeeeDisabled(){return!this.selectedCarrier||this.selectedCarrier.WasteCollectionEligible!==1}isWasteCollectionPermitRequired(){let i=this.truckForm.get("ClassificationType")?.value,e=this.truckForm.get("FacilityID")?.value;return i==="WEEE"&&e==14}static \u0275fac=function(e){return new(e||n)(G(Yr),G(yt))};static \u0275cmp=U({type:n,selectors:[["app-truck-booking"]],decls:13,vars:2,consts:[[1,"page"],[1,"row","ui-section"],[1,"col-md-12"],[1,"article"],[1,"no-margin-h"],[1,"md-table-toolbar","md-default"],[1,"md-toolbar-tools"],[1,"material-icons","md-primary"],["class","row",4,"ngIf"],[1,"row"],[1,"col-md-12",2,"text-align","center","padding","40px"],["md-mode","indeterminate","md-diameter","50px",1,"md-hue-2"],["name","truckBookingForm",1,"form-validation",3,"ngSubmit","formGroup"],[1,"col-md-3"],[1,"md-block"],["name","FacilityID","formControlName","FacilityID","required","",3,"disabled"],[3,"value",4,"ngFor","ngForOf"],[1,"error-sapce"],[4,"ngIf"],["name","ArrivalType","formControlName","ArrivalType","required",""],["value","Inbound"],["value","Outbound"],["name","ParkingLocationID","formControlName","ParkingLocationID","required",""],["name","CarrierID","formControlName","CarrierID","required","",3,"change"],["name","ArrivalDate","formControlName","ArrivalDate","type","date","required",""],["name","ArrivalTime","formControlName","ArrivalTime","type","time","required",""],["name","LoadType","formControlName","LoadType","required",""],["value","Pallet"],["value","Rack"],["name","LoadNumber","formControlName","LoadNumber","required","","maxlength","50"],["name","TruckTypeID","formControlName","TruckTypeID","required",""],["name","TruckReg","formControlName","TruckReg","required","","maxlength","50"],["name","TrailerNumber","formControlName","TrailerNumber","required","","maxlength","50"],["name","DriverName","formControlName","DriverName","required","","maxlength","50"],["name","DriverID","formControlName","DriverID","maxlength","50"],["name","ShipmentTicketID","formControlName","ShipmentTicketID","maxlength","50"],["name","ClassificationType","formControlName","ClassificationType","required",""],["value","All"],["value","UEEE"],["value","WEEE",3,"disabled"],["name","WasteCollectionPermit","formControlName","WasteCollectionPermit","maxlength","250"],[1,"col-md-6"],["name","Notes","formControlName","Notes","maxlength","250"],["name","Status","formControlName","Status","required",""],["value","Reserved"],["value","Requested"],[1,"col-md-12","btns-row"],["type","button",1,"md-button","md-raised","btn-w-md","md-default",3,"click"],["type","submit",1,"md-raised","btn-w-md","md-primary","btn-w-md","md-button",3,"disabled"],[3,"value"],["md-mode","indeterminate","md-diameter","20px",1,"md-hue-2",2,"left","50px"]],template:function(e,t){e&1&&(u(0,"div",0)(1,"div",1)(2,"div",2)(3,"article",3)(4,"md-card",4)(5,"md-toolbar",5)(6,"div",6)(7,"i",7),p(8,"event"),d(),u(9,"span"),p(10,"Truck Booking"),d()()(),y(11,T_,5,0,"div",8)(12,xv,157,28,"div",8),d()()()()()),e&2&&(f(11),g("ngIf",t.isLoading),f(),g("ngIf",!t.isLoading))},dependencies:[vt,er,Ht,Zr,qr,wi,$r,Gr,wn,Dn,ot,Cn],encapsulation:2})};var sm=[{path:"",component:Jt},{path:"truck-form",component:Jt},{path:"truck-booking",component:Ri}];var lm={providers:[bl({eventCoalescing:!0}),Va(sm),ia(),$c()]};function Ev(n,i){n&1&&O(0,"app-truck-form")}function Sv(n,i){n&1&&O(0,"app-truck-booking")}var Co=class n{title="Truck Management";currentView="truck-form";ngOnInit(){this.detectCurrentPage()}detectCurrentPage(){window.location.hash.includes("TruckBooking")?this.currentView="truck-booking":this.currentView="truck-form",console.log("Current page:",this.currentView)}static \u0275fac=function(e){return new(e||n)};static \u0275cmp=U({type:n,selectors:[["app-root"]],decls:5,vars:2,consts:[[1,"truck-app-container"],[1,"content-container"],[1,"content-area"],[4,"ngIf"]],template:function(e,t){e&1&&(u(0,"div",0)(1,"div",1)(2,"div",2),y(3,Ev,1,0,"app-truck-form",3)(4,Sv,1,0,"app-truck-booking",3),d()()()),e&2&&(f(3),g("ngIf",t.currentView==="truck-form"),f(),g("ngIf",t.currentView==="truck-booking"))},dependencies:[vt,Ht,Jt,Ri],styles:[".truck-app-container[_ngcontent-%COMP%]{width:100%;font-family:Roboto,sans-serif}.content-container[_ngcontent-%COMP%]{padding:20px}.content-area[_ngcontent-%COMP%]{width:100%}"]})};function Mv(){return new Promise(n=>{let i=()=>{document.querySelector("app-root")?(console.log("app-root element found, bootstrapping Angular 19"),n()):setTimeout(i,100)};i()})}Mv().then(()=>{Jo(Co,lm).catch(n=>console.error("Angular 19 bootstrap error:",n))});
