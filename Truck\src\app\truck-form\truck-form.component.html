<!-- Truck Form with Professional Theme Design -->
<mat-card class="cardWithShadow">
    <!-- Card Header -->
    <mat-card-header class="p-24">
        <div class="d-flex align-items-center">
            <i class="material-icons text-success m-r-8">local_shipping</i>
            <mat-card-title class="f-s-18 f-w-600">Add New Truck</mat-card-title>
        </div>
    </mat-card-header>

    <mat-divider></mat-divider>

    <!-- Card Content -->
    <mat-card-content class="p-24">
        <form [formGroup]="truckForm" (ngSubmit)="onSubmit()" class="form-validation">
            <div class="row">

                <div class="col-lg-3 col-md-6">
                    <mat-form-field appearance="outline" class="w-100" color="accent">
                        <mat-label>Truck Number *</mat-label>
                        <input matInput formControlName="truckNumber" required maxlength="50" />
                        <mat-error *ngIf="truckForm.get('truckNumber')?.hasError('required')">This is required.</mat-error>
                        <mat-error *ngIf="truckForm.get('truckNumber')?.hasError('maxlength')">Max length 50.</mat-error>
                    </mat-form-field>
                </div>




                <div class="col-lg-3 col-md-6">
                    <mat-form-field appearance="outline" class="w-100" color="accent">
                        <mat-label>Driver Name *</mat-label>
                        <input matInput formControlName="driverName" required maxlength="100" />
                        <mat-error *ngIf="truckForm.get('driverName')?.hasError('required')">This is required.</mat-error>
                        <mat-error *ngIf="truckForm.get('driverName')?.hasError('maxlength')">Max length 100.</mat-error>
                    </mat-form-field>
                </div>

                <div class="col-lg-3 col-md-6">
                    <mat-form-field appearance="outline" class="w-100" color="accent">
                        <mat-label>License Plate *</mat-label>
                        <input matInput formControlName="licensePlate" required maxlength="20" />
                        <mat-error *ngIf="truckForm.get('licensePlate')?.hasError('required')">This is required.</mat-error>
                        <mat-error *ngIf="truckForm.get('licensePlate')?.hasError('maxlength')">Max length 20.</mat-error>
                    </mat-form-field>
                </div>

                <div class="col-lg-3 col-md-6">
                    <mat-form-field appearance="outline" class="w-100" color="accent">
                        <mat-label>Truck Type *</mat-label>
                        <mat-select formControlName="truckType" required>
                            <mat-option value="delivery">Delivery</mat-option>
                            <mat-option value="pickup">Pickup</mat-option>
                            <mat-option value="transport">Transport</mat-option>
                        </mat-select>
                        <mat-error *ngIf="truckForm.get('truckType')?.hasError('required')">This is required.</mat-error>
                    </mat-form-field>
                </div>

                <div class="col-lg-3 col-md-6">
                    <mat-form-field appearance="outline" class="w-100" color="accent">
                        <mat-label>Status *</mat-label>
                        <mat-select formControlName="status" required>
                            <mat-option value="active">Active</mat-option>
                            <mat-option value="inactive">Inactive</mat-option>
                            <mat-option value="maintenance">Maintenance</mat-option>
                        </mat-select>
                        <mat-error *ngIf="truckForm.get('status')?.hasError('required')">This is required.</mat-error>
                    </mat-form-field>
                </div>

                <div class="col-lg-6">
                    <mat-form-field appearance="outline" class="w-100" color="accent">
                        <mat-label>Notes</mat-label>
                        <textarea matInput formControlName="notes" maxlength="500" rows="3"></textarea>
                        <mat-error *ngIf="truckForm.get('notes')?.hasError('maxlength')">Max length 500.</mat-error>
                    </mat-form-field>
                </div>

                                <!-- Action Buttons -->
                                <div class="col-12">
                                    <div class="col-md-12 btns-row">
                                        <button class="md-button md-raised btn-w-md md-default" type="button" (click)="onReset()">Cancel</button>
                                        <button
                                            class="md-button md-raised btn-w-md md-primary"
                                            type="submit"
                                            [disabled]="truckForm.invalid || isLoading">
                                            <span *ngIf="!isLoading">Save</span>
                                            <span *ngIf="isLoading">Saving...</span>
                                        </button>
                                    </div>
                                </div>

                            </div>
                        </form>
    </mat-card-content>
</mat-card>

<!-- Display submitted data -->
<mat-card class="cardWithShadow m-t-20" *ngIf="submittedData">
    <mat-card-header class="p-24">
        <div class="d-flex align-items-center">
            <mat-icon class="text-success m-r-8">check_circle</mat-icon>
            <mat-card-title class="f-s-18 f-w-600">Submitted Data</mat-card-title>
        </div>
    </mat-card-header>

    <mat-divider></mat-divider>

    <mat-card-content class="p-24">
        <div class="bg-light-primary p-16 rounded">
            <pre class="f-s-12">{{ submittedData | json }}</pre>
        </div>
    </mat-card-content>
</mat-card>
