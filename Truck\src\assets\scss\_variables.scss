@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap");

// font
$font-family: "Plus Jakarta Sans", sans-serif;

// light color variable

// Sidenav
$sidenav-desktop: 270px !default;
$sidenav-mini: 80px !default;
$header-height: 70px !default;

//BorderColor
$borderColor: var(--mat-sys-outline-variant);
$borderformColor: var(--mat-sys-outline-variant);

// custom
$primary: var(--mat-sys-primary);
$secondary: var(--mat-sys-secondary);
$accent: var(--mat-sys-secondary);
$error: var(--mat-sys-error);
$warning: #ffae1f;
$success: #13deb9;
$white: #ffffff;

$light: var(--mat-sys-surface-bright);
$light-primary: var(--mat-sys-primary-fixed-dim);
$light-secondary: var(--mat-sys-secondary-fixed-dim);
$light-accent: var(--mat-sys-secondary-fixed-dim);
$light-error: var(--mat-sys-error-fixed-dim);
$light-warning: #ffad1f40;
$light-success: #13deb940;

// layout
$boxedWidth: 1200px;
$border-radius: 7px;

$card-spacer: 24px;

$text-color: var(--mat-sys-on-background);

$dark-text-secondary: rgba(255, 255, 255, 0.67);
$dark-body-bg: #141a21;
