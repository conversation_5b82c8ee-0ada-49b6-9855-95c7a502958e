<?php
require_once('../connection.php');

class TruckManagement {
    private $conn;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * Save a new truck record
     */
    public function saveTruck($data) {
        try {
            $sql = "INSERT INTO trucks (truck_number, driver_name, license_plate, truck_type, status, notes, created_date, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("ssssssi", 
                $data['truckNumber'],
                $data['driverName'], 
                $data['licensePlate'],
                $data['truckType'],
                $data['status'],
                $data['notes'],
                $_SESSION['user_id']
            );
            
            if ($stmt->execute()) {
                $truckId = $this->conn->insert_id;
                return [
                    'Success' => true,
                    'Result' => 'Truck saved successfully',
                    'TruckID' => $truckId
                ];
            } else {
                return [
                    'Success' => false,
                    'Result' => 'Failed to save truck: ' . $stmt->error
                ];
            }
        } catch (Exception $e) {
            return [
                'Success' => false,
                'Result' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get all trucks
     */
    public function getAllTrucks() {
        try {
            $sql = "SELECT t.*, u.username as created_by_name 
                    FROM trucks t 
                    LEFT JOIN users u ON t.created_by = u.user_id 
                    ORDER BY t.created_date DESC";
            
            $result = $this->conn->query($sql);
            
            if ($result) {
                $trucks = [];
                while ($row = $result->fetch_assoc()) {
                    $trucks[] = $row;
                }
                
                return [
                    'Success' => true,
                    'Result' => $trucks
                ];
            } else {
                return [
                    'Success' => false,
                    'Result' => 'Failed to fetch trucks'
                ];
            }
        } catch (Exception $e) {
            return [
                'Success' => false,
                'Result' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get truck by ID
     */
    public function getTruckById($truckId) {
        try {
            $sql = "SELECT t.*, u.username as created_by_name 
                    FROM trucks t 
                    LEFT JOIN users u ON t.created_by = u.user_id 
                    WHERE t.truck_id = ?";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("i", $truckId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                return [
                    'Success' => true,
                    'Result' => $result->fetch_assoc()
                ];
            } else {
                return [
                    'Success' => false,
                    'Result' => 'Truck not found'
                ];
            }
        } catch (Exception $e) {
            return [
                'Success' => false,
                'Result' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update truck
     */
    public function updateTruck($truckId, $data) {
        try {
            $sql = "UPDATE trucks SET 
                    truck_number = ?, 
                    driver_name = ?, 
                    license_plate = ?, 
                    truck_type = ?, 
                    status = ?, 
                    notes = ?,
                    updated_date = NOW(),
                    updated_by = ?
                    WHERE truck_id = ?";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("ssssssii", 
                $data['truckNumber'],
                $data['driverName'], 
                $data['licensePlate'],
                $data['truckType'],
                $data['status'],
                $data['notes'],
                $_SESSION['user_id'],
                $truckId
            );
            
            if ($stmt->execute()) {
                return [
                    'Success' => true,
                    'Result' => 'Truck updated successfully'
                ];
            } else {
                return [
                    'Success' => false,
                    'Result' => 'Failed to update truck: ' . $stmt->error
                ];
            }
        } catch (Exception $e) {
            return [
                'Success' => false,
                'Result' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete truck
     */
    public function deleteTruck($truckId) {
        try {
            $sql = "DELETE FROM trucks WHERE truck_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("i", $truckId);
            
            if ($stmt->execute()) {
                return [
                    'Success' => true,
                    'Result' => 'Truck deleted successfully'
                ];
            } else {
                return [
                    'Success' => false,
                    'Result' => 'Failed to delete truck: ' . $stmt->error
                ];
            }
        } catch (Exception $e) {
            return [
                'Success' => false,
                'Result' => 'Error: ' . $e->getMessage()
            ];
        }
    }
}
?>
