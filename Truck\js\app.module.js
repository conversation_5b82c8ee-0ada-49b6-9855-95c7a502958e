(function() {
    'use strict';

    // Extend the existing 'app' module instead of creating a new one
    angular
        .module('app')
        .config(truckConfigFunction)
        .run(truckRunFunction);

    truckConfigFunction.$inject = ['$stateProvider', '$urlRouterProvider'];

    function truckConfigFunction($stateProvider, $urlRouterProvider) {

        // Configure states for Truck module
        $stateProvider
            .state('TruckForm', {
                url: '/TruckForm',
                template: '<app-root></app-root>'
            })
            .state('TruckBooking', {
                url: '/TruckBooking',
                template: '<app-root></app-root>'
            });

        // Set default route for Truck module
        $urlRouterProvider.when('', '/TruckForm');
        $urlRouterProvider.when('/', '/TruckForm');
    }

    truckRunFunction.$inject = ['$rootScope'];

    function truckRunFunction($rootScope) {
        // Set current module for sidebar highlighting
        $rootScope.CurrentModule = 'Truck';

        // Set host for navigation
        $rootScope.host = window.location.protocol + '//' + window.location.host + '/';

        console.log('Truck module states added to main app');
    }

})();
