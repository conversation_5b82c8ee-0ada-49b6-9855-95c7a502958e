// Authentication middleware
const authenticateUser = (req, res, next) => {
    // Check if user is logged in via session
    if (!req.session || !req.session.user) {
        return res.status(401).json({
            Success: false,
            Result: 'No Access - Please login'
        });
    }

    // Add user info to request object for use in routes
    req.user = req.session.user;
    next();
};

// Optional authentication (for endpoints that work with or without auth)
const optionalAuth = (req, res, next) => {
    if (req.session && req.session.user) {
        req.user = req.session.user;
    }
    next();
};

// Check user permissions (placeholder for future implementation)
const checkPermission = (permission) => {
    return (req, res, next) => {
        // For now, just check if user is authenticated
        // In the future, implement proper permission checking
        if (!req.user) {
            return res.status(403).json({
                Success: false,
                Result: `No Access to ${permission} Page`
            });
        }
        
        // TODO: Implement actual permission checking logic
        // if (!hasPermission(req.user.ProfileID, permission)) {
        //     return res.status(403).json({
        //         Success: false,
        //         Result: `No Access to ${permission} Page`
        //     });
        // }
        
        next();
    };
};

// Validate session and refresh if needed
const validateSession = (req, res, next) => {
    if (req.session && req.session.user) {
        // Refresh session timeout
        req.session.touch();
    }
    next();
};

module.exports = {
    authenticateUser,
    optionalAuth,
    checkPermission,
    validateSession
};
