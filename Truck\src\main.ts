import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';

// Function to wait for app-root element to be available
function waitForAppRoot() {
  return new Promise<void>((resolve) => {
    const checkForElement = () => {
      const appRoot = document.querySelector('app-root');
      if (appRoot) {
        console.log('app-root element found, bootstrapping Angular 19');
        resolve();
      } else {
        // Check again after a short delay
        setTimeout(checkForElement, 100);
      }
    };
    checkForElement();
  });
}

// Wait for app-root element before bootstrapping
waitForAppRoot().then(() => {
  bootstrapApplication(AppComponent, appConfig)
    .catch((err) => console.error('Angular 19 bootstrap error:', err));
});
