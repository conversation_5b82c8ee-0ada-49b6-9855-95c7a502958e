#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Truck Booking API Server Installation Script');
console.log('================================================\n');

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
    console.error('❌ Node.js 16.0.0 or higher is required');
    console.error(`   Current version: ${nodeVersion}`);
    process.exit(1);
}

console.log(`✅ Node.js version: ${nodeVersion}`);

// Check if package.json exists
if (!fs.existsSync('package.json')) {
    console.error('❌ package.json not found. Please run this script from the server directory.');
    process.exit(1);
}

// Install dependencies
console.log('\n📦 Installing dependencies...');
try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully');
} catch (error) {
    console.error('❌ Failed to install dependencies');
    console.error(error.message);
    process.exit(1);
}

// Create .env file if it doesn't exist
if (!fs.existsSync('.env')) {
    console.log('\n⚙️  Creating .env file...');
    try {
        fs.copyFileSync('.env.example', '.env');
        console.log('✅ .env file created from .env.example');
        console.log('⚠️  Please update the .env file with your database credentials');
    } catch (error) {
        console.error('❌ Failed to create .env file');
        console.error(error.message);
    }
} else {
    console.log('✅ .env file already exists');
}

// Check if database schema exists
const sqlFile = path.join('..', 'database', 'create_trucks_table.sql');
if (fs.existsSync(sqlFile)) {
    console.log('✅ Database schema file found');
    console.log('📋 To set up the database, run:');
    console.log(`   mysql -u your_username -p your_database_name < ${sqlFile}`);
} else {
    console.log('⚠️  Database schema file not found');
}

console.log('\n🎉 Installation completed!');
console.log('\n📝 Next steps:');
console.log('1. Update .env file with your database credentials');
console.log('2. Create MySQL database and run the SQL schema');
console.log('3. Start the server with: npm run dev');
console.log('\n🔗 API will be available at: http://localhost:3001/api/v1');
console.log('🏥 Health check: http://localhost:3001/health');

console.log('\n📚 For more information, see README.md');
