@use "sass:map";
@use "@angular/material" as mat;

@include mat.checkbox-overrides(
  (
    unselected-focus-icon-color:
      var(--mat-checkbox-unselected-hover-state-layer-color),
  )
);

//   styles
html {
  .mdc-checkbox__background {
    border-radius: 4px;
    width: 21px;
    height: 21px;
    border: 1px solid var(--mat-sys-outline);
  }

  .mdc-checkbox .mdc-checkbox__native-control:focus ~ .mdc-checkbox__ripple {
    background-color: var(--mat-checkbox-unselected-hover-state-layer-color);
  }
}
