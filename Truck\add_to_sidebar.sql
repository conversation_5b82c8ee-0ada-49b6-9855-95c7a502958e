-- SQL script to add Truck Management to the sidebar navigation
-- Run this script in your database to add the Truck module to the menu

-- Check if Truck Management already exists
SELECT COUNT(*) as existing_count FROM tabs WHERE TabName = 'Truck Management';

-- 1. Add the main tab for Truck Management (only if it doesn't exist)
INSERT INTO tabs (TabName, TabLink, Status, `Order`, AccountID)
SELECT 'Truck Management', 'Truck', '1', 999, 1
WHERE NOT EXISTS (SELECT 1 FROM tabs WHERE TabName = 'Truck Management');

-- Get the TabID (either newly inserted or existing)
SET @TruckTabID = (SELECT TabID FROM tabs WHERE TabName = 'Truck Management' LIMIT 1);

-- 2. Add the menu item under the Truck tab (only if it doesn't exist)
INSERT INTO left_menus (TabID, TabName, TabLink, Status, `Order`)
SELECT @TruckTabID, 'Truck Management', 'Truck', '1', 1
WHERE NOT EXISTS (SELECT 1 FROM left_menus WHERE TabID = @TruckTabID AND TabName = 'Truck Management');

-- Get the MenuID
SET @TruckMenuID = (SELECT MenuID FROM left_menus WHERE TabID = @TruckTabID AND TabName = 'Truck Management' LIMIT 1);

-- 3. Assign the tab to Administrator profile (ProfileID = 1) if not already assigned
INSERT INTO modules_assignment (ProfileID, TabID, Status)
SELECT 1, @TruckTabID, '1'
WHERE NOT EXISTS (SELECT 1 FROM modules_assignment WHERE ProfileID = 1 AND TabID = @TruckTabID);

-- 4. Assign the menu to Administrator profile if not already assigned
INSERT INTO left_menus_assignment (ProfileID, MenuID, Status)
SELECT 1, @TruckMenuID, '1'
WHERE NOT EXISTS (SELECT 1 FROM left_menus_assignment WHERE ProfileID = 1 AND MenuID = @TruckMenuID);

-- 5. Also assign to other common profiles if they exist
-- Profile ID 2 (if exists)
INSERT INTO modules_assignment (ProfileID, TabID, Status)
SELECT 2, @TruckTabID, '1'
WHERE EXISTS (SELECT 1 FROM profiles WHERE ProfileID = 2)
AND NOT EXISTS (SELECT 1 FROM modules_assignment WHERE ProfileID = 2 AND TabID = @TruckTabID);

INSERT INTO left_menus_assignment (ProfileID, MenuID, Status)
SELECT 2, @TruckMenuID, '1'
WHERE EXISTS (SELECT 1 FROM profiles WHERE ProfileID = 2)
AND NOT EXISTS (SELECT 1 FROM left_menus_assignment WHERE ProfileID = 2 AND MenuID = @TruckMenuID);

-- Verify the insertion
SELECT 'Truck Management tab ID:' as Message, @TruckTabID as TabID;
SELECT 'Truck Management menu ID:' as Message, @TruckMenuID as MenuID;
SELECT 'Setup complete! Truck Management should now appear in the sidebar.' as Status;
