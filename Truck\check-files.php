<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>File Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .exists { color: green; }
        .missing { color: red; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Truck Module File Check</h1>
    
    <div class="info">
        <strong>Current Directory:</strong> <?php echo __DIR__; ?><br>
        <strong>Server Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
    </div>
    
    <h2>Required Files:</h2>
    
    <?php
    $files = [
        'js/main.js',
        'js/polyfills.js',
        'js/styles.css',
        'js/chunk-IYJLWEFY.js',
        'js/chunk-RY3FH4ZG.js'
    ];

    foreach ($files as $file) {
        $path = __DIR__ . '/' . $file;
        $exists = file_exists($path);
        $size = $exists ? filesize($path) : 0;
        $class = $exists ? 'exists' : 'missing';
        
        echo "<div class='$class'>";
        echo $exists ? '✅' : '❌';
        echo " <strong>$file</strong>";
        if ($exists) {
            echo " - " . number_format($size) . " bytes";
            echo " - Last modified: " . date('Y-m-d H:i:s', filemtime($path));
        } else {
            echo " - FILE NOT FOUND";
        }
        echo "</div>";
    }
    ?>
    
    <h2>Directory Contents:</h2>
    <div class="info">
        <?php
        $files = scandir(__DIR__);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $path = __DIR__ . '/' . $file;
                if (is_file($path)) {
                    echo "$file (" . number_format(filesize($path)) . " bytes)<br>";
                } else {
                    echo "$file (directory)<br>";
                }
            }
        }
        ?>
    </div>
    
    <h2>Test File Access:</h2>
    <script>
        // Test direct file access
        const files = ['js/main.js', 'js/polyfills.js', 'js/styles.css'];
        
        files.forEach(file => {
            fetch(file)
                .then(response => {
                    const status = response.ok ? '✅' : '❌';
                    const statusText = response.ok ? 'OK' : `Error ${response.status}`;
                    document.body.innerHTML += `<div>${status} ${file} - ${statusText}</div>`;
                })
                .catch(error => {
                    document.body.innerHTML += `<div>❌ ${file} - ${error.message}</div>`;
                });
        });
    </script>
</body>
</html>
