-- Create trucks table for the Truck Management module
CREATE TABLE IF NOT EXISTS `trucks` (
  `truck_id` int(11) NOT NULL AUTO_INCREMENT,
  `truck_number` varchar(50) NOT NULL,
  `driver_name` varchar(100) NOT NULL,
  `license_plate` varchar(20) NOT NULL,
  `truck_type` enum('pickup','delivery','semi','flatbed') NOT NULL,
  `status` enum('active','maintenance','inactive') DEFAULT 'active',
  `notes` text,
  `created_date` datetime NOT NULL,
  `created_by` int(11) NOT NULL,
  `updated_date` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`truck_id`),
  UNIQUE KEY `truck_number` (`truck_number`),
  UNIQUE KEY `license_plate` (`license_plate`),
  KEY `idx_truck_type` (`truck_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Truck Booking table (main table for truck bookings)
CREATE TABLE IF NOT EXISTS `Truck` (
  `TruckID` int(11) NOT NULL AUTO_INCREMENT,
  `FacilityID` int(11) NOT NULL,
  `ArrivalType` enum('Inbound','Outbound') NOT NULL,
  `ParkingLocationID` int(11) NOT NULL,
  `CarrierID` int(11) NOT NULL,
  `ArrivalDate` date NOT NULL,
  `ArrivalTime` time NOT NULL,
  `LoadType` enum('Pallet','Rack') NOT NULL,
  `LoadNumber` varchar(50) NOT NULL,
  `TruckTypeID` int(11) NOT NULL,
  `TruckReg` varchar(50) NOT NULL,
  `TrailerNumber` varchar(50) NOT NULL,
  `DriverName` varchar(50) NOT NULL,
  `DriverID` varchar(50) DEFAULT NULL,
  `ShipmentTicketID` varchar(50) DEFAULT NULL,
  `ClassificationType` enum('All','UEEE','WEEE') NOT NULL,
  `WasteCollectionPermit` varchar(250) DEFAULT NULL,
  `Notes` text,
  `Status` enum('Reserved','Requested','Confirmed','Cancelled') DEFAULT 'Requested',
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` int(11) NOT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int(11) DEFAULT NULL,
  PRIMARY KEY (`TruckID`),
  KEY `idx_facility` (`FacilityID`),
  KEY `idx_parking_location` (`ParkingLocationID`),
  KEY `idx_carrier` (`CarrierID`),
  KEY `idx_truck_type` (`TruckTypeID`),
  KEY `idx_arrival_date` (`ArrivalDate`),
  KEY `idx_status` (`Status`),
  KEY `idx_created_by` (`CreatedBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ParkingLocation table
CREATE TABLE IF NOT EXISTS `ParkingLocation` (
  `ParkingLocationID` int(11) NOT NULL AUTO_INCREMENT,
  `ParkingLocationName` varchar(100) NOT NULL,
  `Description` text,
  `Capacity` int(11) DEFAULT NULL,
  `Status` enum('0','1') DEFAULT '1',
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` int(11) NOT NULL,
  PRIMARY KEY (`ParkingLocationID`),
  KEY `idx_status` (`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create Carrier table
CREATE TABLE IF NOT EXISTS `Carrier` (
  `CarrierID` int(11) NOT NULL AUTO_INCREMENT,
  `CarrierName` varchar(100) NOT NULL,
  `ContactPerson` varchar(100) DEFAULT NULL,
  `Phone` varchar(20) DEFAULT NULL,
  `Email` varchar(100) DEFAULT NULL,
  `Address` text,
  `StatusID` enum('0','1') DEFAULT '1',
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` int(11) NOT NULL,
  PRIMARY KEY (`CarrierID`),
  KEY `idx_status` (`StatusID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create TruckType table
CREATE TABLE IF NOT EXISTS `TruckType` (
  `TruckTypeID` int(11) NOT NULL AUTO_INCREMENT,
  `TruckTypeName` varchar(50) NOT NULL,
  `Description` text,
  `MaxWeight` decimal(10,2) DEFAULT NULL,
  `MaxVolume` decimal(10,2) DEFAULT NULL,
  `Status` enum('0','1') DEFAULT '1',
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` int(11) NOT NULL,
  PRIMARY KEY (`TruckTypeID`),
  KEY `idx_status` (`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create facility table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS `facility` (
  `FacilityID` int(11) NOT NULL AUTO_INCREMENT,
  `FacilityName` varchar(100) NOT NULL,
  `Address` text,
  `City` varchar(50) DEFAULT NULL,
  `State` varchar(50) DEFAULT NULL,
  `ZipCode` varchar(10) DEFAULT NULL,
  `Country` varchar(50) DEFAULT NULL,
  `Status` enum('0','1') DEFAULT '1',
  `CreatedDate` datetime NOT NULL,
  `CreatedBy` int(11) NOT NULL,
  PRIMARY KEY (`FacilityID`),
  KEY `idx_status` (`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing

-- Sample facilities
INSERT INTO `facility` (`FacilityName`, `Address`, `City`, `State`, `Status`, `CreatedDate`, `CreatedBy`) VALUES
('Main Warehouse', '123 Industrial Blvd', 'Los Angeles', 'CA', '1', NOW(), 1),
('Distribution Center', '456 Logistics Ave', 'Chicago', 'IL', '1', NOW(), 1),
('Processing Plant', '789 Manufacturing St', 'Houston', 'TX', '1', NOW(), 1);

-- Sample parking locations
INSERT INTO `ParkingLocation` (`ParkingLocationName`, `Description`, `Capacity`, `Status`, `CreatedDate`, `CreatedBy`) VALUES
('Dock A1', 'Loading dock for inbound trucks', 5, '1', NOW(), 1),
('Dock A2', 'Loading dock for outbound trucks', 5, '1', NOW(), 1),
('Dock B1', 'Heavy equipment loading area', 3, '1', NOW(), 1),
('Dock B2', 'Express delivery dock', 4, '1', NOW(), 1),
('Parking Lot C', 'Overflow parking area', 20, '1', NOW(), 1);

-- Sample carriers
INSERT INTO `Carrier` (`CarrierName`, `ContactPerson`, `Phone`, `Email`, `StatusID`, `CreatedDate`, `CreatedBy`) VALUES
('FedEx', 'John Smith', '555-0101', '<EMAIL>', '1', NOW(), 1),
('UPS', 'Jane Doe', '555-0102', '<EMAIL>', '1', NOW(), 1),
('DHL', 'Mike Johnson', '555-0103', '<EMAIL>', '1', NOW(), 1),
('Local Transport Co', 'Sarah Wilson', '555-0104', '<EMAIL>', '1', NOW(), 1),
('Express Logistics', 'Tom Brown', '555-0105', '<EMAIL>', '1', NOW(), 1);

-- Sample truck types
INSERT INTO `TruckType` (`TruckTypeName`, `Description`, `MaxWeight`, `MaxVolume`, `Status`, `CreatedDate`, `CreatedBy`) VALUES
('Small Van', 'Delivery van for small packages', 3500.00, 15.00, '1', NOW(), 1),
('Medium Truck', 'Standard delivery truck', 7500.00, 35.00, '1', NOW(), 1),
('Large Truck', 'Heavy duty truck for large loads', 15000.00, 75.00, '1', NOW(), 1),
('Semi Trailer', 'Semi-trailer for long distance', 40000.00, 150.00, '1', NOW(), 1),
('Refrigerated Truck', 'Temperature controlled transport', 10000.00, 50.00, '1', NOW(), 1);

-- Sample truck bookings
INSERT INTO `Truck` (
    `FacilityID`, `ArrivalType`, `ParkingLocationID`, `CarrierID`,
    `ArrivalDate`, `ArrivalTime`, `LoadType`, `LoadNumber`,
    `TruckTypeID`, `TruckReg`, `TrailerNumber`, `DriverName`,
    `ClassificationType`, `Status`, `CreatedDate`, `CreatedBy`
) VALUES
(1, 'Inbound', 1, 1, '2024-01-15', '09:00:00', 'Pallet', 'LD001', 2, 'ABC123', 'TR001', 'John Driver', 'All', 'Requested', NOW(), 1),
(1, 'Outbound', 2, 2, '2024-01-15', '14:00:00', 'Rack', 'LD002', 3, 'XYZ789', 'TR002', 'Jane Driver', 'UEEE', 'Reserved', NOW(), 1),
(2, 'Inbound', 3, 3, '2024-01-16', '10:30:00', 'Pallet', 'LD003', 1, 'DEF456', 'TR003', 'Mike Driver', 'WEEE', 'Confirmed', NOW(), 1);

-- Add foreign key constraints if users table exists
-- ALTER TABLE `trucks` ADD CONSTRAINT `fk_trucks_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`);
-- ALTER TABLE `trucks` ADD CONSTRAINT `fk_trucks_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`user_id`);

-- Insert sample data for basic trucks table
INSERT INTO `trucks` (`truck_number`, `driver_name`, `license_plate`, `truck_type`, `status`, `notes`, `created_date`, `created_by`) VALUES
('TRK001', 'John Doe', 'ABC123', 'delivery', 'active', 'Primary delivery truck', NOW(), 1),
('TRK002', 'Jane Smith', 'XYZ789', 'pickup', 'active', 'Pickup truck for small loads', NOW(), 1),
('TRK003', 'Mike Johnson', 'DEF456', 'semi', 'maintenance', 'Large semi truck - currently in maintenance', NOW(), 1);
