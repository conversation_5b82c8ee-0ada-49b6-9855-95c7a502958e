@echo off
echo Updating index.php with correct file references...

REM Build the Angular application
echo Building Angular application...
call ng build
if %errorlevel% neq 0 (
    echo ERROR: Angular build failed!
    pause
    exit /b 1
)

REM Get the generated file names from dist/browser/index.html
echo Extracting file names from generated index.html...

REM This is a simple approach - in production you might want a more robust solution
echo.
echo Please manually update index.php with the file names from dist/browser/index.html
echo.
echo Current files in dist/browser/:
dir /b dist\browser\*.js
dir /b dist\browser\*.css
echo.
echo Update these references in index.php:
echo - CSS file: styles-*.css
echo - JS files: main-*.js, polyfills-*.js
echo - Chunk files: chunk-*.js
echo.
pause
