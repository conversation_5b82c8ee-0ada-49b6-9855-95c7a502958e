<?php
// Simple file server to bypass .htaccess issues
$file = $_GET['file'] ?? '';

// Security: only allow specific files
$allowed_files = [
    'main.js' => 'application/javascript',
    'polyfills.js' => 'application/javascript', 
    'styles.css' => 'text/css',
    'chunk-IYJLWEFY.js' => 'application/javascript',
    'chunk-RY3FH4ZG.js' => 'application/javascript'
];

if (!isset($allowed_files[$file])) {
    http_response_code(404);
    echo "File not allowed: $file";
    exit;
}

$filepath = __DIR__ . '/' . $file;

if (!file_exists($filepath)) {
    http_response_code(404);
    echo "File not found: $file";
    exit;
}

// Set correct MIME type
header('Content-Type: ' . $allowed_files[$file]);
header('Content-Length: ' . filesize($filepath));

// Output the file
readfile($filepath);
?>
