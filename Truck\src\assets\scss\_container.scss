@use "variables" as *;

*,
:after,
:before {
  box-sizing: border-box;
}

body {
  font-family: $font-family;
  line-height: 1.334rem;
  overflow-x: hidden;
  color: var(--mat-sys-on-background);
}

.mainWrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
  flex: 1;
  height: 100%;
}

.container {
  max-width: 1200px;
  padding-left: 24px;
  padding-right: 24px;
  margin: 0 auto;

  &.full-width {
    display: flex;
    align-items: center;
    width: 100%;
  }
}

.pageWrapper {
  padding: 24px;
  min-height: calc(100vh - 70px);

  margin: 0 auto;

  &.maxWidth {
    max-width: $boxedWidth;
  }
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.shadow-none {
  box-shadow: none !important;
}

.rounded {
  border-radius: $border-radius !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 25px !important;
}

.overflow-hidden {
  overflow: hidden;
}

.text-decoration-none {
  text-decoration: none;
}

.position-relative {
  position: relative;
}

.table-responsive {
  overflow-x: auto;

  td,
  mat-cell {
    white-space: nowrap;
    padding: 16px;
  }
}

.op-5 {
  opacity: 0.5;
}

.cursor-pointer {
  cursor: pointer;
}

.avatar-group {
  img {
    border: 2px solid $white;
    margin-right: -5px;

    &:last-child {
      margin-right: 0;
    }
  }
}

a {
  color: var(--mat-sys-on-background);

  &:hover {
    color: $primary;
  }
}

.filter-sidebar {
  width: 290px;
  position: fixed;
  right: -290px;
  top: 0;
  z-index: 10;
  height: 100%;
  transition: all 0.3s ease-in-out;
  box-shadow: var(--mat-sys-level3);
  background-color: var(--mat-sys-background);

  &.open {
    right: 0;
  }
}

pre {
  margin: 0;
  white-space: nowrap;
}

td.hljs-ln-line.hljs-ln-numbers {
  padding-right: 10px;
}

@media (min-width: 768px) {
  html .flex-sm-row {
    flex-direction: row !important;
  }
}

.mat-drawer[style*="visibility: hidden"] {
  display: none;
}