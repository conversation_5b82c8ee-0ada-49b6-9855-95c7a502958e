.card-overlay {
  position: absolute;
  top: 0;
  width: 100%;
  height: 225px;
}

.user-category {
  margin-top: -45px;
  z-index: 1;
  position: relative;
}

.featured-card {
  height: 400px;
  &:before {
    content: "";
    position: absolute;
    background: rgba(33, 33, 33, 0.6);
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  .featured-overlay {
    position: absolute;
    z-index: 2;
    top: 0;
    width: 100%;
    height: 100%;
  }
}

.detail-card-overlay {
  position: absolute;
  top: 0;
  width: 100%;
  height: 440px;
}

@media (min-width: 1200px) {
  .blog-title {
    font-size: 36px;
    line-height: 40px;
  }
}
