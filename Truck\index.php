<?php
session_start();
if(!isset($_SESSION['user'])) {
    header("Location: ../index.php");
    exit;
}
?>
<!doctype html>
<html class="no-js">
    <head>
        <meta charset="utf-8">
        <meta http-equiv='X-UA-Compatible' content='IE=edge'>
        <title>Truck Management</title>
        <meta name="description" content="eViridis Material Management">
        <meta name="keywords" content="eViridis,Recycling Solutions">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-touch-fullscreen" content="yes">

        <link rel="apple-touch-icon" sizes="57x57" href="../assets/images/favicons/apple-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="../assets/images/favicons/apple-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicons/apple-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="../assets/images/favicons/apple-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicons/apple-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="../assets/images/favicons/apple-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="../assets/images/favicons/apple-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="../assets/images/favicons/apple-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="../assets/images/favicons/apple-icon-180x180.png">
        <link rel="icon" type="image/png" sizes="192x192"  href="../assets/images/favicons/android-icon-192x192.png">
        <link rel="icon" type="image/png" sizes="32x32" href="../assets/images/favicons/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="96x96" href="../assets/images/favicons/favicon-96x96.png">
        <link rel="icon" type="image/png" sizes="16x16" href="../assets/images/favicons/favicon-16x16.png">
        <link rel="manifest" href="../assets/images/favicons/manifest.json">
        <meta name="msapplication-TileColor" content="#ffffff">
        <meta name="msapplication-TileImage" content="../assets/images/favicons/ms-icon-144x144.png">
        <meta name="theme-color" content="#ffffff">

        <!-- Needs images, font... therefore can not be part of main.css -->
        <link rel="stylesheet" href="../vendors/material-design-icons/iconfont/material-icons.css" rel="stylesheet" >
        <link rel="stylesheet" href="../node_modules/font-awesome/css/font-awesome.min.css">
        <!-- end Needs images -->

        <!-- build:css({.tmp,client}) styles/main.css -->
        <link rel="stylesheet" href="../node_modules/angular-material/angular-material.min.css">
        <link rel="stylesheet" href="../styles/main.css">
        <!-- endbuild -->

        <!-- Angular 19 Built Files -->
        <link rel="stylesheet" href="dist/browser/styles.css?v=<?php echo time(); ?>" media="print" onload="this.media='all'">
        <noscript><link rel="stylesheet" href="dist/browser/styles.css?v=<?php echo time(); ?>"></noscript>

        <!-- Base href for Angular routing -->
        <base href="/Truck/">
    </head>
    <body data-ng-app="app"
          id="app"
          class="app"
          data-custom-page
          data-ng-controller="AppCtrl"
          data-ng-class=" { 'layout-boxed': main.layout === 'boxed',
                            'nav-collapsed-min': main.isMenuCollapsed
          } ">
        <!--[if lt IE 9]>
            <div class="lt-ie9-bg">
                <p class="browsehappy">You are using an <strong>outdated</strong> browser.</p>
                <p>Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
            </div>
        <![endif]-->

        <!-- Header using existing header.php -->
        <header data-ng-include=" '../app/layout/header.php' "
                 id="header"
                 class="header-container "
                 data-ng-class="{ 'header-fixed': main.fixedHeader,
                                  'bg-white': ['11','12','13','14','15','16','21'].indexOf(main.skin) >= 0,
                                  'bg-dark': main.skin === '31',
                                  'bg-primary': ['22','32'].indexOf(main.skin) >= 0,
                                  'bg-success': ['23','33'].indexOf(main.skin) >= 0,
                                  'bg-info': ['24','34'].indexOf(main.skin) >= 0,
                                  'bg-warning': ['25','35'].indexOf(main.skin) >= 0,
                                  'bg-danger': ['26','36'].indexOf(main.skin) >= 0
                 }"></header>

        <!-- Main Container with Sidebar -->
        <div class="main-container"
             data-ng-class="{ 'app-nav-horizontal': main.menu === 'horizontal' }">

            <!-- Sidebar using existing sidebar.php -->
            <aside data-ng-include=" '../sidebar.php' "
                   id="nav-container"
                   class="nav-container"
                   data-ng-class="{ 'nav-fixed': main.fixedSidebar,
                                    'nav-horizontal': main.menu === 'horizontal',
                                    'nav-vertical': main.menu === 'vertical',
                                    'bg-white': ['31','32','33','34','35','36'].indexOf(main.skin) >= 0,
                                    'bg-dark': ['31','32','33','34','35','36'].indexOf(main.skin) < 0
                   }">
            </aside>

            <!-- Content Area -->
            <div id="content" class="content-container">
                <section data-ui-view
                         class="view-container {{main.pageTransition.class}}">
                    <!-- Angular 19 Application Root will be loaded via AngularJS routing -->
                </section>
            </div>

        </div>

        <!-- build:js scripts/vendor.js -->
        <script src="../node_modules/jquery/dist/jquery.min.js"></script>
        <script src="../node_modules/angular/angular.min.js"></script>
        <script src="../node_modules/angular-sanitize/angular-sanitize.js"></script>
        <script src="../node_modules/angular-animate/angular-animate.min.js"></script>
        <script src="../node_modules/angular-aria/angular-aria.min.js"></script>
        <script src="../node_modules/angular-messages/angular-messages.min.js"></script>
        <script src="../node_modules/@uirouter/angularjs/release/angular-ui-router.min.js"></script>
        <script src="../node_modules/oclazyload/dist/ocLazyLoad.js"></script>
        <!-- endbuild -->

        <!-- build:js scripts/ui.js -->
        <script src="../node_modules/angular-translate/dist/angular-translate.min.js"></script>
        <script src="../node_modules/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
        <script src="../node_modules/angular-material/angular-material.min.js"></script>
        <script src="../node_modules/angular-scroll/angular-scroll.min.js"></script>
        <script src="../node_modules/jquery-slimscroll/jquery.slimscroll.min.js"></script>
        <script src="../vendors/echarts.js"></script>
        <script src="../vendors/ngecharts.js"></script>
        <script src="../node_modules/angular-material-data-table/dist/md-data-table.min.js"></script>
        <script src="../node_modules/angular-validation-match/dist/angular-validation-match.min.js"></script>
        <!-- endbuild -->

        <!-- Essential global scripts -->
        <script type="text/javascript" src="../config.js"></script>
        <script src="../global.js"></script>

        <!-- build:js scripts/app.js -->
        <!-- inject:js -->
        <!-- Load main app.module.js FIRST -->
        <script src="../app/app.module.js"></script>

        <!-- Then load core modules -->
        <script src="../app/core/core.module.js"></script>
        <script src="../app/chart/chart.module.js"></script>
        <script src="../app/form/form.module.js"></script>
        <script src="../app/form/formValidation.module.js"></script>
        <script src="../app/layout/layout.module.js"></script>
        <script src="../app/ui/ui.module.js"></script>
        <script src="../app/page/page.module.js"></script>
        <script src="../app/table/table.module.js"></script>

        <!-- Then load config and controllers -->
        <script src="../app/core/app.config.js"></script>
        <script src="app/core/app.controller.js"></script>
        <script src="../app/core/config.lazyload.js"></script>

        <script src="../app/core/i18n.js"></script>

        <script src="../app/layout/customizer.controller.js"></script>
        <script src="../app/layout/layout.diretive.js"></script>

        <script src="../app/layout/sidebar.directive.js"></script>
        <script src="../app/ui/ui.directive.js"></script>

        <script src="../app/core/angular-file-upload.min.js"></script>
        <script src="../app/core/moment.min.js"></script>
        <script src="../app/core/moment-timezone-with-data.min.js"></script>
        <script src="../app/core/angular-moment.js"></script>
        <!-- endinject -->
        <!-- endbuild -->

        <!-- Truck Module AngularJS Scripts -->
        <script src="js/app.module.js"></script>

        <!-- Session data for Angular 19 -->
        <script>
            window.sessionData = {
                user: <?php echo json_encode($_SESSION['user'] ?? null); ?>,
                userId: <?php echo json_encode($_SESSION['user_id'] ?? null); ?>,
                facilityId: <?php echo json_encode($_SESSION['facility_id'] ?? null); ?>,
                permissions: <?php echo json_encode($_SESSION['permissions'] ?? []); ?>
            };
        </script>

        <!-- Angular 19 Built Scripts -->
        <?php
        // Automatically detect chunk files
        $browserDir = __DIR__ . '/dist/browser/';
        if (is_dir($browserDir)) {
            $files = scandir($browserDir);
            foreach ($files as $file) {
                if (preg_match('/^chunk-.*\.js$/', $file)) {
                    echo '<link rel="modulepreload" href="dist/browser/' . $file . '?v=' . time() . '">' . "\n        ";
                }
            }
        }
        ?>
        <link rel="stylesheet" href="dist/browser/styles.css?v=<?php echo time(); ?>">
        <script src="dist/browser/polyfills.js?v=<?php echo time(); ?>" type="module"></script>
        <script src="dist/browser/main.js?v=<?php echo time(); ?>" type="module"></script>
    </body>
</html>

