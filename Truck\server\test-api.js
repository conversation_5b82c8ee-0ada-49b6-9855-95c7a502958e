#!/usr/bin/env node

const http = require('http');

const API_BASE = 'http://localhost:3001';
const API_PREFIX = '/api/v1';

// Test configuration
const tests = [
    {
        name: 'Health Check',
        method: 'GET',
        path: '/health',
        expectAuth: false
    },
    {
        name: 'Test Login',
        method: 'POST',
        path: `${API_PREFIX}/auth/test-login`,
        expectAuth: false,
        data: {}
    },
    {
        name: 'Get Facilities',
        method: 'GET',
        path: `${API_PREFIX}/truck-booking/facilities`,
        expectAuth: true
    },
    {
        name: 'Get Parking Locations',
        method: 'GET',
        path: `${API_PREFIX}/truck-booking/parking-locations`,
        expectAuth: true
    },
    {
        name: 'Get Carriers',
        method: 'GET',
        path: `${API_PREFIX}/truck-booking/carriers`,
        expectAuth: true
    },
    {
        name: 'Get Truck Types',
        method: 'GET',
        path: `${API_PREFIX}/truck-booking/truck-types`,
        expectAuth: true
    }
];

let sessionCookie = '';

function makeRequest(test) {
    return new Promise((resolve) => {
        const data = test.data ? JSON.stringify(test.data) : '';
        
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: test.path,
            method: test.method,
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': data.length
            }
        };

        // Add session cookie if available and auth is expected
        if (sessionCookie && test.expectAuth) {
            options.headers['Cookie'] = sessionCookie;
        }

        const req = http.request(options, (res) => {
            let responseData = '';

            // Capture session cookie from login
            if (test.name === 'Test Login' && res.headers['set-cookie']) {
                sessionCookie = res.headers['set-cookie'][0];
            }

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsed = JSON.parse(responseData);
                    resolve({
                        status: res.statusCode,
                        data: parsed,
                        success: res.statusCode >= 200 && res.statusCode < 300
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        data: responseData,
                        success: false,
                        error: 'Invalid JSON response'
                    });
                }
            });
        });

        req.on('error', (error) => {
            resolve({
                status: 0,
                success: false,
                error: error.message
            });
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function runTests() {
    console.log('🧪 Testing Truck Booking API');
    console.log('============================\n');

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
        process.stdout.write(`Testing ${test.name}... `);
        
        const result = await makeRequest(test);
        
        if (result.success) {
            console.log('✅ PASS');
            passed++;
            
            // Show some response data for successful tests
            if (result.data && result.data.Success) {
                if (Array.isArray(result.data.Result)) {
                    console.log(`   → Returned ${result.data.Result.length} items`);
                } else if (typeof result.data.Result === 'string') {
                    console.log(`   → ${result.data.Result}`);
                }
            }
        } else {
            console.log('❌ FAIL');
            failed++;
            console.log(`   → Status: ${result.status}`);
            if (result.error) {
                console.log(`   → Error: ${result.error}`);
            } else if (result.data && result.data.Result) {
                console.log(`   → Message: ${result.data.Result}`);
            }
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n📊 Test Results');
    console.log('================');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

    if (failed === 0) {
        console.log('\n🎉 All tests passed! API is working correctly.');
    } else {
        console.log('\n⚠️  Some tests failed. Check the server logs and database connection.');
    }
}

// Check if server is running
console.log('🔍 Checking if server is running...');
makeRequest({ name: 'Server Check', method: 'GET', path: '/health', expectAuth: false })
    .then((result) => {
        if (result.success) {
            console.log('✅ Server is running\n');
            runTests();
        } else {
            console.log('❌ Server is not running or not responding');
            console.log('💡 Start the server with: npm run dev');
            process.exit(1);
        }
    })
    .catch((error) => {
        console.log('❌ Failed to connect to server');
        console.log('💡 Make sure the server is running on port 3001');
        process.exit(1);
    });
