@use "@angular/material" as mat;

html.dark-theme {
  color-scheme: dark;
  --mat-sys-background: #141a21;
  --mat-sys-on-background: rgba(255, 255, 255, 0.9);
  --mat-sys-on-primary: #fff;
  --mat-sys-surface-bright: #ffffff05;
  --mat-sys-surface: #141a21;
  --mat-sys-surface-container: #141a21;
  --mat-sys-surface-container-low: #141a21;
  --mat-sys-outline-variant: #2e3f50;
  --mat-sys-outline: #2e3f50;
  --mat-form-field-outlined-hover-outline-color: #465670;
  --mat-checkbox-unselected-hover-state-layer-color: #19212a;
  --mat-menu-item-hover-state-layer-color: #19212a;
  --mat-button-toggle-state-layer-color: #19212a;
  --mat-option-focus-state-layer-color: #19212a;
  --mat-option-hover-state-layer-color: #19212a;
  --mat-slide-toggle-unselected-track-color: #19212a;
  --mat-stepper-header-focus-state-layer-color: #19212a;
  --mat-stepper-header-hover-state-layer-color: #19212a;
  --mat-expansion-header-hover-state-layer-color: #19212a;
  @include mat.card-overrides((elevated-container-color: #1c252e,
      elevated-container-elevation: rgba(0, 0, 0, 0.2) 0px 0px 2px 0px,
      subtitle-text-color: rgba(255, 255, 255, 0.67),
    ));
  @include mat.theme-overrides((level1: 0px 12px 24px -4px rgba(143, 176, 210, 0.02),
      level2: 0px 12px 24px -4px rgba(143, 176, 210, 0.02),
      level3: 0px 12px 24px -4px rgba(143, 176, 210, 0.02),
    ));
}