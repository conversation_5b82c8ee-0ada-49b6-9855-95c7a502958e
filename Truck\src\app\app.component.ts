import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TruckFormComponent } from './truck-form/truck-form.component';
import { TruckBookingComponent } from './truck-booking/truck-booking.component';

@Component({
  selector: 'app-root',
  imports: [CommonModule, TruckFormComponent, TruckBookingComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'Truck Management';
  currentView = 'truck-form';

  ngOnInit() {
    // Simple detection of which page we're on
    this.detectCurrentPage();

    // Listen for hash changes to update the view
    window.addEventListener('hashchange', () => {
      this.detectCurrentPage();
    });
  }

  private detectCurrentPage() {
    const hash = window.location.hash;

    if (hash.includes('TruckBooking')) {
      this.currentView = 'truck-booking';
    } else {
      this.currentView = 'truck-form';
    }

    console.log('Current page detected:', this.currentView, 'Hash:', hash);
  }
}
