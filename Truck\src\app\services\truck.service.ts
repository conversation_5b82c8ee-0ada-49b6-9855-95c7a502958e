import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Truck {
  truck_id?: number;
  truckNumber: string;
  driverName: string;
  licensePlate: string;
  truckType: 'pickup' | 'delivery' | 'semi' | 'flatbed';
  status: 'active' | 'maintenance' | 'inactive';
  notes?: string;
  created_date?: string;
  created_by?: number;
  updated_date?: string;
  updated_by?: number;
}

export interface ApiResponse<T> {
  Success: boolean;
  Result: T;
  TruckID?: number;
}

@Injectable({
  providedIn: 'root'
})
export class TruckService {
  private apiUrl = 'includes/truck_submit.php';

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) { }

  /**
   * Save a new truck
   */
  saveTruck(truck: Truck): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(
      `${this.apiUrl}?ajax=SaveTruck`,
      truck,
      this.httpOptions
    );
  }

  /**
   * Get all trucks
   */
  getAllTrucks(): Observable<ApiResponse<Truck[]>> {
    return this.http.get<ApiResponse<Truck[]>>(
      `${this.apiUrl}?ajax=GetAllTrucks`
    );
  }

  /**
   * Get truck by ID
   */
  getTruckById(truckId: number): Observable<ApiResponse<Truck>> {
    return this.http.get<ApiResponse<Truck>>(
      `${this.apiUrl}?ajax=GetTruckById&truckId=${truckId}`
    );
  }

  /**
   * Update truck
   */
  updateTruck(truck: Truck): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(
      `${this.apiUrl}?ajax=UpdateTruck`,
      truck,
      this.httpOptions
    );
  }

  /**
   * Delete truck
   */
  deleteTruck(truckId: number): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(
      `${this.apiUrl}?ajax=DeleteTruck`,
      { truckId },
      this.httpOptions
    );
  }
}
