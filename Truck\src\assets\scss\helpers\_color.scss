@use "sass:meta";
@use "../variables" as *;

@mixin syntax-colors($args...) {
  @each $name, $color in meta.keywords($args) {
    html .bg-#{$name} {
      background-color: $color !important;
    }

    html .text-#{$name} {
      color: $color !important;
    }
  }
}

@include syntax-colors($primary: var(--mat-sys-primary),
  $secondary: var(--mat-sys-secondary),
  $success: $success,
  $warning: $warning,
  $error: var(--mat-sys-error),
  $white: $white,
  $light: $light,
  $light-error: var(--mat-sys-error-fixed-dim),
  $light-secondary: var(--mat-sys-secondary-fixed-dim),
  $light-success: $light-success,
  $light-warning: $light-warning,
  $light-primary: var(--mat-sys-primary-fixed-dim));

.fill-warning svg {
  fill: $warning;
  color: $warning;
}

.fill-light svg {
  fill: $light;
  color: $light;
}