# Truck Booking API Server

A Node.js Express API server for the Truck Booking Management system, replacing the PHP backend.

## Features

- ✅ RESTful API endpoints for truck booking management
- ✅ MySQL database integration with connection pooling
- ✅ Session-based authentication
- ✅ Input validation with Joi
- ✅ Security middleware (Helmet, CORS, Rate limiting)
- ✅ Comprehensive error handling
- ✅ Logging with Morgan
- ✅ Environment-based configuration

## Prerequisites

- Node.js 16.0.0 or higher
- MySQL 5.7 or higher
- npm or yarn package manager

## Installation

1. **Navigate to the server directory:**
   ```bash
   cd Truck/server
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your database credentials and configuration:
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=your_username
   DB_PASSWORD=your_password
   DB_NAME=your_database_name
   PORT=3001
   SESSION_SECRET=your_super_secret_session_key
   ```

4. **Set up the database:**
   - Create a MySQL database
   - Run the SQL script to create tables:
     ```bash
     mysql -u your_username -p your_database_name < ../database/create_trucks_table.sql
     ```

5. **Start the server:**
   ```bash
   # Development mode (with auto-restart)
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Base URL
```
http://localhost:3001/api/v1
```

### Authentication
- `POST /api/v1/auth/test-login` - Test login (development only)
- `POST /api/v1/auth/logout` - Logout

### Truck Booking Endpoints

#### Master Data
- `GET /api/v1/truck-booking/facilities` - Get all facilities
- `GET /api/v1/truck-booking/parking-locations` - Get all parking locations
- `GET /api/v1/truck-booking/carriers` - Get all carriers
- `GET /api/v1/truck-booking/truck-types` - Get all truck types

#### Truck Booking CRUD
- `POST /api/v1/truck-booking/save` - Create new truck booking
- `GET /api/v1/truck-booking/:id` - Get truck booking by ID
- `PUT /api/v1/truck-booking/:id` - Update truck booking
- `DELETE /api/v1/truck-booking/:id` - Delete truck booking
- `POST /api/v1/truck-booking/list` - Get truck list with filters and pagination
- `PATCH /api/v1/truck-booking/:id/status` - Change booking status

### Request/Response Format

All API responses follow this format:
```json
{
  "Success": true|false,
  "Result": "data or error message"
}
```

#### Example: Create Truck Booking
```bash
POST /api/v1/truck-booking/save
Content-Type: application/json

{
  "FacilityID": 1,
  "ArrivalType": "Inbound",
  "ParkingLocationID": 1,
  "CarrierID": 1,
  "ArrivalDate": "2024-01-15",
  "ArrivalTime": "09:00",
  "LoadType": "Pallet",
  "LoadNumber": "LD001",
  "TruckTypeID": 2,
  "TruckReg": "ABC123",
  "TrailerNumber": "TR001",
  "DriverName": "John Driver",
  "ClassificationType": "All",
  "Status": "Requested"
}
```

#### Example: Get Truck List with Filters
```bash
POST /api/v1/truck-booking/list
Content-Type: application/json

{
  "filters": {
    "Status": "Requested",
    "ArrivalDate": "2024-01-15"
  },
  "pagination": {
    "limit": 10,
    "offset": 0
  }
}
```

## Updating Angular Frontend

To use the Node.js API instead of PHP, update the Angular service URLs:

### Before (PHP):
```typescript
this.http.post('/Truck/includes/truck_submit.php', 'ajax=GetFacilities', {
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
})
```

### After (Node.js):
```typescript
this.http.get('/api/v1/truck-booking/facilities')
```

## Development

### Available Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with auto-restart
- `npm test` - Run tests (when implemented)

### Project Structure
```
server/
├── config/
│   └── database.js          # Database configuration
├── middleware/
│   ├── auth.js             # Authentication middleware
│   └── validation.js       # Input validation
├── models/
│   └── TruckBooking.js     # Database models
├── routes/
│   └── truckBooking.js     # API routes
├── .env.example            # Environment variables template
├── package.json            # Dependencies and scripts
├── server.js              # Main server file
└── README.md              # This file
```

## Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Prevent abuse
- **Input Validation**: Joi schema validation
- **Session Management**: Secure session handling
- **SQL Injection Prevention**: Parameterized queries

## Health Check

Check if the server is running:
```bash
GET http://localhost:3001/health
```

## Troubleshooting

1. **Database Connection Issues:**
   - Verify database credentials in `.env`
   - Ensure MySQL server is running
   - Check if database exists

2. **Port Already in Use:**
   - Change PORT in `.env` file
   - Kill existing process: `lsof -ti:3001 | xargs kill`

3. **CORS Issues:**
   - Update CORS_ORIGIN in `.env`
   - Ensure frontend URL is included

## Migration from PHP

The Node.js API maintains compatibility with the existing PHP endpoints:

| PHP Endpoint | Node.js Endpoint | Method |
|-------------|------------------|---------|
| `ajax=GetFacilities` | `/facilities` | GET |
| `ajax=GetParkingLocations` | `/parking-locations` | GET |
| `ajax=GetCarriers` | `/carriers` | GET |
| `ajax=GetTruckTypes` | `/truck-types` | GET |
| `ajax=TruckSave` | `/save` | POST |
| `ajax=GetTruckDetails` | `/:id` | GET |
| `ajax=GetTruckList` | `/list` | POST |

## License

MIT License
