<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    echo json_encode([
        'Success' => false,
        'Result' => 'No Access - Please login'
    ]);
    exit;
}

require_once('../database/truck.class.php');
require_once('../database/truck_booking.class.php');

$truckManager = new TruckManagement();
$bookingManager = new TruckBookingClass();

// Get the request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['ajax'] ?? $_POST['ajax'] ?? '';

try {
    switch ($action) {
        case 'SaveTruck':
            if ($method === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                
                // Validate required fields
                $required = ['truckNumber', 'driverName', 'licensePlate', 'truckType'];
                foreach ($required as $field) {
                    if (empty($input[$field])) {
                        echo json_encode([
                            'Success' => false,
                            'Result' => ucfirst($field) . ' is required'
                        ]);
                        exit;
                    }
                }
                
                $result = $truckManager->saveTruck($input);
                echo json_encode($result);
            } else {
                echo json_encode([
                    'Success' => false,
                    'Result' => 'Invalid request method'
                ]);
            }
            break;
            
        case 'GetAllTrucks':
            if ($method === 'GET') {
                $result = $truckManager->getAllTrucks();
                echo json_encode($result);
            } else {
                echo json_encode([
                    'Success' => false,
                    'Result' => 'Invalid request method'
                ]);
            }
            break;
            
        case 'GetTruckById':
            if ($method === 'GET') {
                $truckId = $_GET['truckId'] ?? 0;
                if ($truckId > 0) {
                    $result = $truckManager->getTruckById($truckId);
                    echo json_encode($result);
                } else {
                    echo json_encode([
                        'Success' => false,
                        'Result' => 'Invalid truck ID'
                    ]);
                }
            } else {
                echo json_encode([
                    'Success' => false,
                    'Result' => 'Invalid request method'
                ]);
            }
            break;
            
        case 'UpdateTruck':
            if ($method === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                $truckId = $input['truckId'] ?? 0;
                
                if ($truckId > 0) {
                    // Validate required fields
                    $required = ['truckNumber', 'driverName', 'licensePlate', 'truckType'];
                    foreach ($required as $field) {
                        if (empty($input[$field])) {
                            echo json_encode([
                                'Success' => false,
                                'Result' => ucfirst($field) . ' is required'
                            ]);
                            exit;
                        }
                    }
                    
                    $result = $truckManager->updateTruck($truckId, $input);
                    echo json_encode($result);
                } else {
                    echo json_encode([
                        'Success' => false,
                        'Result' => 'Invalid truck ID'
                    ]);
                }
            } else {
                echo json_encode([
                    'Success' => false,
                    'Result' => 'Invalid request method'
                ]);
            }
            break;
            
        case 'DeleteTruck':
            if ($method === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                $truckId = $input['truckId'] ?? 0;
                
                if ($truckId > 0) {
                    $result = $truckManager->deleteTruck($truckId);
                    echo json_encode($result);
                } else {
                    echo json_encode([
                        'Success' => false,
                        'Result' => 'Invalid truck ID'
                    ]);
                }
            } else {
                echo json_encode([
                    'Success' => false,
                    'Result' => 'Invalid request method'
                ]);
            }
            break;

        // Truck Booking endpoints
        case 'GetParkingLocations':
            $result = $bookingManager->GetParkingLocations($_POST);
            echo $result;
            break;

        case 'GetCarriers':
            $result = $bookingManager->GetCarriers($_POST);
            echo $result;
            break;

        case 'GetTruckTypes':
            $result = $bookingManager->GetTruckTypes($_POST);
            echo $result;
            break;

        case 'TruckSave':
            $result = $bookingManager->TruckSave($_POST);
            echo $result;
            break;

        case 'GetTruckDetails':
            $result = $bookingManager->GetTruckDetails($_POST);
            echo $result;
            break;

        case 'GetTruckList':
            $result = $bookingManager->GetTruckList($_POST);
            echo $result;
            break;

        case 'DeleteTruckBooking':
            $result = $bookingManager->DeleteTruck($_POST);
            echo $result;
            break;

        case 'ChangeStatus':
            $result = $bookingManager->ChangeStatus($_POST);
            echo $result;
            break;

        case 'GenerateTruckListxls':
            $result = $bookingManager->GenerateTruckListxls($_POST);
            echo $result;
            break;

        default:
            echo json_encode([
                'Success' => false,
                'Result' => 'Invalid action'
            ]);
            break;
    }
} catch (Exception $e) {
    echo json_encode([
        'Success' => false,
        'Result' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
