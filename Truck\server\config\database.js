const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'truck_management',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test database connection
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        console.log('✅ Database connected successfully');
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
}

// Execute query with error handling
async function executeQuery(query, params = []) {
    try {
        const [rows] = await pool.execute(query, params);
        return {
            success: true,
            data: rows
        };
    } catch (error) {
        console.error('Database query error:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Get single record
async function getOne(query, params = []) {
    try {
        const [rows] = await pool.execute(query, params);
        return {
            success: true,
            data: rows[0] || null
        };
    } catch (error) {
        console.error('Database query error:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Insert record and return inserted ID
async function insert(query, params = []) {
    try {
        const [result] = await pool.execute(query, params);
        return {
            success: true,
            insertId: result.insertId,
            affectedRows: result.affectedRows
        };
    } catch (error) {
        console.error('Database insert error:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Update/Delete record
async function modify(query, params = []) {
    try {
        const [result] = await pool.execute(query, params);
        return {
            success: true,
            affectedRows: result.affectedRows
        };
    } catch (error) {
        console.error('Database modify error:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

module.exports = {
    pool,
    testConnection,
    executeQuery,
    getOne,
    insert,
    modify
};
