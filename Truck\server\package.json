{"name": "truck-booking-api", "version": "1.0.0", "description": "Node.js API for Truck Booking Management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "install-setup": "node install.js", "test-api": "node test-api.js", "setup": "npm run install-setup && npm run dev"}, "keywords": ["truck", "booking", "api", "nodejs", "express", "mysql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-session": "^1.17.3", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}