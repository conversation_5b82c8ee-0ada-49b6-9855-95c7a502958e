@use "../variables" as *;

.demos-dd {
  max-width: $boxedWidth !important;
}

.img-overlay {
  position: relative;
  border: 1px solid var(--mat-sys-outline);
  .overlay-content {
    display: none;
  }

  &:hover {
    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 8;
      background-color: rgba(55, 114, 255, 0.2);
      border-radius: $border-radius;
    }

    .overlay-content {
      display: flex;
      position: absolute;
      top: 0;
      z-index: 9;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
    }
  }
}

.topbar-xl {
  height: 80px;
  z-index: 1;
}

// ----------------------------------------------------------
// banner
// ----------------------------------------------------------
@media (min-width: 900px) {
  .banner-title {
    font-size: 54px !important;
    line-height: 60px !important;
  }
}

@media (max-width: 1200px) {
  .banner-section {
    padding-top: 60px;
  }
}

.banner-title {
  font-size: 36px;
  line-height: 44px;
}

.banner-subtitle {
  line-height: 25px;
}

.btn-custom {
  height: 56px !important;
  padding: 0 52px !important;
  font-size: 16px;

  &.sm {
    padding: 0 34px !important;
  }
}

@media (max-width: 767px) {
  .btn-custom {
    padding: 0 36px !important;
  }
}

.banner-slider {
  background-color: $light-primary;
  min-width: 2000px;
  height: calc(100vh - 100px);
  max-height: 790px;
  border-radius: $border-radius;
  overflow: hidden;
}

.slider-img {
  animation: slideup 35s linear infinite;
}

.slider-img2 {
  animation: slidedown 35s linear infinite;
}

@keyframes slideup {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0px, -100%, 0px);
  }
}

@keyframes slidedown {
  0% {
    transform: translate3d(0, -100%, 0);
  }

  100% {
    transform: translate3d(0px, 0, 0px);
  }
}

// sections

.spacer {
  padding: 80px 0 100px;
}

.section-title {
  font-size: 28px;
  line-height: 32px;
}

@media (min-width: 991px) {
  .section-title {
    font-size: 36px;
    line-height: 43px;
  }
}

// demo slider
.demo-slider {
  margin-top: 40px;

  .demo-slide {
    animation: slide 45s linear infinite;
  }
}

@keyframes slide {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(-100%, 0, 0);
  }
}

.lh-base {
  line-height: 1.75;
}

// call to action

.shape-card {
  background-repeat: no-repeat;
  background-position: center center;
  overflow: hidden;
}

.spacer-sm {
  padding-top: 60px;
  padding-bottom: 30px;
}

.spacer-bottom {
  padding-bottom: 60px;
}

.section-title2 {
  font-size: 30px;
  line-height: 36px;
  font-weight: 700;
}

.c2a-card {
  margin-top: -70px;
  background-repeat: no-repeat;
  background-position: center;
}

.border-white {
  border: 1px solid $white !important;
}

.border-accent {
  border: 1px solid $accent !important;
}

.border-primary {
  border: 1px solid $primary !important;
}

.hstack {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

@media (max-width: 767px) {
  .hstack {
    flex-direction: column;
  }

  .bg-size-cover {
    background-size: cover;
  }
}

.shapes-component {
  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -32px;
    width: 388px;
    height: 382px;
    z-index: -1;
    background-image: url(/assets/images/shapes/shape-2.svg);
    background-repeat: no-repeat;
  }
  &::after {
    content: "";
    position: absolute;
    top: -125px;
    right: -96px;
    width: 267px;
    height: 252px;
    z-index: -1;
    background-image: url(/assets/images/shapes/shape-1.svg);
    background-repeat: no-repeat;
  }
}
