# Sidebar Integration Guide

## Adding Truck Module to Main Navigation

To add the Truck Management module to your main application sidebar, you have two options:

## Option 1: Database Integration (Recommended)

Add the Truck module to your database so it appears in the dynamic menu system:

### 1. Add to tabs table:
```sql
INSERT INTO tabs (TabName, TabLink, Status, `Order`, AccountID) 
VALUES ('Truck Management', 'Truck', '1', 999, 1);
```

### 2. Add to left_menus table:
```sql
-- Get the TabID from the previous insert
SET @TabID = LAST_INSERT_ID();

INSERT INTO left_menus (TabID, TabName, TabLink, Status, `Order`) 
VALUES (@TabID, 'Truck Form', 'TruckForm', '1', 1);
```

### 3. Assign to profiles:
```sql
-- For Administrator profile (ProfileID = 1)
INSERT INTO modules_assignment (ProfileID, TabID, Status) 
VALUES (1, @TabID, '1');

INSERT INTO left_menus_assignment (Profile<PERSON>, MenuID, Status) 
VALUES (1, LAST_INSERT_ID(), '1');
```

## Option 2: Manual Addition (Quick Setup)

If you want to quickly add the link without database changes, add this to your sidebar.php file:

### Find the menu generation section and add:
```php
// Add this after the existing menu items, around line 150
?>
<li class="nav-item">
    <a md-button aria-label="menu" href="<?php echo HOST; ?>Truck/" target="_self">
        <i class="material-icons">local_shipping</i>
        Truck Management
    </a>
</li>
<?php
```

## Option 3: Add to Existing Module

If you want to add it as a sub-item under an existing module (like Administration):

### Add to the Administration module:
```sql
-- Get the Administration TabID
SELECT TabID FROM tabs WHERE TabName = 'Administration';

-- Add the Truck menu item (replace X with the actual TabID)
INSERT INTO left_menus (TabID, TabName, TabLink, Status, `Order`, GroupName) 
VALUES (X, 'Truck Management', 'Truck', '1', 999, 'Vehicle Management');

-- Assign to all profiles that have access to Administration
INSERT INTO left_menus_assignment (ProfileID, MenuID, Status) 
SELECT DISTINCT ProfileID, LAST_INSERT_ID(), '1' 
FROM modules_assignment 
WHERE TabID = X AND Status = '1';
```

## Verification

After adding the menu item:

1. **Clear any cache** if your application uses caching
2. **Logout and login again** to refresh the session
3. **Check the sidebar** for the new "Truck Management" link
4. **Click the link** to verify it navigates to the Truck module
5. **Test the form** to ensure everything works correctly

## Styling

The Truck module will automatically inherit the styling from your main application. The Angular Material components will match the existing design.

## Icon Options

You can use these Material Icons for the truck module:
- `local_shipping` (truck icon)
- `directions_car` (car icon)
- `commute` (commute icon)
- `airport_shuttle` (shuttle icon)

## Troubleshooting

### Link doesn't appear:
- Check if the user profile has access to the module
- Verify the database entries are correct
- Clear browser cache and refresh

### Link appears but doesn't work:
- Verify the Truck folder is uploaded correctly
- Check the index.php file exists
- Ensure the dist/ folder contains the built Angular files

### Permission issues:
- Make sure the user has appropriate permissions
- Check if the module is assigned to the user's profile
- Verify the Status field is set to '1' (active)

## Next Steps

After successful integration:
1. Test with different user profiles
2. Verify permissions work correctly
3. Train users on the new functionality
4. Monitor for any issues or feedback
