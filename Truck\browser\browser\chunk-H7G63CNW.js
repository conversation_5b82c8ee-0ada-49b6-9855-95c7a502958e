var pd=Object.defineProperty,hd=Object.defineProperties;var gd=Object.getOwnPropertyDescriptors;var hn=Object.getOwnPropertySymbols;var Js=Object.prototype.hasOwnProperty,Ks=Object.prototype.propertyIsEnumerable;var Ys=(e,t,n)=>t in e?pd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,K=(e,t)=>{for(var n in t||={})Js.call(t,n)&&Ys(e,n,t[n]);if(hn)for(var n of hn(t))Ks.call(t,n)&&Ys(e,n,t[n]);return e},X=(e,t)=>hd(e,gd(t));var zv=(e,t)=>{var n={};for(var r in e)Js.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&hn)for(var r of hn(e))t.indexOf(r)<0&&Ks.call(e,r)&&(n[r]=e[r]);return n};function uo(e,t){return Object.is(e,t)}var F=null,gn=!1,fo=1,G=Symbol("SIGNAL");function b(e){let t=F;return F=e,t}function po(){return F}var it={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Pt(e){if(gn)throw new Error("");if(F===null)return;F.consumerOnSignalRead(e);let t=F.nextProducerIndex++;if(En(F),t<F.producerNode.length&&F.producerNode[t]!==e&&At(F)){let n=F.producerNode[t];In(n,F.producerIndexOfThis[t])}F.producerNode[t]!==e&&(F.producerNode[t]=e,F.producerIndexOfThis[t]=At(F)?ea(e,F,t):0),F.producerLastReadVersion[t]=e.version}function Xs(){fo++}function ho(e){if(!(At(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===fo)){if(!e.producerMustRecompute(e)&&!vn(e)){lo(e);return}e.producerRecomputeValue(e),lo(e)}}function go(e){if(e.liveConsumerNode===void 0)return;let t=gn;gn=!0;try{for(let n of e.liveConsumerNode)n.dirty||md(n)}finally{gn=t}}function mo(){return F?.consumerAllowSignalWrites!==!1}function md(e){e.dirty=!0,go(e),e.consumerMarkedDirty?.(e)}function lo(e){e.dirty=!1,e.lastCleanEpoch=fo}function Lt(e){return e&&(e.nextProducerIndex=0),b(e)}function yn(e,t){if(b(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(At(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)In(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function vn(e){En(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(ho(n),r!==n.version))return!0}return!1}function Ft(e){if(En(e),At(e))for(let t=0;t<e.producerNode.length;t++)In(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ea(e,t,n){if(ta(e),e.liveConsumerNode.length===0&&na(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=ea(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function In(e,t){if(ta(e),e.liveConsumerNode.length===1&&na(e))for(let r=0;r<e.producerNode.length;r++)In(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];En(o),o.producerIndexOfThis[r]=t}}function At(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function En(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ta(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function na(e){return e.producerNode!==void 0}function Dn(e,t){let n=Object.create(yd);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(ho(n),Pt(n),n.value===mn)throw n.error;return n.value};return r[G]=n,r}var ao=Symbol("UNSET"),co=Symbol("COMPUTING"),mn=Symbol("ERRORED"),yd=X(K({},it),{value:ao,dirty:!0,error:null,equal:uo,kind:"computed",producerMustRecompute(e){return e.value===ao||e.value===co},producerRecomputeValue(e){if(e.value===co)throw new Error("Detected cycle in computations.");let t=e.value;e.value=co;let n=Lt(e),r,o=!1;try{r=e.computation(),b(null),o=t!==ao&&t!==mn&&r!==mn&&e.equal(t,r)}catch(i){r=mn,e.error=i}finally{yn(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function vd(){throw new Error}var ra=vd;function oa(e){ra(e)}function yo(e){ra=e}var Id=null;function vo(e,t){let n=Object.create(wn);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Pt(n),n.value);return r[G]=n,r}function jt(e,t){mo()||oa(e),e.equal(e.value,t)||(e.value=t,Ed(e))}function Io(e,t){mo()||oa(e),jt(e,t(e.value))}var wn=X(K({},it),{equal:uo,value:void 0,kind:"signal"});function Ed(e){e.version++,Xs(),go(e),Id?.()}function Eo(e){let t=b(null);try{return e()}finally{b(t)}}var Do;function Vt(){return Do}function ve(e){let t=Do;return Do=e,t}var bn=Symbol("NotFound");function E(e){return typeof e=="function"}function st(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Mn=st(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Pe(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var L=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(E(r))try{r()}catch(i){t=i instanceof Mn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{ia(i)}catch(s){t=t??[],s instanceof Mn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Mn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)ia(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Pe(n,t)}remove(t){let{_finalizers:n}=this;n&&Pe(n,t),t instanceof e&&t._removeParent(this)}};L.EMPTY=(()=>{let e=new L;return e.closed=!0,e})();var wo=L.EMPTY;function Cn(e){return e instanceof L||e&&"closed"in e&&E(e.remove)&&E(e.add)&&E(e.unsubscribe)}function ia(e){E(e)?e():e.unsubscribe()}var se={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var at={setTimeout(e,t,...n){let{delegate:r}=at;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=at;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function _n(e){at.setTimeout(()=>{let{onUnhandledError:t}=se;if(t)t(e);else throw e})}function Ht(){}var sa=bo("C",void 0,void 0);function aa(e){return bo("E",void 0,e)}function ca(e){return bo("N",e,void 0)}function bo(e,t,n){return{kind:e,value:t,error:n}}var Le=null;function ct(e){if(se.useDeprecatedSynchronousErrorHandling){let t=!Le;if(t&&(Le={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Le;if(Le=null,n)throw r}}else e()}function la(e){se.useDeprecatedSynchronousErrorHandling&&Le&&(Le.errorThrown=!0,Le.error=e)}var Fe=class extends L{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Cn(t)&&t.add(this)):this.destination=_d}static create(t,n,r){return new Ie(t,n,r)}next(t){this.isStopped?Co(ca(t),this):this._next(t)}error(t){this.isStopped?Co(aa(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Co(sa,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Md=Function.prototype.bind;function Mo(e,t){return Md.call(e,t)}var _o=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){xn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){xn(r)}else xn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){xn(n)}}},Ie=class extends Fe{constructor(t,n,r){super();let o;if(E(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&se.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Mo(t.next,i),error:t.error&&Mo(t.error,i),complete:t.complete&&Mo(t.complete,i)}):o=t}this.destination=new _o(o)}};function xn(e){se.useDeprecatedSynchronousErrorHandling?la(e):_n(e)}function Cd(e){throw e}function Co(e,t){let{onStoppedNotification:n}=se;n&&at.setTimeout(()=>n(e,t))}var _d={closed:!0,next:Ht,error:Cd,complete:Ht};var lt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function q(e){return e}function xd(...e){return xo(e)}function xo(e){return e.length===0?q:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var x=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Nd(n)?n:new Ie(n,r,o);return ct(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=ua(r),new r((o,i)=>{let s=new Ie({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[lt](){return this}pipe(...n){return xo(n)(this)}toPromise(n){return n=ua(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function ua(e){var t;return(t=e??se.Promise)!==null&&t!==void 0?t:Promise}function Td(e){return e&&E(e.next)&&E(e.error)&&E(e.complete)}function Nd(e){return e&&e instanceof Fe||Td(e)&&Cn(e)}function To(e){return E(e?.lift)}function y(e){return t=>{if(To(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function m(e,t,n,r,o){return new No(e,t,n,r,o)}var No=class extends Fe{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function So(){return y((e,t)=>{let n=null;e._refCount++;let r=m(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var ko=class extends x{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,To(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new L;let n=this.getSubject();t.add(this.source.subscribe(m(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=L.EMPTY)}return t}refCount(){return So()(this)}};var da=st(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ne=(()=>{class e extends x{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Tn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new da}next(n){ct(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){ct(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){ct(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?wo:(this.currentObservers=null,i.push(n),new L(()=>{this.currentObservers=null,Pe(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new x;return n.source=this,n}}return e.create=(t,n)=>new Tn(t,n),e})(),Tn=class extends ne{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:wo}};var Bt=class extends ne{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var $t={now(){return($t.delegate||Date).now()},delegate:void 0};var Ut=class extends ne{constructor(t=1/0,n=1/0,r=$t){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Nn=class extends L{constructor(t,n){super()}schedule(t,n=0){return this}};var qt={setInterval(e,t,...n){let{delegate:r}=qt;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=qt;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Sn=class extends Nn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return qt.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&qt.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Pe(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var ut=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};ut.now=$t.now;var kn=class extends ut{constructor(t,n=ut.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Wt=new kn(Sn),fa=Wt;var je=new x(e=>e.complete());function Rn(e){return e&&E(e.schedule)}function Ro(e){return e[e.length-1]}function On(e){return E(Ro(e))?e.pop():void 0}function de(e){return Rn(Ro(e))?e.pop():void 0}function pa(e,t){return typeof Ro(e)=="number"?e.pop():t}function ga(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function ha(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ve(e){return this instanceof Ve?(this.v=e,this):new Ve(e)}function ma(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(h){return Promise.resolve(h).then(d,f)}}function a(d,h){r[d]&&(o[d]=function(g){return new Promise(function(O,_){i.push([d,g,O,_])>1||c(d,g)})},h&&(o[d]=h(o[d])))}function c(d,h){try{l(r[d](h))}catch(g){p(i[0][3],g)}}function l(d){d.value instanceof Ve?Promise.resolve(d.value.v).then(u,f):p(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function p(d,h){d(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ya(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof ha=="function"?ha(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var An=e=>e&&typeof e.length=="number"&&typeof e!="function";function Pn(e){return E(e?.then)}function Ln(e){return E(e[lt])}function Fn(e){return Symbol.asyncIterator&&E(e?.[Symbol.asyncIterator])}function jn(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Sd(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Vn=Sd();function Hn(e){return E(e?.[Vn])}function Bn(e){return ma(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ve(n.read());if(o)return yield Ve(void 0);yield yield Ve(r)}}finally{n.releaseLock()}})}function $n(e){return E(e?.getReader)}function S(e){if(e instanceof x)return e;if(e!=null){if(Ln(e))return kd(e);if(An(e))return Rd(e);if(Pn(e))return Od(e);if(Fn(e))return va(e);if(Hn(e))return Ad(e);if($n(e))return Pd(e)}throw jn(e)}function kd(e){return new x(t=>{let n=e[lt]();if(E(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Rd(e){return new x(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Od(e){return new x(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,_n)})}function Ad(e){return new x(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function va(e){return new x(t=>{Ld(e,t).catch(n=>t.error(n))})}function Pd(e){return va(Bn(e))}function Ld(e,t){var n,r,o,i;return ga(this,void 0,void 0,function*(){try{for(n=ya(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Z(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Un(e,t=0){return y((n,r)=>{n.subscribe(m(r,o=>Z(r,e,()=>r.next(o),t),()=>Z(r,e,()=>r.complete(),t),o=>Z(r,e,()=>r.error(o),t)))})}function qn(e,t=0){return y((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ia(e,t){return S(e).pipe(qn(t),Un(t))}function Ea(e,t){return S(e).pipe(qn(t),Un(t))}function Da(e,t){return new x(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function wa(e,t){return new x(n=>{let r;return Z(n,t,()=>{r=e[Vn](),Z(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>E(r?.return)&&r.return()})}function Wn(e,t){if(!e)throw new Error("Iterable cannot be null");return new x(n=>{Z(n,t,()=>{let r=e[Symbol.asyncIterator]();Z(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function ba(e,t){return Wn(Bn(e),t)}function Ma(e,t){if(e!=null){if(Ln(e))return Ia(e,t);if(An(e))return Da(e,t);if(Pn(e))return Ea(e,t);if(Fn(e))return Wn(e,t);if(Hn(e))return wa(e,t);if($n(e))return ba(e,t)}throw jn(e)}function fe(e,t){return t?Ma(e,t):S(e)}function Fd(...e){let t=de(e);return fe(e,t)}function jd(e,t){let n=E(e)?e:()=>e,r=o=>o.error(n());return new x(t?o=>t.schedule(r,0,o):r)}function Vd(e){return!!e&&(e instanceof x||E(e.lift)&&E(e.subscribe))}var He=st(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Ca(e){return e instanceof Date&&!isNaN(e)}function Be(e,t){return y((n,r)=>{let o=0;n.subscribe(m(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Hd}=Array;function Bd(e,t){return Hd(t)?e(...t):e(t)}function zn(e){return Be(t=>Bd(e,t))}var{isArray:$d}=Array,{getPrototypeOf:Ud,prototype:qd,keys:Wd}=Object;function Gn(e){if(e.length===1){let t=e[0];if($d(t))return{args:t,keys:null};if(zd(t)){let n=Wd(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function zd(e){return e&&typeof e=="object"&&Ud(e)===qd}function Zn(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Gd(...e){let t=de(e),n=On(e),{args:r,keys:o}=Gn(e);if(r.length===0)return fe([],t);let i=new x(Zd(r,t,o?s=>Zn(o,s):q));return n?i.pipe(zn(n)):i}function Zd(e,t,n=q){return r=>{_a(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)_a(t,()=>{let l=fe(e[c],t),u=!1;l.subscribe(m(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function _a(e,t,n){e?Z(n,e,t):t()}function xa(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,p=()=>{f&&!c.length&&!l&&t.complete()},d=g=>l<r?h(g):c.push(g),h=g=>{i&&t.next(g),l++;let O=!1;S(n(g,u++)).subscribe(m(t,_=>{o?.(_),i?d(_):t.next(_)},()=>{O=!0},void 0,()=>{if(O)try{for(l--;c.length&&l<r;){let _=c.shift();s?Z(t,s,()=>h(_)):h(_)}p()}catch(_){t.error(_)}}))};return e.subscribe(m(t,d,()=>{f=!0,p()})),()=>{a?.()}}function $e(e,t,n=1/0){return E(t)?$e((r,o)=>Be((i,s)=>t(r,i,o,s))(S(e(r,o))),n):(typeof t=="number"&&(n=t),y((r,o)=>xa(r,o,e,n)))}function Qn(e=1/0){return $e(q,e)}function Ta(){return Qn(1)}function Yn(...e){return Ta()(fe(e,de(e)))}function Qd(e){return new x(t=>{S(e()).subscribe(t)})}function Yd(...e){let t=On(e),{args:n,keys:r}=Gn(e),o=new x(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let f=!1;S(n[u]).subscribe(m(i,p=>{f||(f=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!f)&&(l||i.next(r?Zn(r,a):a),i.complete())}))}});return t?o.pipe(zn(t)):o}function Na(e=0,t,n=fa){let r=-1;return t!=null&&(Rn(t)?n=t:r=t),new x(o=>{let i=Ca(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Jd(...e){let t=de(e),n=pa(e,1/0),r=e;return r.length?r.length===1?S(r[0]):Qn(n)(fe(r,t)):je}function Ue(e,t){return y((n,r)=>{let o=0;n.subscribe(m(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Sa(e){return y((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(m(n,l=>{r=!0,o=l,i||S(e(l)).subscribe(i=m(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Kd(e,t=Wt){return Sa(()=>Na(e,t))}function ka(e){return y((t,n)=>{let r=null,o=!1,i;r=t.subscribe(m(n,void 0,void 0,s=>{i=S(e(s,ka(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Ra(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(m(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Xd(e,t){return E(t)?$e(e,t,1):$e(e,1)}function ef(e,t=Wt){return y((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(m(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function zt(e){return y((t,n)=>{let r=!1;t.subscribe(m(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Oo(e){return e<=0?()=>je:y((t,n)=>{let r=0;t.subscribe(m(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function tf(e,t=q){return e=e??nf,y((n,r)=>{let o,i=!0;n.subscribe(m(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function nf(e,t){return e===t}function Jn(e=rf){return y((t,n)=>{let r=!1;t.subscribe(m(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function rf(){return new He}function of(e){return y((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function sf(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ue((o,i)=>e(o,i,r)):q,Oo(1),n?zt(t):Jn(()=>new He))}function Ao(e){return e<=0?()=>je:y((t,n)=>{let r=[];t.subscribe(m(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function af(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ue((o,i)=>e(o,i,r)):q,Ao(1),n?zt(t):Jn(()=>new He))}function cf(){return y((e,t)=>{let n,r=!1;e.subscribe(m(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function lf(e,t){return y(Ra(e,t,arguments.length>=2,!0))}function Lo(e={}){let{connector:t=()=>new ne,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,l=0,u=!1,f=!1,p=()=>{a?.unsubscribe(),a=void 0},d=()=>{p(),s=c=void 0,u=f=!1},h=()=>{let g=s;d(),g?.unsubscribe()};return y((g,O)=>{l++,!f&&!u&&p();let _=c=c??t();O.add(()=>{l--,l===0&&!f&&!u&&(a=Po(h,o))}),_.subscribe(O),!s&&l>0&&(s=new Ie({next:ue=>_.next(ue),error:ue=>{f=!0,p(),a=Po(d,n,ue),_.error(ue)},complete:()=>{u=!0,p(),a=Po(d,r),_.complete()}}),S(g).subscribe(s))})(i)}}function Po(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ie({next:()=>{r.unsubscribe(),e()}});return S(t(...n)).subscribe(r)}function uf(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,Lo({connector:()=>new Ut(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function df(e){return Ue((t,n)=>e<=n)}function ff(...e){let t=de(e);return y((n,r)=>{(t?Yn(e,n,t):Yn(e,n)).subscribe(r)})}function pf(e,t){return y((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(m(r,c=>{o?.unsubscribe();let l=0,u=i++;S(e(c,u)).subscribe(o=m(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function hf(e){return y((t,n)=>{S(e).subscribe(m(n,()=>n.complete(),Ht)),!n.closed&&t.subscribe(n)})}function gf(e,t=!1){return y((n,r)=>{let o=0;n.subscribe(m(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function mf(e,t,n){let r=E(e)||t||n?{next:e,error:t,complete:n}:e;return r?y((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(m(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):q}var wc="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",T=class extends Error{code;constructor(t,n){super(vf(t,n)),this.code=t}};function yf(e){return`NG0${Math.abs(e)}`}function vf(e,t){return`${yf(e)}${t?": "+t:""}`}var bc=Symbol("InputSignalNode#UNSET"),If=X(K({},wn),{transformFn:void 0,applyValueToInputSignal(e,t){jt(e,t)}});function Mc(e,t){let n=Object.create(If);n.value=e,n.transformFn=t?.transform;function r(){if(Pt(n),n.value===bc){let o=null;throw new T(-950,o)}return n.value}return r[G]=n,r}function ln(e){return{toString:e}.toString()}var Kn="__parameters__";function Ef(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Cc(e,t,n){return ln(()=>{let r=Ef(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let f=c.hasOwnProperty(Kn)?c[Kn]:Object.defineProperty(c,Kn,{value:[]})[Kn];for(;f.length<=u;)f.push(null);return(f[u]=f[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Oa=globalThis;function k(e){for(let t in e)if(e[t]===k)return t;throw Error("Could not find renamed property on target object.")}function Df(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Y(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Y).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Jo(e,t){return e?t?`${e} ${t}`:e:t||""}var wf=k({__forward_ref__:k});function _c(e){return e.__forward_ref__=_c,e.toString=function(){return Y(this())},e}function W(e){return xc(e)?e():e}function xc(e){return typeof e=="function"&&e.hasOwnProperty(wf)&&e.__forward_ref__===_c}function $(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function l_(e){return{providers:e.providers||[],imports:e.imports||[]}}function Fr(e){return Aa(e,Tc)||Aa(e,Nc)}function u_(e){return Fr(e)!==null}function Aa(e,t){return e.hasOwnProperty(t)?e[t]:null}function bf(e){let t=e&&(e[Tc]||e[Nc]);return t||null}function Pa(e){return e&&(e.hasOwnProperty(La)||e.hasOwnProperty(Mf))?e[La]:null}var Tc=k({\u0275prov:k}),La=k({\u0275inj:k}),Nc=k({ngInjectableDef:k}),Mf=k({ngInjectorDef:k}),R=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=$({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Sc(e){return e&&!!e.\u0275providers}var Cf=k({\u0275cmp:k}),_f=k({\u0275dir:k}),xf=k({\u0275pipe:k}),Tf=k({\u0275mod:k}),cr=k({\u0275fac:k}),Yt=k({__NG_ELEMENT_ID__:k}),Fa=k({__NG_ENV_ID__:k});function Jt(e){return typeof e=="string"?e:e==null?"":String(e)}function Nf(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Jt(e)}function kc(e,t){throw new T(-200,e)}function cs(e,t){throw new T(-201,!1)}var M=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(M||{}),Ko;function Rc(){return Ko}function Q(e){let t=Ko;return Ko=e,t}function Oc(e,t,n){let r=Fr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&M.Optional)return null;if(t!==void 0)return t;cs(e,"Injector")}var Sf={},qe=Sf,Xo="__NG_DI_FLAG__",lr=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?bn:qe,r)}},ur="ngTempTokenPath",kf="ngTokenPath",Rf=/\n/gm,Of="\u0275",ja="__source";function Af(e,t=M.Default){if(Vt()===void 0)throw new T(-203,!1);if(Vt()===null)return Oc(e,void 0,t);{let n=Vt(),r;return n instanceof lr?r=n.injector:r=n,r.get(e,t&M.Optional?null:void 0,t)}}function Ne(e,t=M.Default){return(Rc()||Af)(W(e),t)}function C(e,t=M.Default){return Ne(e,jr(t))}function jr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ei(e){let t=[];for(let n=0;n<e.length;n++){let r=W(e[n]);if(Array.isArray(r)){if(r.length===0)throw new T(900,!1);let o,i=M.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Pf(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(Ne(o,i))}else t.push(Ne(r))}return t}function Ac(e,t){return e[Xo]=t,e.prototype[Xo]=t,e}function Pf(e){return e[Xo]}function Lf(e,t,n,r){let o=e[ur];throw t[ja]&&o.unshift(t[ja]),e.message=Ff(`
`+e.message,o,n,r),e[kf]=o,e[ur]=null,e}function Ff(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Of?e.slice(2):e;let o=Y(t);if(Array.isArray(t))o=t.map(Y).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Y(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Rf,`
  `)}`}var jf=Ac(Cc("Optional"),8);var Vf=Ac(Cc("SkipSelf"),4);function ze(e,t){let n=e.hasOwnProperty(cr);return n?e[cr]:null}function Hf(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Bf(e){return e.flat(Number.POSITIVE_INFINITY)}function ls(e,t){e.forEach(n=>Array.isArray(n)?ls(n,t):t(n))}function Pc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function dr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function $f(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Uf(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Vr(e,t,n){let r=un(e,t);return r>=0?e[r|1]=n:(r=~r,Uf(e,r,t,n)),r}function Fo(e,t){let n=un(e,t);if(n>=0)return e[n|1]}function un(e,t){return qf(e,t,1)}function qf(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ge={},z=[],fr=new R(""),Lc=new R("",-1),Fc=new R(""),pr=class{get(t,n=qe){if(n===qe){let r=new Error(`NullInjectorError: No provider for ${Y(t)}!`);throw r.name="NullInjectorError",r}return n}};function jc(e,t){let n=e[Tf]||null;if(!n&&t===!0)throw new Error(`Type ${Y(e)} does not have '\u0275mod' property.`);return n}function Ze(e){return e[Cf]||null}function Wf(e){return e[_f]||null}function zf(e){return e[xf]||null}function Gf(e){return{\u0275providers:e}}function Zf(...e){return{\u0275providers:Vc(!0,e),\u0275fromNgModule:!0}}function Vc(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ls(t,s=>{let a=s;ti(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Hc(o,i),n}function Hc(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];us(o,i=>{t(i,r)})}}function ti(e,t,n,r){if(e=W(e),!e)return!1;let o=null,i=Pa(e),s=!i&&Ze(e);if(!i&&!s){let c=e.ngModule;if(i=Pa(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)ti(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{ls(i.imports,u=>{ti(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&Hc(l,t)}if(!a){let l=ze(o)||(()=>new o);t({provide:o,useFactory:l,deps:z},o),t({provide:Fc,useValue:o,multi:!0},o),t({provide:fr,useValue:()=>Ne(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;us(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function us(e,t){for(let n of e)Sc(n)&&(n=n.\u0275providers),Array.isArray(n)?us(n,t):t(n)}var Qf=k({provide:String,useValue:k});function Bc(e){return e!==null&&typeof e=="object"&&Qf in e}function Yf(e){return!!(e&&e.useExisting)}function Jf(e){return!!(e&&e.useFactory)}function yt(e){return typeof e=="function"}function Kf(e){return!!e.useClass}var $c=new R(""),nr={},Va={},jo;function Hr(){return jo===void 0&&(jo=new pr),jo}var Se=class{},Kt=class extends Se{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,ri(t,s=>this.processProvider(s)),this.records.set(Lc,dt(void 0,this)),o.has("environment")&&this.records.set(Se,dt(void 0,this));let i=this.records.get($c);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Fc,z,M.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?bn:qe,r)}destroy(){Zt(this),this._destroyed=!0;let t=b(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),b(t)}}onDestroy(t){return Zt(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Zt(this);let n=ve(this),r=Q(void 0),o;try{return t()}finally{ve(n),Q(r)}}get(t,n=qe,r=M.Default){if(Zt(this),t.hasOwnProperty(Fa))return t[Fa](this);r=jr(r);let o,i=ve(this),s=Q(void 0);try{if(!(r&M.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=rp(t)&&Fr(t);l&&this.injectableDefInScope(l)?c=dt(ni(t),nr):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&M.Self?Hr():this.parent;return n=r&M.Optional&&n===qe?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[ur]=a[ur]||[]).unshift(Y(t)),i)throw a;return Lf(a,t,"R3InjectorError",this.source)}else throw a}finally{Q(s),ve(i)}}resolveInjectorInitializers(){let t=b(null),n=ve(this),r=Q(void 0),o;try{let i=this.get(fr,z,M.Self);for(let s of i)s()}finally{ve(n),Q(r),b(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Y(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=W(t);let n=yt(t)?t:W(t&&t.provide),r=ep(t);if(!yt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=dt(void 0,nr,!0),o.factory=()=>ei(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=b(null);try{return n.value===Va?kc(Y(t)):n.value===nr&&(n.value=Va,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&np(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{b(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=W(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function ni(e){let t=Fr(e),n=t!==null?t.factory:ze(e);if(n!==null)return n;if(e instanceof R)throw new T(204,!1);if(e instanceof Function)return Xf(e);throw new T(204,!1)}function Xf(e){if(e.length>0)throw new T(204,!1);let n=bf(e);return n!==null?()=>n.factory(e):()=>new e}function ep(e){if(Bc(e))return dt(void 0,e.useValue);{let t=Uc(e);return dt(t,nr)}}function Uc(e,t,n){let r;if(yt(e)){let o=W(e);return ze(o)||ni(o)}else if(Bc(e))r=()=>W(e.useValue);else if(Jf(e))r=()=>e.useFactory(...ei(e.deps||[]));else if(Yf(e))r=(o,i)=>Ne(W(e.useExisting),i!==void 0&&i&M.Optional?M.Optional:void 0);else{let o=W(e&&(e.useClass||e.provide));if(tp(e))r=()=>new o(...ei(e.deps));else return ze(o)||ni(o)}return r}function Zt(e){if(e.destroyed)throw new T(205,!1)}function dt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function tp(e){return!!e.deps}function np(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function rp(e){return typeof e=="function"||typeof e=="object"&&e instanceof R}function ri(e,t){for(let n of e)Array.isArray(n)?ri(n,t):n&&Sc(n)?ri(n.\u0275providers,t):t(n)}function qc(e,t){let n;e instanceof Kt?(Zt(e),n=e):n=new lr(e);let r,o=ve(n),i=Q(void 0);try{return t()}finally{ve(o),Q(i)}}function Wc(){return Rc()!==void 0||Vt()!=null}function ds(e){if(!Wc())throw new T(-203,!1)}function op(e){return typeof e=="function"}var we=0,D=1,v=2,B=3,le=4,J=5,vt=6,hr=7,j=8,It=9,ke=10,P=11,Xt=12,Ha=13,_t=14,te=15,Qe=16,ft=17,Ee=18,Br=19,zc=20,xe=21,Vo=22,Ye=23,re=24,gt=25,V=26,Gc=1;var Je=7,gr=8,Et=9,H=10;function Te(e){return Array.isArray(e)&&typeof e[Gc]=="object"}function be(e){return Array.isArray(e)&&e[Gc]===!0}function fs(e){return(e.flags&4)!==0}function xt(e){return e.componentOffset>-1}function $r(e){return(e.flags&1)===1}function he(e){return!!e.template}function mr(e){return(e[v]&512)!==0}function Tt(e){return(e[v]&256)===256}var oi=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Zc(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var d_=(()=>{let e=()=>Qc;return e.ngInherit=!0,e})();function Qc(e){return e.type.prototype.ngOnChanges&&(e.setInput=sp),ip}function ip(){let e=Jc(this),t=e?.current;if(t){let n=e.previous;if(n===Ge)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function sp(e,t,n,r,o){let i=this.declaredInputs[r],s=Jc(e)||ap(e,{previous:Ge,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new oi(l&&l.currentValue,n,c===Ge),Zc(e,t,o,n)}var Yc="__ngSimpleChanges__";function Jc(e){return e[Yc]||null}function ap(e,t){return e[Yc]=t}var Ba=null;var N=function(e,t=null,n){Ba?.(e,t,n)},Kc="svg",cp="math";function ge(e){for(;Array.isArray(e);)e=e[we];return e}function Xc(e,t){return ge(t[e])}function ye(e,t){return ge(t[e.index])}function ps(e,t){return e.data[t]}function el(e,t){return e[t]}function tl(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function me(e,t){let n=t[e];return Te(n)?n:n[we]}function lp(e){return(e[v]&4)===4}function hs(e){return(e[v]&128)===128}function up(e){return be(e[B])}function Re(e,t){return t==null?null:e[t]}function nl(e){e[ft]=0}function rl(e){e[v]&1024||(e[v]|=1024,hs(e)&&Nt(e))}function dp(e,t){for(;e>0;)t=t[_t],e--;return t}function Ur(e){return!!(e[v]&9216||e[re]?.dirty)}function ii(e){e[ke].changeDetectionScheduler?.notify(8),e[v]&64&&(e[v]|=1024),Ur(e)&&Nt(e)}function Nt(e){e[ke].changeDetectionScheduler?.notify(0);let t=Ke(e);for(;t!==null&&!(t[v]&8192||(t[v]|=8192,!hs(t)));)t=Ke(t)}function ol(e,t){if(Tt(e))throw new T(911,!1);e[xe]===null&&(e[xe]=[]),e[xe].push(t)}function fp(e,t){if(e[xe]===null)return;let n=e[xe].indexOf(t);n!==-1&&e[xe].splice(n,1)}function Ke(e){let t=e[B];return be(t)?t[B]:t}function gs(e){return e[hr]??=[]}function ms(e){return e.cleanup??=[]}function pp(e,t,n,r){let o=gs(t);o.push(n),e.firstCreatePass&&ms(e).push(r,o.length-1)}var w={lFrame:dl(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var si=!1;function hp(){return w.lFrame.elementDepthCount}function gp(){w.lFrame.elementDepthCount++}function mp(){w.lFrame.elementDepthCount--}function ys(){return w.bindingsEnabled}function il(){return w.skipHydrationRootTNode!==null}function yp(e){return w.skipHydrationRootTNode===e}function vp(){w.skipHydrationRootTNode=null}function I(){return w.lFrame.lView}function A(){return w.lFrame.tView}function f_(e){return w.lFrame.contextLView=e,e[j]}function p_(e){return w.lFrame.contextLView=null,e}function U(){let e=sl();for(;e!==null&&e.type===64;)e=e.parent;return e}function sl(){return w.lFrame.currentTNode}function Ip(){let e=w.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ae(e,t){let n=w.lFrame;n.currentTNode=e,n.isParent=t}function vs(){return w.lFrame.isParent}function Is(){w.lFrame.isParent=!1}function Ep(){return w.lFrame.contextLView}function al(){return si}function yr(e){let t=si;return si=e,t}function cl(){let e=w.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Dp(){return w.lFrame.bindingIndex}function wp(e){return w.lFrame.bindingIndex=e}function ot(){return w.lFrame.bindingIndex++}function Es(e){let t=w.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function bp(){return w.lFrame.inI18n}function Mp(e,t){let n=w.lFrame;n.bindingIndex=n.bindingRootIndex=e,ai(t)}function Cp(){return w.lFrame.currentDirectiveIndex}function ai(e){w.lFrame.currentDirectiveIndex=e}function _p(e){let t=w.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Ds(){return w.lFrame.currentQueryIndex}function qr(e){w.lFrame.currentQueryIndex=e}function xp(e){let t=e[D];return t.type===2?t.declTNode:t.type===1?e[J]:null}function ll(e,t,n){if(n&M.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&M.Host);)if(o=xp(i),o===null||(i=i[_t],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=w.lFrame=ul();return r.currentTNode=t,r.lView=e,!0}function ws(e){let t=ul(),n=e[D];w.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ul(){let e=w.lFrame,t=e===null?null:e.child;return t===null?dl(e):t}function dl(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function fl(){let e=w.lFrame;return w.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var pl=fl;function bs(){let e=fl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Tp(e){return(w.lFrame.contextLView=dp(e,w.lFrame.contextLView))[j]}function Me(){return w.lFrame.selectedIndex}function Xe(e){w.lFrame.selectedIndex=e}function Wr(){let e=w.lFrame;return ps(e.tView,e.selectedIndex)}function h_(){w.lFrame.currentNamespace=Kc}function g_(){Np()}function Np(){w.lFrame.currentNamespace=null}function Sp(){return w.lFrame.currentNamespace}var hl=!0;function zr(){return hl}function Gr(e){hl=e}function kp(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Qc(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Ms(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function rr(e,t,n){gl(e,t,3,n)}function or(e,t,n,r){(e[v]&3)===n&&gl(e,t,n,r)}function Ho(e,t){let n=e[v];(n&3)===t&&(n&=16383,n+=1,e[v]=n)}function gl(e,t,n,r){let o=r!==void 0?e[ft]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[ft]+=65536),(a<i||i==-1)&&(Rp(e,n,t,c),e[ft]=(e[ft]&**********)+c+2),c++}function $a(e,t){N(4,e,t);let n=b(null);try{t.call(e)}finally{b(n),N(5,e,t)}}function Rp(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[v]>>14<e[ft]>>16&&(e[v]&3)===t&&(e[v]+=16384,$a(a,i)):$a(a,i)}var mt=-1,et=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Op(e){return(e.flags&8)!==0}function Ap(e){return(e.flags&16)!==0}function Pp(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Lp(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function ml(e){return e===3||e===4||e===6}function Lp(e){return e.charCodeAt(0)===64}function Dt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ua(e,n,o,null,t[++r]):Ua(e,n,o,null,null))}}return e}function Ua(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function yl(e){return e!==mt}function vr(e){return e&32767}function Fp(e){return e>>16}function Ir(e,t){let n=Fp(e),r=t;for(;n>0;)r=r[_t],n--;return r}var ci=!0;function Er(e){let t=ci;return ci=e,t}var jp=256,vl=jp-1,Il=5,Vp=0,pe={};function Hp(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Yt)&&(r=n[Yt]),r==null&&(r=n[Yt]=Vp++);let o=r&vl,i=1<<o;t.data[e+(o>>Il)]|=i}function Dr(e,t){let n=El(e,t);if(n!==-1)return n;let r=t[D];r.firstCreatePass&&(e.injectorIndex=t.length,Bo(r.data,e),Bo(t,null),Bo(r.blueprint,null));let o=Cs(e,t),i=e.injectorIndex;if(yl(o)){let s=vr(o),a=Ir(o,t),c=a[D].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Bo(e,t){e.push(0,0,0,0,0,0,0,0,t)}function El(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Cs(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Cl(o),r===null)return mt;if(n++,o=o[_t],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return mt}function li(e,t,n){Hp(e,t,n)}function Bp(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(ml(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Dl(e,t,n){if(n&M.Optional||e!==void 0)return e;cs(t,"NodeInjector")}function wl(e,t,n,r){if(n&M.Optional&&r===void 0&&(r=null),(n&(M.Self|M.Host))===0){let o=e[It],i=Q(void 0);try{return o?o.get(t,r,n&M.Optional):Oc(t,r,n&M.Optional)}finally{Q(i)}}return Dl(r,t,n)}function bl(e,t,n,r=M.Default,o){if(e!==null){if(t[v]&2048&&!(r&M.Self)){let s=Wp(e,t,n,r,pe);if(s!==pe)return s}let i=Ml(e,t,n,r,pe);if(i!==pe)return i}return wl(t,n,r,o)}function Ml(e,t,n,r,o){let i=Up(n);if(typeof i=="function"){if(!ll(t,e,r))return r&M.Host?Dl(o,n,r):wl(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&M.Optional))cs(n);else return s}finally{pl()}}else if(typeof i=="number"){let s=null,a=El(e,t),c=mt,l=r&M.Host?t[te][J]:null;for((a===-1||r&M.SkipSelf)&&(c=a===-1?Cs(e,t):t[a+8],c===mt||!Wa(r,!1)?a=-1:(s=t[D],a=vr(c),t=Ir(c,t)));a!==-1;){let u=t[D];if(qa(i,a,u.data)){let f=$p(a,t,n,s,r,l);if(f!==pe)return f}c=t[a+8],c!==mt&&Wa(r,t[D].data[a+8]===l)&&qa(i,a,t)?(s=u,a=vr(c),t=Ir(c,t)):a=-1}}return o}function $p(e,t,n,r,o,i){let s=t[D],a=s.data[e+8],c=r==null?xt(a)&&ci:r!=s&&(a.type&3)!==0,l=o&M.Host&&i===a,u=ir(a,s,n,c,l);return u!==null?en(t,s,u,a,o):pe}function ir(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,p=o?a+u:l;for(let d=f;d<p;d++){let h=s[d];if(d<c&&n===h||d>=c&&h.type===n)return d}if(o){let d=s[c];if(d&&he(d)&&d.type===n)return c}return null}function en(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof et){let a=i;a.resolving&&kc(Nf(s[n]));let c=Er(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?Q(a.injectImpl):null,f=ll(e,r,M.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&kp(n,s[n],t)}finally{u!==null&&Q(u),Er(c),a.resolving=!1,pl()}}return i}function Up(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Yt)?e[Yt]:void 0;return typeof t=="number"?t>=0?t&vl:qp:t}function qa(e,t,n){let r=1<<e;return!!(n[t+(e>>Il)]&r)}function Wa(e,t){return!(e&M.Self)&&!(e&M.Host&&t)}var We=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return bl(this._tNode,this._lView,t,jr(r),n)}};function qp(){return new We(U(),I())}function m_(e){return ln(()=>{let t=e.prototype.constructor,n=t[cr]||ui(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[cr]||ui(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ui(e){return xc(e)?()=>{let t=ui(W(e));return t&&t()}:ze(e)}function Wp(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[v]&2048&&!mr(s);){let a=Ml(i,s,n,r|M.Self,pe);if(a!==pe)return a;let c=i.parent;if(!c){let l=s[zc];if(l){let u=l.get(n,pe,r);if(u!==pe)return u}c=Cl(s),s=s[_t]}i=c}return o}function Cl(e){let t=e[D],n=t.type;return n===2?t.declTNode:n===1?e[J]:null}function zp(e){return Bp(U(),e)}function za(e,t=null,n=null,r){let o=_l(e,t,n,r);return o.resolveInjectorInitializers(),o}function _l(e,t=null,n=null,r,o=new Set){let i=[n||z,Zf(e)];return r=r||(typeof e=="object"?void 0:Y(e)),new Kt(i,t||Hr(),r||null,o)}var Oe=class e{static THROW_IF_NOT_FOUND=qe;static NULL=new pr;static create(t,n){if(Array.isArray(t))return za({name:""},n,t,"");{let r=t.name??"";return za({name:r},t.parent,t.providers,r)}}static \u0275prov=$({token:e,providedIn:"any",factory:()=>Ne(Lc)});static __NG_ELEMENT_ID__=-1};var Ga=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>zp(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},Gp=new R("");Gp.__NG_ELEMENT_ID__=e=>{let t=U();if(t===null)throw new T(204,!1);if(t.type&2)return t.value;if(e&M.Optional)return null;throw new T(204,!1)};var xl=!1,Zr=(()=>{class e{static __NG_ELEMENT_ID__=Zp;static __NG_ENV_ID__=n=>n}return e})(),wr=class extends Zr{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Tt(n)?(t(),()=>{}):(ol(n,t),()=>fp(n,t))}};function Zp(){return new wr(I())}var tt=class{},_s=new R("",{providedIn:"root",factory:()=>!1});var Tl=new R(""),Nl=new R(""),Qr=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Bt(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=$({token:e,providedIn:"root",factory:()=>new e})}return e})();var di=class extends ne{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Wc()&&(this.destroyRef=C(Zr,{optional:!0})??void 0,this.pendingTasks=C(Qr,{optional:!0})??void 0)}emit(t){let n=b(null);try{super.next(t)}finally{b(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof L&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},_e=di;function tn(...e){}function Sl(e){let t,n;function r(){e=tn;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Za(e){return queueMicrotask(()=>e()),()=>{e=tn}}var xs="isAngularZone",br=xs+"_ID",Qp=0,ee=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new _e(!1);onMicrotaskEmpty=new _e(!1);onStable=new _e(!1);onError=new _e(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=xl}=t;if(typeof Zone>"u")throw new T(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Kp(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(xs)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new T(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new T(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Yp,tn,tn);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Yp={};function Ts(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Jp(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Sl(()=>{e.callbackScheduled=!1,fi(e),e.isCheckStableRunning=!0,Ts(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),fi(e)}function Kp(e){let t=()=>{Jp(e)},n=Qp++;e._inner=e._inner.fork({name:"angular",properties:{[xs]:!0,[br]:n,[br+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Xp(c))return r.invokeTask(i,s,a,c);try{return Qa(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Ya(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Qa(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!eh(c)&&t(),Ya(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,fi(e),Ts(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function fi(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Qa(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Ya(e){e._nesting--,Ts(e)}var pi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new _e;onMicrotaskEmpty=new _e;onStable=new _e;onError=new _e;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Xp(e){return kl(e,"__ignore_ng_zone__")}function eh(e){return kl(e,"__scheduler_tick__")}function kl(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var nt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},th=new R("",{providedIn:"root",factory:()=>{let e=C(ee),t=C(nt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Ja(e,t){return Mc(e,t)}function nh(e){return Mc(bc,e)}var y_=(Ja.required=nh,Ja);function rh(){return St(U(),I())}function St(e,t){return new Yr(ye(e,t))}var Yr=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=rh}return e})();function Rl(e){return e instanceof Yr?e.nativeElement:e}function oh(e){return typeof e=="function"&&e[G]!==void 0}function ih(e,t){let n=vo(e,t?.equal),r=n[G];return n.set=o=>jt(r,o),n.update=o=>Io(r,o),n.asReadonly=sh.bind(n),n}function sh(){let e=this[G];if(e.readonlyFn===void 0){let t=()=>this();t[G]=e,e.readonlyFn=t}return e.readonlyFn}function Ol(e){return oh(e)&&typeof e.set=="function"}function ah(){return this._results[Symbol.iterator]()}var hi=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new ne}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Bf(t);(this._changesDetected=!Hf(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=ah};function Al(e){return(e.flags&128)===128}var Pl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Pl||{}),Ll=new Map,ch=0;function lh(){return ch++}function uh(e){Ll.set(e[Br],e)}function gi(e){Ll.delete(e[Br])}var Ka="__ngContext__";function kt(e,t){Te(t)?(e[Ka]=t[Br],uh(t)):e[Ka]=t}function Fl(e){return Vl(e[Xt])}function jl(e){return Vl(e[le])}function Vl(e){for(;e!==null&&!be(e);)e=e[le];return e}var mi;function v_(e){mi=e}function dh(){if(mi!==void 0)return mi;if(typeof document<"u")return document;throw new T(210,!1)}var I_=new R("",{providedIn:"root",factory:()=>fh}),fh="ng",ph=new R(""),E_=new R("",{providedIn:"platform",factory:()=>"unknown"});var D_=new R(""),w_=new R("",{providedIn:"root",factory:()=>dh().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var hh="h",gh="b";var Hl=!1,mh=new R("",{providedIn:"root",factory:()=>Hl});var Ns=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ns||{}),Jr=new R(""),Xa=new Set;function Rt(e){Xa.has(e)||(Xa.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Ss=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=yh}return e})();function yh(){return new Ss(I(),U())}var pt=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(pt||{}),Bl=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=$({token:e,providedIn:"root",factory:()=>new e})}return e})(),vh=[pt.EarlyRead,pt.Write,pt.MixedReadWrite,pt.Read],Ih=(()=>{class e{ngZone=C(ee);scheduler=C(tt);errorHandler=C(nt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){C(Jr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&N(16),this.executing=!0;for(let r of vh)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&N(17)}register(n){let{view:r}=n;r!==void 0?((r[gt]??=[]).push(n),Nt(r),r[v]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Ns.AFTER_NEXT_RENDER,n):n()}static \u0275prov=$({token:e,providedIn:"root",factory:()=>new e})}return e})(),yi=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[gt];t&&(this.view[gt]=t.filter(n=>n!==this))}};function Eh(e,t){!t?.injector&&ds(Eh);let n=t?.injector??C(Oe);return Rt("NgAfterRender"),$l(e,n,t,!1)}function Dh(e,t){!t?.injector&&ds(Dh);let n=t?.injector??C(Oe);return Rt("NgAfterNextRender"),$l(e,n,t,!0)}function wh(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function $l(e,t,n,r){let o=t.get(Bl);o.impl??=t.get(Ih);let i=t.get(Jr,null,{optional:!0}),s=n?.phase??pt.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Zr):null,c=t.get(Ss,null,{optional:!0}),l=new yi(o.impl,wh(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var bh=(e,t,n,r)=>{};function Mh(e,t,n,r){bh(e,t,n,r)}var Ch=()=>null;function Ul(e,t,n=!1){return Ch(e,t,n)}function ql(e,t){let n=e.contentQueries;if(n!==null){let r=b(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];qr(i),a.contentQueries(2,t[s],s)}}}finally{b(r)}}}function vi(e,t,n){qr(0);let r=b(null);try{t(e,n)}finally{b(r)}}function ks(e,t,n){if(fs(t)){let r=b(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{b(r)}}}var nn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(nn||{}),Xn;function _h(){if(Xn===void 0&&(Xn=null,Oa.trustedTypes))try{Xn=Oa.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Xn}function Kr(e){return _h()?.createHTML(e)||e}var De=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${wc})`}},Ii=class extends De{getTypeName(){return"HTML"}},Ei=class extends De{getTypeName(){return"Style"}},Di=class extends De{getTypeName(){return"Script"}},wi=class extends De{getTypeName(){return"URL"}},bi=class extends De{getTypeName(){return"ResourceURL"}};function Wl(e){return e instanceof De?e.changingThisBreaksApplicationSecurity:e}function b_(e,t){let n=xh(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${wc})`)}return n===t}function xh(e){return e instanceof De&&e.getTypeName()||null}function M_(e){return new Ii(e)}function C_(e){return new Ei(e)}function __(e){return new Di(e)}function x_(e){return new wi(e)}function T_(e){return new bi(e)}function Th(e){let t=new Ci(e);return Nh()?new Mi(t):t}var Mi=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Kr(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},Ci=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Kr(t),n}};function Nh(){try{return!!new window.DOMParser().parseFromString(Kr(""),"text/html")}catch{return!1}}var Sh=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function kh(e){return e=String(e),e.match(Sh)?e:"unsafe:"+e}function Ce(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function dn(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var zl=Ce("area,br,col,hr,img,wbr"),Gl=Ce("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Zl=Ce("rp,rt"),Rh=dn(Zl,Gl),Oh=dn(Gl,Ce("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Ah=dn(Zl,Ce("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),ec=dn(zl,Oh,Ah,Rh),Ql=Ce("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Ph=Ce("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Lh=Ce("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Fh=dn(Ql,Ph,Lh),jh=Ce("script,style,template"),_i=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Bh(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Hh(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=tc(t).toLowerCase();if(!ec.hasOwnProperty(n))return this.sanitizedSomething=!0,!jh.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Fh.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Ql[a]&&(c=kh(c)),this.buf.push(" ",s,'="',nc(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=tc(t).toLowerCase();ec.hasOwnProperty(n)&&!zl.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(nc(t))}};function Vh(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Hh(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Yl(t);return t}function Bh(e){let t=e.firstChild;if(t&&Vh(e,t))throw Yl(t);return t}function tc(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Yl(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var $h=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Uh=/([^\#-~ |!])/g;function nc(e){return e.replace(/&/g,"&amp;").replace($h,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Uh,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var er;function N_(e,t){let n=null;try{er=er||Th(e);let r=t?String(t):"";n=er.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=er.getInertBodyElement(r)}while(r!==i);let a=new _i().sanitizeChildren(rc(n)||n);return Kr(a)}finally{if(n){let r=rc(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function rc(e){return"content"in e&&qh(e)?e.content:null}function qh(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Wh=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Wh||{});var zh=/^>|^->|<!--|-->|--!>|<!-$/g,Gh=/(<|>)/g,Zh="\u200B$1\u200B";function Qh(e){return e.replace(zh,t=>t.replace(Gh,Zh))}function Jl(e){return e instanceof Function?e():e}function Yh(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Kl="ng-template";function Jh(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Yh(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Rs(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Rs(e){return e.type===4&&e.value!==Kl}function Kh(e,t,n){let r=e.type===4&&!n?Kl:e.value;return t===r}function Xh(e,t,n){let r=4,o=e.attrs,i=o!==null?ng(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!ae(r)&&!ae(c))return!1;if(s&&ae(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Kh(e,c,n)||c===""&&t.length===1){if(ae(r))return!1;s=!0}}else if(r&8){if(o===null||!Jh(e,o,c,n)){if(ae(r))return!1;s=!0}}else{let l=t[++a],u=eg(c,o,Rs(e),n);if(u===-1){if(ae(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(ae(r))return!1;s=!0}}}}return ae(r)||s}function ae(e){return(e&1)===0}function eg(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return rg(t,e)}function Xl(e,t,n=!1){for(let r=0;r<t.length;r++)if(Xh(e,t[r],n))return!0;return!1}function tg(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function ng(e){for(let t=0;t<e.length;t++){let n=e[t];if(ml(n))return t}return e.length}function rg(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function og(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function oc(e,t){return e?":not("+t.trim()+")":t}function ig(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ae(s)&&(t+=oc(i,o),o=""),r=s,i=i||!ae(r);n++}return o!==""&&(t+=oc(i,o)),t}function sg(e){return e.map(ig).join(",")}function ag(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ae(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var ie={};function cg(e,t){return e.createText(t)}function lg(e,t,n){e.setValue(t,n)}function ug(e,t){return e.createComment(Qh(t))}function eu(e,t,n){return e.createElement(t,n)}function Mr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function tu(e,t,n){e.appendChild(t,n)}function ic(e,t,n,r,o){r!==null?Mr(e,t,n,r,o):tu(e,t,n)}function dg(e,t,n){e.removeChild(null,t,n)}function fg(e,t,n){e.setAttribute(t,"style",n)}function pg(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function nu(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Pp(e,t,r),o!==null&&pg(e,t,o),i!==null&&fg(e,t,i)}function Os(e,t,n,r,o,i,s,a,c,l,u){let f=V+r,p=f+o,d=hg(f,p),h=typeof l=="function"?l():l;return d[D]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function hg(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:ie);return n}function gg(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Os(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function As(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[we]=o,f[v]=r|4|128|8|64|1024,(l!==null||e&&e[v]&2048)&&(f[v]|=2048),nl(f),f[B]=f[_t]=e,f[j]=n,f[ke]=s||e&&e[ke],f[P]=a||e&&e[P],f[It]=c||e&&e[It]||null,f[J]=i,f[Br]=lh(),f[vt]=u,f[zc]=l,f[te]=t.type==2?e[te]:f,f}function mg(e,t,n){let r=ye(t,e),o=gg(n),i=e[ke].rendererFactory,s=Ps(e,As(e,o,null,ru(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function ru(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function ou(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ps(e,t){return e[Xt]?e[Ha][le]=t:e[Xt]=t,e[Ha]=t,t}function S_(e=1){iu(A(),I(),Me()+e,!1)}function iu(e,t,n,r){if(!r)if((t[v]&3)===3){let i=e.preOrderCheckHooks;i!==null&&rr(t,i,n)}else{let i=e.preOrderHooks;i!==null&&or(t,i,0,n)}Xe(n)}var Xr=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Xr||{});function xi(e,t,n,r){let o=b(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Xr.SignalBased)!==0&&(c=t[i][G]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Zc(t,c,i,r)}finally{b(o)}}function su(e,t,n,r,o){let i=Me(),s=r&2;try{Xe(-1),s&&t.length>V&&iu(e,t,V,!1),N(s?2:0,o),n(r,o)}finally{Xe(i),N(s?3:1,o)}}function eo(e,t,n){wg(e,t,n),(n.flags&64)===64&&bg(e,t,n)}function Ls(e,t,n=ye){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function yg(e,t,n,r){let i=r.get(mh,Hl)||n===nn.ShadowDom,s=e.selectRootElement(t,i);return vg(s),s}function vg(e){Ig(e)}var Ig=()=>null;function Eg(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Fs(e,t,n,r,o,i,s,a){if(!a&&Vs(t,e,n,r,o)){xt(t)&&Dg(n,t.index);return}if(t.type&3){let c=ye(t,n);r=Eg(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function Dg(e,t){let n=me(t,e);n[v]&16||(n[v]|=64)}function wg(e,t,n){let r=n.directiveStart,o=n.directiveEnd;xt(n)&&mg(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Dr(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=en(t,e,s,n);if(kt(c,t),i!==null&&xg(t,s-r,c,a,n,i),he(a)){let l=me(n.index,t);l[j]=en(t,e,s,n)}}}function bg(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Cp();try{Xe(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];ai(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Mg(c,l)}}finally{Xe(-1),ai(s)}}function Mg(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function js(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Xl(t,i.selectors,!1)&&(r??=[],he(i)?r.unshift(i):r.push(i))}return r}function Cg(e,t,n,r,o,i){let s=ye(e,t);_g(t[P],s,i,e.value,n,r,o)}function _g(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Jt(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function xg(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];xi(r,n,c,l)}}function Tg(e,t){let n=e[It],r=n?n.get(nt,null):null;r&&r.handleError(t)}function Vs(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];xi(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];xi(u,l,r,o),a=!0}return a}function Ng(e,t){let n=me(t,e),r=n[D];Sg(r,n);let o=n[we];o!==null&&n[vt]===null&&(n[vt]=Ul(o,n[It])),N(18),Hs(r,n,n[j]),N(19,n[j])}function Sg(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Hs(e,t,n){ws(t);try{let r=e.viewQuery;r!==null&&vi(1,r,n);let o=e.template;o!==null&&su(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ee]?.finishViewCreation(e),e.staticContentQueries&&ql(e,t),e.staticViewQueries&&vi(2,e.viewQuery,n);let i=e.components;i!==null&&kg(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[v]&=-5,bs()}}function kg(e,t){for(let n=0;n<t.length;n++)Ng(e,t[n])}function fn(e,t,n,r){let o=b(null);try{let i=t.tView,a=e[v]&4096?4096:16,c=As(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Qe]=l;let u=e[Ee];return u!==null&&(c[Ee]=u.createEmbeddedView(i)),Hs(i,c,n),c}finally{b(o)}}function wt(e,t){return!t||t.firstChild===null||Al(e)}var Rg;function Bs(e,t){return Rg(e,t)}var Ti=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Ti||{});function $s(e){return(e.flags&32)===32}function ht(e,t,n,r,o){if(r!=null){let i,s=!1;be(r)?i=r:Te(r)&&(s=!0,r=r[we]);let a=ge(r);e===0&&n!==null?o==null?tu(t,n,a):Mr(t,n,a,o||null,!0):e===1&&n!==null?Mr(t,n,a,o||null,!0):e===2?dg(t,a,s):e===3&&t.destroyNode(a),i!=null&&$g(t,e,i,n,o)}}function Og(e,t){au(e,t),t[we]=null,t[J]=null}function Ag(e,t,n,r,o,i){r[we]=o,r[J]=t,ro(e,r,n,1,o,i)}function au(e,t){t[ke].changeDetectionScheduler?.notify(9),ro(e,t,t[P],2,null,null)}function Pg(e){let t=e[Xt];if(!t)return $o(e[D],e);for(;t;){let n=null;if(Te(t))n=t[Xt];else{let r=t[H];r&&(n=r)}if(!n){for(;t&&!t[le]&&t!==e;)Te(t)&&$o(t[D],t),t=t[B];t===null&&(t=e),Te(t)&&$o(t[D],t),n=t&&t[le]}t=n}}function Us(e,t){let n=e[Et],r=n.indexOf(t);n.splice(r,1)}function to(e,t){if(Tt(t))return;let n=t[P];n.destroyNode&&ro(e,t,n,3,null,null),Pg(t)}function $o(e,t){if(Tt(t))return;let n=b(null);try{t[v]&=-129,t[v]|=256,t[re]&&Ft(t[re]),Fg(e,t),Lg(e,t),t[D].type===1&&t[P].destroy();let r=t[Qe];if(r!==null&&be(t[B])){r!==t[B]&&Us(r,t);let o=t[Ee];o!==null&&o.detachView(e)}gi(t)}finally{b(n)}}function Lg(e,t){let n=e.cleanup,r=t[hr];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[hr]=null);let o=t[xe];if(o!==null){t[xe]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Ye];if(i!==null){t[Ye]=null;for(let s of i)s.destroy()}}function Fg(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof et)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];N(4,a,c);try{c.call(a)}finally{N(5,a,c)}}else{N(4,o,i);try{i.call(o)}finally{N(5,o,i)}}}}}function cu(e,t,n){return jg(e,t.parent,n)}function jg(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[we];if(xt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===nn.None||o===nn.Emulated)return null}return ye(r,n)}function lu(e,t,n){return Hg(e,t,n)}function Vg(e,t,n){return e.type&40?ye(e,n):null}var Hg=Vg,sc;function no(e,t,n,r){let o=cu(e,r,t),i=t[P],s=r.parent||t[J],a=lu(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)ic(i,o,n[c],a,!1);else ic(i,o,n,a,!1);sc!==void 0&&sc(i,r,t,n,o)}function Qt(e,t){if(t!==null){let n=t.type;if(n&3)return ye(t,e);if(n&4)return Ni(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Qt(e,r);{let o=e[t.index];return be(o)?Ni(-1,o):ge(o)}}else{if(n&128)return Qt(e,t.next);if(n&32)return Bs(t,e)()||ge(e[t.index]);{let r=uu(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Ke(e[te]);return Qt(o,r)}else return Qt(e,t.next)}}}return null}function uu(e,t){if(t!==null){let r=e[te][J],o=t.projection;return r.projection[o]}return null}function Ni(e,t){let n=H+e+1;if(n<t.length){let r=t[n],o=r[D].firstChild;if(o!==null)return Qt(r,o)}return t[Je]}function qs(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&kt(ge(a),r),n.flags|=2),!$s(n))if(c&8)qs(e,t,n.child,r,o,i,!1),ht(t,e,o,a,i);else if(c&32){let l=Bs(n,r),u;for(;u=l();)ht(t,e,o,u,i);ht(t,e,o,a,i)}else c&16?du(e,t,r,n,o,i):ht(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ro(e,t,n,r,o,i){qs(n,r,e.firstChild,t,o,i,!1)}function Bg(e,t,n){let r=t[P],o=cu(e,n,t),i=n.parent||t[J],s=lu(i,n,t);du(r,0,t,n,o,s)}function du(e,t,n,r,o,i){let s=n[te],c=s[J].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];ht(t,e,o,u,i)}else{let l=c,u=s[B];Al(r)&&(l.flags|=128),qs(e,t,l,u,o,i,!0)}}function $g(e,t,n,r,o){let i=n[Je],s=ge(n);i!==s&&ht(t,e,r,i,o);for(let a=H;a<n.length;a++){let c=n[a];ro(c[D],c,e,t,r,i)}}function Ug(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Ti.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Ti.Important),e.setStyle(n,r,o,i))}}function Cr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ge(i)),be(i)&&qg(i,r);let s=n.type;if(s&8)Cr(e,t,n.child,r);else if(s&32){let a=Bs(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=uu(t,n);if(Array.isArray(a))r.push(...a);else{let c=Ke(t[te]);Cr(c[D],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function qg(e,t){for(let n=H;n<e.length;n++){let r=e[n],o=r[D].firstChild;o!==null&&Cr(r[D],r,o,t)}e[Je]!==e[we]&&t.push(e[Je])}function fu(e){if(e[gt]!==null){for(let t of e[gt])t.impl.addSequence(t);e[gt].length=0}}var pu=[];function Wg(e){return e[re]??zg(e)}function zg(e){let t=pu.pop()??Object.create(Zg);return t.lView=e,t}function Gg(e){e.lView[re]!==e&&(e.lView=null,pu.push(e))}var Zg=X(K({},it),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Nt(e.lView)},consumerOnSignalRead(){this.lView[re]=this}});function Qg(e){let t=e[re]??Object.create(Yg);return t.lView=e,t}var Yg=X(K({},it),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Ke(e.lView);for(;t&&!hu(t[D]);)t=Ke(t);t&&rl(t)},consumerOnSignalRead(){this.lView[re]=this}});function hu(e){return e.type!==2}function gu(e){if(e[Ye]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Ye])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[v]&8192)}}var Jg=100;function mu(e,t=!0,n=0){let o=e[ke].rendererFactory,i=!1;i||o.begin?.();try{Kg(e,n)}catch(s){throw t&&Tg(e,s),s}finally{i||o.end?.()}}function Kg(e,t){let n=al();try{yr(!0),Si(e,t);let r=0;for(;Ur(e);){if(r===Jg)throw new T(103,!1);r++,Si(e,1)}}finally{yr(n)}}function Xg(e,t,n,r){if(Tt(t))return;let o=t[v],i=!1,s=!1;ws(t);let a=!0,c=null,l=null;i||(hu(e)?(l=Wg(t),c=Lt(l)):po()===null?(a=!1,l=Qg(t),c=Lt(l)):t[re]&&(Ft(t[re]),t[re]=null));try{nl(t),wp(e.bindingStartIndex),n!==null&&su(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&rr(t,d,null)}else{let d=e.preOrderHooks;d!==null&&or(t,d,0,null),Ho(t,0)}if(s||em(t),gu(t),yu(t,0),e.contentQueries!==null&&ql(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&rr(t,d)}else{let d=e.contentHooks;d!==null&&or(t,d,1),Ho(t,1)}nm(e,t);let f=e.components;f!==null&&Iu(t,f,0);let p=e.viewQuery;if(p!==null&&vi(2,p,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&rr(t,d)}else{let d=e.viewHooks;d!==null&&or(t,d,2),Ho(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Vo]){for(let d of t[Vo])d();t[Vo]=null}i||(fu(t),t[v]&=-73)}catch(u){throw i||Nt(t),u}finally{l!==null&&(yn(l,c),a&&Gg(l)),bs()}}function yu(e,t){for(let n=Fl(e);n!==null;n=jl(n))for(let r=H;r<n.length;r++){let o=n[r];vu(o,t)}}function em(e){for(let t=Fl(e);t!==null;t=jl(t)){if(!(t[v]&2))continue;let n=t[Et];for(let r=0;r<n.length;r++){let o=n[r];rl(o)}}}function tm(e,t,n){N(18);let r=me(t,e);vu(r,n),N(19,r[j])}function vu(e,t){hs(e)&&Si(e,t)}function Si(e,t){let r=e[D],o=e[v],i=e[re],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&vn(i)),s||=!1,i&&(i.dirty=!1),e[v]&=-9217,s)Xg(r,e,r.template,e[j]);else if(o&8192){gu(e),yu(e,1);let a=r.components;a!==null&&Iu(e,a,1),fu(e)}}function Iu(e,t,n){for(let r=0;r<t.length;r++)tm(e,t[r],n)}function nm(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Xe(~o);else{let i=o,s=n[++r],a=n[++r];Mp(s,i);let c=t[i];N(24,c),a(2,c),N(25,c)}}}finally{Xe(-1)}}function Ws(e,t){let n=al()?64:1088;for(e[ke].changeDetectionScheduler?.notify(t);e;){e[v]|=n;let r=Ke(e);if(mr(e)&&!r)return e;e=r}return null}function Eu(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Du(e,t){let n=H+t;if(n<e.length)return e[n]}function pn(e,t,n,r=!0){let o=t[D];if(rm(o,t,e,n),r){let s=Ni(n,e),a=t[P],c=a.parentNode(e[Je]);c!==null&&Ag(o,e[J],a,t,c,s)}let i=t[vt];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function wu(e,t){let n=rn(e,t);return n!==void 0&&to(n[D],n),n}function rn(e,t){if(e.length<=H)return;let n=H+t,r=e[n];if(r){let o=r[Qe];o!==null&&o!==e&&Us(o,r),t>0&&(e[n-1][le]=r[le]);let i=dr(e,H+t);Og(r[D],r);let s=i[Ee];s!==null&&s.detachView(i[D]),r[B]=null,r[le]=null,r[v]&=-129}return r}function rm(e,t,n,r){let o=H+r,i=n.length;r>0&&(n[o-1][le]=t),r<i-H?(t[le]=n[o],Pc(n,H+r,t)):(n.push(t),t[le]=null),t[B]=n;let s=t[Qe];s!==null&&n!==s&&bu(s,t);let a=t[Ee];a!==null&&a.insertView(e),ii(t),t[v]|=128}function bu(e,t){let n=e[Et],r=t[B];if(Te(r))e[v]|=2;else{let o=r[B][te];t[te]!==o&&(e[v]|=2)}n===null?e[Et]=[t]:n.push(t)}var on=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[D];return Cr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[j]}set context(t){this._lView[j]=t}get destroyed(){return Tt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[B];if(be(t)){let n=t[gr],r=n?n.indexOf(this):-1;r>-1&&(rn(t,r),dr(n,r))}this._attachedToViewContainer=!1}to(this._lView[D],this._lView)}onDestroy(t){ol(this._lView,t)}markForCheck(){Ws(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[v]&=-129}reattach(){ii(this._lView),this._lView[v]|=128}detectChanges(){this._lView[v]|=1024,mu(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new T(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=mr(this._lView),n=this._lView[Qe];n!==null&&!t&&Us(n,this._lView),au(this._lView[D],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new T(902,!1);this._appRef=t;let n=mr(this._lView),r=this._lView[Qe];r!==null&&!n&&bu(r,this._lView),ii(this._lView)}};var _r=(()=>{class e{static __NG_ELEMENT_ID__=sm}return e})(),om=_r,im=class extends om{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=fn(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new on(o)}};function sm(){return oo(U(),I())}function oo(e,t){return e.type&4?new im(t,e,St(e,t)):null}function Ot(e,t,n,r,o){let i=e.data[t];if(i===null)i=am(e,t,n,r,o),bp()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Ip();i.injectorIndex=s===null?-1:s.injectorIndex}return Ae(i,!0),i}function am(e,t,n,r,o){let i=sl(),s=vs(),a=s?i:i&&i.parent,c=e.data[t]=lm(e,a,n,t,r,o);return cm(e,c,i,s),c}function cm(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function lm(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return il()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var O_=new RegExp(`^(\\d+)*(${gh}|${hh})*(.*)`);var um=()=>null;function bt(e,t){return um(e,t)}var dm=class{},Mu=class{},ki=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Y(t)}.`)}},io=class{static NULL=new ki},xr=class{},F_=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>fm()}return e})();function fm(){let e=I(),t=U(),n=me(t.index,e);return(Te(n)?n:e)[P]}var pm=(()=>{class e{static \u0275prov=$({token:e,providedIn:"root",factory:()=>null})}return e})();var Uo={},Ri=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=jr(r);let o=this.injector.get(t,Uo,r);return o!==Uo||n===Uo?o:this.parentInjector.get(t,n,r)}};function Oi(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Jo(o,a);else if(i==2){let c=a,l=t[++s];r=Jo(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function so(e,t=M.Default){let n=I();if(n===null)return Ne(e,t);let r=U();return bl(r,n,W(e),t)}function j_(){let e="invalid";throw new Error(e)}function zs(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=gm(s);u===null?a=s:[a,c,l]=u,vm(e,t,n,a,i,c,l)}i!==null&&r!==null&&hm(n,r,i)}function hm(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new T(-301,!1);r.push(t[o],i)}}function gm(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&he(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,mm(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function mm(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function ym(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function vm(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let d=r[p];!c&&he(d)&&(c=!0,ym(e,n,p)),li(Dr(n,t),e,d.type)}Mm(n,e.data.length,a);for(let p=0;p<a;p++){let d=r[p];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=ou(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let d=r[p];if(n.mergedAttrs=Dt(n.mergedAttrs,d.hostAttrs),Em(e,n,t,f,d),bm(f,d,o),s!==null&&s.has(d)){let[g,O]=s.get(d);n.directiveToIndex.set(d.type,[f,g+n.directiveStart,O+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let h=d.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}Im(e,n,i)}function Im(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))ac(0,t,o,r),ac(1,t,o,r),lc(t,r,!1);else{let i=n.get(o);cc(0,t,i,r),cc(1,t,i,r),lc(t,r,!0)}}}function ac(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Cu(t,i)}}function cc(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Cu(t,s)}}function Cu(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function lc(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Rs(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Em(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=ze(o.type,!0)),s=new et(i,he(o),so);e.blueprint[r]=s,n[r]=s,Dm(e,t,r,ou(e,n,o.hostVars,ie),o)}function Dm(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;wm(s)!=a&&s.push(a),s.push(n,r,i)}}function wm(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function bm(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;he(t)&&(n[""]=e)}}function Mm(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function _u(e,t,n,r,o,i,s,a){let c=t.consts,l=Re(c,s),u=Ot(t,e,2,r,l);return i&&zs(t,n,u,Re(c,a),o),u.mergedAttrs=Dt(u.mergedAttrs,u.attrs),u.attrs!==null&&Oi(u,u.attrs,!1),u.mergedAttrs!==null&&Oi(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function xu(e,t){Ms(e,t),fs(t)&&e.queries.elementEnd(t)}var Tr=class extends io{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ze(t);return new Mt(n,this.ngModule)}};function Cm(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Xr.SignalBased)!==0};return o&&(i.transform=o),i})}function _m(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function xm(e,t,n){let r=t instanceof Se?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Ri(n,r):n}function Tm(e){let t=e.get(xr,null);if(t===null)throw new T(407,!1);let n=e.get(pm,null),r=e.get(tt,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function Nm(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return eu(t,n,n==="svg"?Kc:n==="math"?cp:null)}var Mt=class extends Mu{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Cm(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=_m(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=sg(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){N(22);let i=b(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:ag(this.componentDef.selectors[0]),c=Os(0,null,null,1,0,null,null,null,null,[a],null),l=xm(s,o||this.ngModule,t),u=Tm(l),f=u.rendererFactory.createRenderer(null,s),p=r?yg(f,r,s.encapsulation,l):Nm(s,f),d=As(null,c,null,512|ru(s),null,null,u,f,l,null,Ul(p,l,!0));d[V]=p,ws(d);let h=null;try{let g=_u(V,c,d,"#host",()=>[this.componentDef],!0,0);p&&(nu(f,p,g),kt(p,d)),eo(c,d,g),ks(c,g,d),xu(c,g),n!==void 0&&Sm(g,this.ngContentSelectors,n),h=me(g.index,d),d[j]=h[j],Hs(c,d,null)}catch(g){throw h!==null&&gi(h),gi(d),g}finally{N(23),bs()}return new Ai(this.componentType,d)}finally{b(i)}}},Ai=class extends dm{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=ps(n[D],V),this.location=St(this._tNode,n),this.instance=me(this._tNode.index,n)[j],this.hostView=this.changeDetectorRef=new on(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Vs(r,o[D],o,t,n);this.previousInputValues.set(t,n);let s=me(r.index,o);Ws(s,1)}get injector(){return new We(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Sm(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Gs=(()=>{class e{static __NG_ELEMENT_ID__=km}return e})();function km(){let e=U();return Nu(e,I())}var Rm=Gs,Tu=class extends Rm{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return St(this._hostTNode,this._hostLView)}get injector(){return new We(this._hostTNode,this._hostLView)}get parentInjector(){let t=Cs(this._hostTNode,this._hostLView);if(yl(t)){let n=Ir(t,this._hostLView),r=vr(t),o=n[D].data[r+8];return new We(o,n)}else return new We(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=uc(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-H}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=bt(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,wt(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!op(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new Mt(Ze(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let g=(s?l:this.parentInjector).get(Se,null);g&&(i=g)}let u=Ze(c.componentType??{}),f=bt(this._lContainer,u?.id??null),p=f?.firstChild??null,d=c.create(l,o,p,i);return this.insertImpl(d.hostView,a,wt(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(up(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[B],l=new Tu(c,c[J],c[B]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return pn(s,o,i,r),t.attachToViewContainerRef(),Pc(qo(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=uc(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=rn(this._lContainer,n);r&&(dr(qo(this._lContainer),n),to(r[D],r))}detach(t){let n=this._adjustIndex(t,-1),r=rn(this._lContainer,n);return r&&dr(qo(this._lContainer),n)!=null?new on(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function uc(e){return e[gr]}function qo(e){return e[gr]||(e[gr]=[])}function Nu(e,t){let n,r=t[e.index];return be(r)?n=r:(n=Eu(r,t,null,e),t[e.index]=n,Ps(t,n)),Am(n,t,e,r),new Tu(n,e,t)}function Om(e,t){let n=e[P],r=n.createComment(""),o=ye(t,e),i=n.parentNode(o);return Mr(n,i,r,n.nextSibling(o),!1),r}var Am=Fm,Pm=()=>!1;function Lm(e,t,n){return Pm(e,t,n)}function Fm(e,t,n,r){if(e[Je])return;let o;n.type&8?o=ge(r):o=Om(t,n),e[Je]=o}var Pi=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Li=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Qs(t,n).matches!==null&&this.queries[n].setDirty()}},Nr=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Um(t):this.predicate=t}},Fi=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},ji=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,jm(n,i)),this.matchTNodeWithReadOption(t,n,ir(n,t,i,!1,!1))}else r===_r?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,ir(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Yr||o===Gs||o===_r&&n.type&4)this.addMatch(n.index,-2);else{let i=ir(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function jm(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Vm(e,t){return e.type&11?St(e,t):e.type&4?oo(e,t):null}function Hm(e,t,n,r){return n===-1?Vm(t,e):n===-2?Bm(e,t,r):en(e,e[D],n,t)}function Bm(e,t,n){if(n===Yr)return St(t,e);if(n===_r)return oo(t,e);if(n===Gs)return Nu(t,e)}function Su(e,t,n,r){let o=t[Ee].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(Hm(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Vi(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Su(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let f=H;f<u.length;f++){let p=u[f];p[Qe]===p[B]&&Vi(p[D],p,l,r)}if(u[Et]!==null){let f=u[Et];for(let p=0;p<f.length;p++){let d=f[p];Vi(d[D],d,l,r)}}}}}return r}function Zs(e,t){return e[Ee].queries[t].queryList}function ku(e,t,n){let r=new hi((n&4)===4);return pp(e,t,r,r.destroy),(t[Ee]??=new Li).queries.push(new Pi(r))-1}function $m(e,t,n){let r=A();return r.firstCreatePass&&(Ou(r,new Nr(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),ku(r,I(),t)}function Ru(e,t,n,r){let o=A();if(o.firstCreatePass){let i=U();Ou(o,new Nr(t,n,r),i.index),qm(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return ku(o,I(),n)}function Um(e){return e.split(",").map(t=>t.trim())}function Ou(e,t,n){e.queries===null&&(e.queries=new Fi),e.queries.track(new ji(t,n))}function qm(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Qs(e,t){return e.queries.getByIndex(t)}function Au(e,t){let n=e[D],r=Qs(n,t);return r.crossesNgTemplate?Vi(n,e,t,[]):Su(n,e,r,t)}function Pu(e,t,n){let r,o=Dn(()=>{r._dirtyCounter();let i=Zm(r,e);if(t&&i===void 0)throw new T(-951,!1);return i});return r=o[G],r._dirtyCounter=ih(0),r._flatValue=void 0,o}function Wm(e){return Pu(!0,!1,e)}function zm(e){return Pu(!0,!0,e)}function Gm(e,t){let n=e[G];n._lView=I(),n._queryIndex=t,n._queryList=Zs(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function Zm(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[v]&4)return t?void 0:z;let o=Zs(n,r),i=Au(n,r);return o.reset(i,Rl),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function dc(e,t){return Wm(t)}function Qm(e,t){return zm(t)}var H_=(dc.required=Qm,dc);var sn=class{},Ym=class{};var Hi=class extends sn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Tr(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=jc(t);this._bootstrapComponents=Jl(i.bootstrap),this._r3Injector=_l(t,n,[{provide:sn,useValue:this},{provide:io,useValue:this.componentFactoryResolver},...r],Y(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Bi=class extends Ym{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Hi(this.moduleType,t,[])}};var Sr=class extends sn{injector;componentFactoryResolver=new Tr(this);instance=null;constructor(t){super();let n=new Kt([...t.providers,{provide:sn,useValue:this},{provide:io,useValue:this.componentFactoryResolver}],t.parent||Hr(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Jm(e,t,n=null){return new Sr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Km=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Vc(!1,n.type),o=r.length>0?Jm([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=$({token:e,providedIn:"environment",factory:()=>new e(Ne(Se))})}return e})();function U_(e){return ln(()=>{let t=Lu(e),n=X(K({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Pl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Km).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||nn.Emulated,styles:e.styles||z,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Rt("NgStandalone"),Fu(n);let r=e.dependencies;return n.directiveDefs=fc(r,!1),n.pipeDefs=fc(r,!0),n.id=ry(n),n})}function Xm(e){return Ze(e)||Wf(e)}function ey(e){return e!==null}function q_(e){return ln(()=>({type:e.type,bootstrap:e.bootstrap||z,declarations:e.declarations||z,imports:e.imports||z,exports:e.exports||z,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function ty(e,t){if(e==null)return Ge;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Xr.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function ny(e){if(e==null)return Ge;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function W_(e){return ln(()=>{let t=Lu(e);return Fu(t),t})}function z_(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Lu(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Ge,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||z,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:ty(e.inputs,t),outputs:ny(e.outputs),debugInfo:null}}function Fu(e){e.features?.forEach(t=>t(e))}function fc(e,t){if(!e)return null;let n=t?zf:Xm;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(ey)}function ry(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function oy(e){return Object.getPrototypeOf(e.prototype).constructor}function iy(e){let t=oy(e.type),n=!0,r=[e];for(;t;){let o;if(he(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new T(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Wo(e.inputs),s.declaredInputs=Wo(e.declaredInputs),s.outputs=Wo(e.outputs);let a=o.hostBindings;a&&uy(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&cy(e,c),l&&ly(e,l),sy(e,o),Df(e.outputs,o.outputs),he(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===iy&&(n=!1)}}t=Object.getPrototypeOf(t)}ay(r)}function sy(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function ay(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Dt(o.hostAttrs,n=Dt(n,o.hostAttrs))}}function Wo(e){return e===Ge?{}:e===z?[]:e}function cy(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function ly(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function uy(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function ju(e){return fy(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function dy(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function fy(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Vu(e,t,n){return e[t]=n}function oe(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Hu(e,t,n,r){let o=oe(e,t,n);return oe(e,t+1,r)||o}function py(e,t,n,r,o,i,s,a,c){let l=t.consts,u=Ot(t,e,4,s||null,a||null);ys()&&zs(t,n,u,Re(l,c),js),u.mergedAttrs=Dt(u.mergedAttrs,u.attrs),Ms(t,u);let f=u.tView=Os(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),f.queries=t.queries.embeddedTView(u)),u}function kr(e,t,n,r,o,i,s,a,c,l){let u=n+V,f=t.firstCreatePass?py(u,t,e,r,o,i,s,a,c):t.data[u];Ae(f,!1);let p=gy(t,e,f,n);zr()&&no(t,e,p,f),kt(p,e);let d=Eu(p,e,p,f);return e[u]=d,Ps(e,d),Lm(d,f,e),$r(f)&&eo(t,e,f),c!=null&&Ls(e,f,l),f}function hy(e,t,n,r,o,i,s,a){let c=I(),l=A(),u=Re(l.consts,i);return kr(c,l,e,t,n,r,o,u,s,a),hy}var gy=my;function my(e,t,n,r){return Gr(!0),t[P].createComment("")}var G_=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var yy=new R("");var Bu=(()=>{class e{static \u0275prov=$({token:e,providedIn:"root",factory:()=>new $i})}return e})(),$i=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function $u(e){return!!e&&typeof e.then=="function"}function vy(e){return!!e&&typeof e.subscribe=="function"}var Iy=new R("");var Uu=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=C(Iy,{optional:!0})??[];injector=C(Oe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=qc(this.injector,o);if($u(i))n.push(i);else if(vy(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ey=new R("");function Dy(){yo(()=>{throw new T(600,!1)})}function wy(e){return e.isBoundToModule}var by=10;var an=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=C(th);afterRenderManager=C(Bl);zonelessEnabled=C(_s);rootEffectScheduler=C(Bu);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new ne;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=C(Qr).hasPendingTasks.pipe(Be(n=>!n));constructor(){C(Jr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=C(Se);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Oe.NULL){N(10);let i=n instanceof Mu;if(!this._injector.get(Uu).done){let d="";throw new T(405,d)}let a;i?a=n:a=this._injector.get(io).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=wy(a)?void 0:this._injector.get(sn),l=r||a.selector,u=a.create(o,[],l,c),f=u.location.nativeElement,p=u.injector.get(yy,null);return p?.registerApplication(f),u.onDestroy(()=>{this.detachView(u.hostView),sr(this.components,u),p?.unregisterApplication(f)}),this._loadComponent(u),N(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){N(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ns.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new T(101,!1);let n=b(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,b(n),this.afterTick.next(),N(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(xr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<by;)N(14),this.synchronizeOnce(),N(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)My(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Ur(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;sr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Ey,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>sr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new T(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function sr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function My(e,t,n,r){if(!n&&!Ur(e))return;mu(e,t,n&&!r?0:1)}function Cy(e,t,n,r){let o=I(),i=ot();if(oe(o,i,t)){let s=A(),a=Wr();Cg(a,o,e,t,n,r)}return Cy}function qu(e,t,n,r){return oe(e,ot(),n)?t+Jt(n)+r:ie}function _y(e,t,n,r,o,i){let s=Dp(),a=Hu(e,s,n,o);return Es(2),a?t+Jt(n)+r+Jt(o)+i:ie}function tr(e,t){return e<<17|t<<2}function rt(e){return e>>17&32767}function xy(e){return(e&2)==2}function Ty(e,t){return e&131071|t<<17}function Ui(e){return e|2}function Ct(e){return(e&131068)>>2}function zo(e,t){return e&-131069|t<<2}function Ny(e){return(e&1)===1}function qi(e){return e|1}function Sy(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=rt(s),c=Ct(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let f=n;u=f[1],(u===null||un(f,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=rt(e[a+1]);e[r+1]=tr(p,a),p!==0&&(e[p+1]=zo(e[p+1],r)),e[a+1]=Ty(e[a+1],r)}else e[r+1]=tr(a,0),a!==0&&(e[a+1]=zo(e[a+1],r)),a=r;else e[r+1]=tr(c,0),a===0?a=r:e[c+1]=zo(e[c+1],r),c=r;l&&(e[r+1]=Ui(e[r+1])),pc(e,u,r,!0),pc(e,u,r,!1),ky(t,u,e,r,i),s=tr(a,c),i?t.classBindings=s:t.styleBindings=s}function ky(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&un(i,t)>=0&&(n[r+1]=qi(n[r+1]))}function pc(e,t,n,r){let o=e[n+1],i=t===null,s=r?rt(o):Ct(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];Ry(c,t)&&(a=!0,e[s+1]=r?qi(l):Ui(l)),s=r?rt(l):Ct(l)}a&&(e[n+1]=r?Ui(o):qi(o))}function Ry(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?un(e,t)>=0:!1}var ce={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Oy(e){return e.substring(ce.key,ce.keyEnd)}function Ay(e){return Py(e),Wu(e,zu(e,0,ce.textEnd))}function Wu(e,t){let n=ce.textEnd;return n===t?-1:(t=ce.keyEnd=Ly(e,ce.key=t,n),zu(e,t,n))}function Py(e){ce.key=0,ce.keyEnd=0,ce.value=0,ce.valueEnd=0,ce.textEnd=e.length}function zu(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function Ly(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function Fy(e,t,n){let r=I(),o=ot();if(oe(r,o,t)){let i=A(),s=Wr();Fs(i,s,r,e,t,r[P],n,!1)}return Fy}function Wi(e,t,n,r,o){Vs(t,e,n,o?"class":"style",r)}function jy(e,t,n){return Zu(e,t,n,!1),jy}function Vy(e,t){return Zu(e,t,null,!0),Vy}function Z_(e){Qu(Wy,Gu,e,!0)}function Gu(e,t){for(let n=Ay(t);n>=0;n=Wu(t,n))Vr(e,Oy(t),!0)}function Zu(e,t,n,r){let o=I(),i=A(),s=Es(2);if(i.firstUpdatePass&&Ju(i,e,s,r),t!==ie&&oe(o,s,t)){let a=i.data[Me()];Ku(i,a,o,o[P],e,o[s+1]=Gy(t,n),r,s)}}function Qu(e,t,n,r){let o=A(),i=Es(2);o.firstUpdatePass&&Ju(o,null,i,r);let s=I();if(n!==ie&&oe(s,i,n)){let a=o.data[Me()];if(Xu(a,r)&&!Yu(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Jo(c,n||"")),Wi(o,a,s,n,r)}else zy(o,a,s,s[P],s[i+1],s[i+1]=qy(e,t,n),r,i)}}function Yu(e,t){return t>=e.expandoStartIndex}function Ju(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Me()],s=Yu(e,n);Xu(i,r)&&t===null&&!s&&(t=!1),t=Hy(o,i,t,r),Sy(o,i,t,n,s,r)}}function Hy(e,t,n,r){let o=_p(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Go(null,e,t,n,r),n=cn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Go(o,e,t,n,r),i===null){let c=By(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Go(null,e,t,c[1],r),c=cn(c,t.attrs,r),$y(e,t,r,c))}else i=Uy(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function By(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Ct(r)!==0)return e[rt(r)]}function $y(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[rt(o)]=r}function Uy(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=cn(r,s,n)}return cn(r,t.attrs,n)}function Go(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=cn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function cn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Vr(e,s,n?!0:t[++i]))}return e===void 0?null:e}function qy(e,t,n){if(n==null||n==="")return z;let r=[],o=Wl(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function Wy(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Vr(e,r,n)}function zy(e,t,n,r,o,i,s,a){o===ie&&(o=z);let c=0,l=0,u=0<o.length?o[0]:null,f=0<i.length?i[0]:null;for(;u!==null||f!==null;){let p=c<o.length?o[c+1]:void 0,d=l<i.length?i[l+1]:void 0,h=null,g;u===f?(c+=2,l+=2,p!==d&&(h=f,g=d)):f===null||u!==null&&u<f?(c+=2,h=u):(l+=2,h=f,g=d),h!==null&&Ku(e,t,n,r,h,g,s,a),u=c<o.length?o[c]:null,f=l<i.length?i[l]:null}}function Ku(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=Ny(l)?hc(c,t,n,o,Ct(l),s):void 0;if(!Rr(u)){Rr(i)||xy(l)&&(i=hc(c,null,n,o,a,s));let f=Xc(Me(),n);Ug(r,s,f,o,i)}}function hc(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,f=u===null,p=n[o+1];p===ie&&(p=f?z:void 0);let d=f?Fo(p,r):u===r?p:void 0;if(l&&!Rr(d)&&(d=Fo(c,r)),Rr(d)&&(a=d,s))return a;let h=e[o+1];o=s?rt(h):Ct(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Fo(c,r))}return a}function Rr(e){return e!==void 0}function Gy(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Y(Wl(e)))),e}function Xu(e,t){return(e.flags&(t?8:16))!==0}function Q_(e,t,n){let r=I(),o=qu(r,e,t,n);Qu(Vr,Gu,o,!0)}var zi=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Zo(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function Zy(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],f=Zo(i,l,i,u,n);if(f!==0){f<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),d=t[c],h=Zo(s,p,c,d,n);if(h!==0){h<0&&e.updateValue(s,d),s--,c--;continue}let g=n(i,l),O=n(s,p),_=n(i,u);if(Object.is(_,O)){let ue=n(c,d);Object.is(ue,g)?(e.swap(i,s),e.updateValue(s,d),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new Or,o??=mc(e,i,s,n),Gi(e,r,i,_))e.updateValue(i,u),i++,s++;else if(o.has(_))r.set(g,e.detach(i)),s--;else{let ue=e.create(i,t[i]);e.attach(i,ue),i++,s++}}for(;i<=c;)gc(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),f=l.value,p=Zo(i,u,i,f,n);if(p!==0)p<0&&e.updateValue(i,f),i++,l=c.next();else{r??=new Or,o??=mc(e,i,s,n);let d=n(i,f);if(Gi(e,r,i,d))e.updateValue(i,f),i++,s++,l=c.next();else if(!o.has(d))e.attach(i,e.create(i,f)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)gc(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Gi(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function gc(e,t,n,r,o){if(Gi(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function mc(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Or=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function Y_(e,t){Rt("NgControlFlow");let n=I(),r=ot(),o=n[r]!==ie?n[r]:-1,i=o!==-1?Ar(n,V+o):void 0,s=0;if(oe(n,r,e)){let a=b(null);try{if(i!==void 0&&wu(i,s),e!==-1){let c=V+e,l=Ar(n,c),u=Ji(n[D],c),f=bt(l,u.tView.ssrId),p=fn(n,u,t,{dehydratedView:f});pn(l,p,s,wt(u,f))}}finally{b(a)}}else if(i!==void 0){let a=Du(i,s);a!==void 0&&(a[j]=t)}}var Zi=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-H}};function J_(e,t){return t}var Qi=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function K_(e,t,n,r,o,i,s,a,c,l,u,f,p){Rt("NgControlFlow");let d=I(),h=A(),g=c!==void 0,O=I(),_=a?s.bind(O[te][j]):s,ue=new Qi(g,_);O[V+e]=ue,kr(d,h,e+1,t,n,r,o,Re(h.consts,i)),g&&kr(d,h,e+2,c,l,u,f,Re(h.consts,p))}var Yi=class extends zi{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-H}at(t){return this.getLView(t)[j].$implicit}attach(t,n){let r=n[vt];this.needsIndexUpdate||=t!==this.length,pn(this.lContainer,n,t,wt(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,Qy(this.lContainer,t)}create(t,n){let r=bt(this.lContainer,this.templateTNode.tView.ssrId),o=fn(this.hostLView,this.templateTNode,new Zi(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){to(t[D],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[j].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[j].$index=t}getLView(t){return Yy(this.lContainer,t)}};function X_(e){let t=b(null),n=Me();try{let r=I(),o=r[D],i=r[n],s=n+1,a=Ar(r,s);if(i.liveCollection===void 0){let l=Ji(o,s);i.liveCollection=new Yi(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(Zy(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=ot(),u=c.length===0;if(oe(r,l,u)){let f=n+2,p=Ar(r,f);if(u){let d=Ji(o,f),h=bt(p,d.tView.ssrId),g=fn(r,d,void 0,{dehydratedView:h});pn(p,g,0,wt(d,h))}else wu(p,0)}}}finally{b(t)}}function Ar(e,t){return e[t]}function Qy(e,t){return rn(e,t)}function Yy(e,t){return Du(e,t)}function Ji(e,t){return ps(e,t)}function ed(e,t,n,r){let o=I(),i=A(),s=V+e,a=o[P],c=i.firstCreatePass?_u(s,i,o,t,js,ys(),n,r):i.data[s],l=Ky(i,o,c,a,t,e);o[s]=l;let u=$r(c);return Ae(c,!0),nu(a,l,c),!$s(c)&&zr()&&no(i,o,l,c),(hp()===0||u)&&kt(l,o),gp(),u&&(eo(i,o,c),ks(i,c,o)),r!==null&&Ls(o,c),ed}function td(){let e=U();vs()?Is():(e=e.parent,Ae(e,!1));let t=e;yp(t)&&vp(),mp();let n=A();return n.firstCreatePass&&xu(n,t),t.classesWithoutHost!=null&&Op(t)&&Wi(n,t,I(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Ap(t)&&Wi(n,t,I(),t.stylesWithoutHost,!1),td}function Jy(e,t,n,r){return ed(e,t,n,r),td(),Jy}var Ky=(e,t,n,r,o,i)=>(Gr(!0),eu(r,o,Sp()));function Xy(e,t,n,r,o){let i=t.consts,s=Re(i,r),a=Ot(t,e,8,"ng-container",s);s!==null&&Oi(a,s,!0);let c=Re(i,o);return ys()&&zs(t,n,a,c,js),a.mergedAttrs=Dt(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function nd(e,t,n){let r=I(),o=A(),i=e+V,s=o.firstCreatePass?Xy(i,o,r,t,n):o.data[i];Ae(s,!0);let a=tv(o,r,s,e);return r[i]=a,zr()&&no(o,r,a,s),kt(a,r),$r(s)&&(eo(o,r,s),ks(o,s,r)),n!=null&&Ls(r,s),nd}function rd(){let e=U(),t=A();return vs()?Is():(e=e.parent,Ae(e,!1)),t.firstCreatePass&&(Ms(t,e),fs(e)&&t.queries.elementEnd(e)),rd}function ev(e,t,n){return nd(e,t,n),rd(),ev}var tv=(e,t,n,r)=>(Gr(!0),ug(t[P],""));function ex(){return I()}function nv(e,t,n){let r=I(),o=ot();if(oe(r,o,t)){let i=A(),s=Wr();Fs(i,s,r,e,t,r[P],n,!0)}return nv}var Pr="en-US";var rv=Pr;function ov(e){typeof e=="string"&&(rv=e.toLowerCase().replace(/_/g,"-"))}function yc(e,t,n){return function r(o){if(o===Function)return n;let i=xt(e)?me(e.index,t):t;Ws(i,5);let s=t[j],a=vc(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=vc(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function vc(e,t,n,r){let o=b(null);try{return N(6,t,n),n(r)!==!1}catch(i){return iv(e,i),!1}finally{N(7,t,n),b(o)}}function iv(e,t){let n=e[It],r=n?n.get(nt,null):null;r&&r.handleError(t)}function Ic(e,t,n,r,o,i){let s=t[n],a=t[D],l=a.data[n].outputs[r],u=s[l],f=a.firstCreatePass?ms(a):null,p=gs(t),d=u.subscribe(i),h=p.length;p.push(i,d),f&&f.push(o,e.index,h,-(h+1))}function sv(e,t,n,r){let o=I(),i=A(),s=U();return od(i,o,o[P],s,e,t,r),sv}function av(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[hr],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function od(e,t,n,r,o,i,s){let a=$r(r),l=e.firstCreatePass?ms(e):null,u=gs(t),f=!0;if(r.type&3||s){let p=ye(r,t),d=s?s(p):p,h=u.length,g=s?_=>s(ge(_[r.index])):r.index,O=null;if(!s&&a&&(O=av(e,t,o,r.index)),O!==null){let _=O.__ngLastListenerFn__||O;_.__ngNextListenerFn__=i,O.__ngLastListenerFn__=i,f=!1}else{i=yc(r,t,i),Mh(t,d,o,i);let _=n.listen(d,o,i);u.push(i,_),l&&l.push(o,g,h,h+1)}}else i=yc(r,t,i);if(f){let p=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let h=0;h<d.length;h+=2){let g=d[h],O=d[h+1];Ic(r,t,g,O,o,i)}if(p&&p.length)for(let h of p)Ic(r,t,h,o,o,i)}}function tx(e=1){return Tp(e)}function cv(e,t){let n=null,r=tg(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Xl(e,i,!0):og(r,i))return o}return n}function nx(e){let t=I()[te][J];if(!t.projection){let n=e?e.length:1,r=t.projection=$f(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?cv(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function rx(e,t=0,n,r,o,i){let s=I(),a=A(),c=r?e+1:null;c!==null&&kr(s,a,c,r,o,i,null,n);let l=Ot(a,V+e,16,null,n||null);l.projection===null&&(l.projection=t),Is();let f=!s[vt]||il();s[te][J].projection[l.projection]===null&&c!==null?lv(s,a,c):f&&!$s(l)&&Bg(a,s,l)}function lv(e,t,n){let r=V+n,o=t.data[r],i=e[r],s=bt(i,o.tView.ssrId),a=fn(e,o,void 0,{dehydratedView:s});pn(i,a,0,wt(o,s))}function ox(e,t,n,r){Ru(e,t,n,r)}function ix(e,t,n){$m(e,t,n)}function sx(e){let t=I(),n=A(),r=Ds();qr(r+1);let o=Qs(n,r);if(e.dirty&&lp(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Au(t,r);e.reset(i,Rl),e.notifyOnChanges()}return!0}return!1}function ax(){return Zs(I(),Ds())}function cx(e,t,n,r,o){Gm(t,Ru(e,n,r,o))}function lx(e=1){qr(Ds()+e)}function ux(e){let t=Ep();return el(t,V+e)}function dx(e,t=""){let n=I(),r=A(),o=e+V,i=r.firstCreatePass?Ot(r,o,1,t,null):r.data[o],s=uv(r,n,i,t,e);n[o]=s,zr()&&no(r,n,s,i),Ae(i,!1)}var uv=(e,t,n,r,o)=>(Gr(!0),cg(t[P],r));function dv(e){return id("",e,""),dv}function id(e,t,n){let r=I(),o=qu(r,e,t,n);return o!==ie&&sd(r,Me(),o),id}function fv(e,t,n,r,o){let i=I(),s=_y(i,e,t,n,r,o);return s!==ie&&sd(i,Me(),s),fv}function sd(e,t,n){let r=Xc(t,e);lg(e[P],r,n)}function pv(e,t,n){Ol(t)&&(t=t());let r=I(),o=ot();if(oe(r,o,t)){let i=A(),s=Wr();Fs(i,s,r,e,t,r[P],n,!1)}return pv}function fx(e,t){let n=Ol(e);return n&&e.set(t),n}function hv(e,t){let n=I(),r=A(),o=U();return od(r,n,n[P],o,e,t),hv}var gv={};function mv(e){let t=A(),n=I(),r=e+V,o=Ot(t,r,128,null,null);return Ae(o,!1),tl(t,n,r,gv),mv}function yv(e,t,n){let r=A();if(r.firstCreatePass){let o=he(e);Ki(n,r.data,r.blueprint,o,!0),Ki(t,r.data,r.blueprint,o,!1)}}function Ki(e,t,n,r,o){if(e=W(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ki(e[i],t,n,r,o);else{let i=A(),s=I(),a=U(),c=yt(e)?e:W(e.provide),l=Uc(e),u=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(yt(e)||!e.multi){let d=new et(l,o,so),h=Yo(c,t,o?u:u+p,f);h===-1?(li(Dr(a,s),i,c),Qo(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[h]=d,s[h]=d)}else{let d=Yo(c,t,u+p,f),h=Yo(c,t,u,u+p),g=d>=0&&n[d],O=h>=0&&n[h];if(o&&!O||!o&&!g){li(Dr(a,s),i,c);let _=Ev(o?Iv:vv,n.length,o,r,l);!o&&O&&(n[h].providerFactory=_),Qo(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(_),s.push(_)}else{let _=ad(n[o?h:d],l,!o&&r);Qo(i,e,d>-1?d:h,_)}!o&&r&&O&&n[h].componentProviders++}}}function Qo(e,t,n,r){let o=yt(t),i=Kf(t);if(o||i){let c=(i?W(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function ad(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Yo(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function vv(e,t,n,r,o){return Xi(this.multi,[])}function Iv(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=en(r,r[D],this.providerFactory.index,o);s=c.slice(0,a),Xi(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],Xi(i,s);return s}function Xi(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Ev(e,t,n,r,o){let i=new et(e,n,so);return i.multi=[],i.index=t,i.componentProviders=0,ad(i,o,r&&!n),i}function px(e,t=[]){return n=>{n.providersResolver=(r,o)=>yv(r,o?o(e):e,t)}}function hx(e,t,n,r,o){return wv(I(),cl(),e,t,n,r,o)}function cd(e,t){let n=e[t];return n===ie?void 0:n}function Dv(e,t,n,r,o,i){let s=t+n;return oe(e,s,o)?Vu(e,s+1,i?r.call(i,o):r(o)):cd(e,s+1)}function wv(e,t,n,r,o,i,s){let a=t+n;return Hu(e,a,o,i)?Vu(e,a+2,s?r.call(s,o,i):r(o,i)):cd(e,a+2)}function gx(e,t){let n=A(),r,o=e+V;n.firstCreatePass?(r=bv(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=ze(r.type,!0)),s,a=Q(so);try{let c=Er(!1),l=i();return Er(c),tl(n,I(),o,l),l}finally{Q(a)}}function bv(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function mx(e,t,n){let r=e+V,o=I(),i=el(o,r);return Mv(o,r)?Dv(o,cl(),t,i.transform,n,i):i.transform(n)}function Mv(e,t){return e[D].data[t].pure}function yx(e,t){return oo(e,t)}var es=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},vx=new es("19.2.14"),ts=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Ix=(()=>{class e{compileModuleSync(n){return new Bi(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=jc(n),i=Jl(o.declarations).reduce((s,a)=>{let c=Ze(a);return c&&s.push(new Mt(c)),s},[]);return new ts(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Cv=(()=>{class e{zone=C(ee);changeDetectionScheduler=C(tt);applicationRef=C(an);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),_v=new R("",{factory:()=>!1});function ld({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new ee(X(K({},ud()),{scheduleInRootZone:n})),[{provide:ee,useFactory:e},{provide:fr,multi:!0,useFactory:()=>{let r=C(Cv,{optional:!0});return()=>r.initialize()}},{provide:fr,multi:!0,useFactory:()=>{let r=C(xv);return()=>{r.initialize()}}},t===!0?{provide:Tl,useValue:!0}:[],{provide:Nl,useValue:n??xl}]}function Ex(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=ld({ngZoneFactory:()=>{let o=ud(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Rt("NgZone_CoalesceEvent"),new ee(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Gf([{provide:_v,useValue:!0},{provide:_s,useValue:!1},r])}function ud(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var xv=(()=>{class e{subscription=new L;initialized=!1;zone=C(ee);pendingTasks=C(Qr);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ee.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ee.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Tv=(()=>{class e{appRef=C(an);taskService=C(Qr);ngZone=C(ee);zonelessEnabled=C(_s);tracing=C(Jr,{optional:!0});disableScheduling=C(Tl,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new L;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(br):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(C(Nl,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof pi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Za:Sl;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(br+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Za(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Nv(){return typeof $localize<"u"&&$localize.locale||Pr}var dd=new R("",{providedIn:"root",factory:()=>C(dd,M.Optional|M.SkipSelf)||Nv()});var ns=new R(""),Sv=new R("");function Gt(e){return!e.moduleRef}function kv(e){let t=Gt(e)?e.r3Injector:e.moduleRef.injector,n=t.get(ee);return n.run(()=>{Gt(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(nt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Gt(e)){let i=()=>t.destroy(),s=e.platformInjector.get(ns);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ns);s.add(i),e.moduleRef.onDestroy(()=>{sr(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Ov(r,n,()=>{let i=t.get(Uu);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(dd,Pr);if(ov(s||Pr),!t.get(Sv,!0))return Gt(e)?t.get(an):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Gt(e)){let c=t.get(an);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return Rv(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function Rv(e,t){let n=e.injector.get(an);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new T(-403,!1);t.push(e)}function Ov(e,t,n){try{let r=n();return $u(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var ar=null;function Av(e=[],t){return Oe.create({name:t,providers:[{provide:$c,useValue:"platform"},{provide:ns,useValue:new Set([()=>ar=null])},...e]})}function Pv(e=[]){if(ar)return ar;let t=Av(e);return ar=t,Dy(),Lv(t),t}function Lv(e){let t=e.get(ph,null);qc(e,()=>{t?.forEach(n=>n())})}var Dx=(()=>{class e{static __NG_ELEMENT_ID__=Fv}return e})();function Fv(e){return jv(U(),I(),(e&16)===16)}function jv(e,t,n){if(xt(e)&&!n){let r=me(e.index,t);return new on(r,r)}else if(e.type&175){let r=t[te];return new on(r,t)}return null}var rs=class{constructor(){}supports(t){return ju(t)}create(t){return new os(t)}},Vv=(e,t)=>t,os=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||Vv}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Ec(r,o,i)?n:r,a=Ec(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let d=p<i.length?i[p]:i[p]=0,h=d+p;u<=h&&h<l&&(i[p]=d+1)}let f=s.previousIndex;i[f]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!ju(t))throw new T(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,dy(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new is(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Lr),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Lr),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},is=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},ss=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Lr=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new ss,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Ec(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Dc(){return new Hv([new rs])}var Hv=(()=>{class e{factories;static \u0275prov=$({token:e,providedIn:"root",factory:Dc});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Dc()),deps:[[e,new Vf,new jf]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new T(901,!1)}}return e})();function wx(e){N(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Pv(r),i=[ld({}),{provide:tt,useExisting:Tv},...n||[]],s=new Sr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return kv({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{N(9)}}function bx(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Mx(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function Cx(e){return Eo(e)}function _x(e,t){return Dn(e,t?.equal)}var as=class{[G];constructor(t){this[G]=t}destroy(){this[G].destroy()}};function Bv(e,t){!t?.injector&&ds(Bv);let n=t?.injector??C(Oe),r=t?.manualCleanup!==!0?n.get(Zr):null,o,i=n.get(Ss,null,{optional:!0}),s=n.get(tt);return i!==null&&!t?.forceRoot?(o=qv(i.view,s,e),r instanceof wr&&r._lView===i.view&&(r=null)):o=Wv(e,n.get(Bu),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new as(o)}var fd=X(K({},it),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:tn,run(){if(this.dirty=!1,this.hasRun&&!vn(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Lt(this),n=yr(!1);try{this.maybeCleanup(),this.fn(e)}finally{yr(n),yn(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),$v=X(K({},fd),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){Ft(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),Uv=X(K({},fd),{consumerMarkedDirty(){this.view[v]|=8192,Nt(this.view),this.notifier.notify(13)},destroy(){Ft(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Ye]?.delete(this)}});function qv(e,t,n){let r=Object.create(Uv);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Ye]??=new Set,e[Ye].add(r),r.consumerMarkedDirty(r),r}function Wv(e,t,n){let r=Object.create($v);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function xx(e,t){let n=Ze(e),r=t.elementInjector||Hr();return new Mt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}export{K as a,X as b,zv as c,L as d,xd as e,x as f,So as g,ko as h,ne as i,Bt as j,Ut as k,je as l,fe as m,Fd as n,jd as o,Vd as p,He as q,Be as r,Gd as s,$e as t,Yn as u,Qd as v,Yd as w,Jd as x,Ue as y,Kd as z,ka as A,Xd as B,ef as C,zt as D,Oo as E,tf as F,of as G,sf as H,Ao as I,af as J,cf as K,lf as L,Lo as M,uf as N,df as O,ff as P,pf as Q,hf as R,gf as S,mf as T,T as U,_c as V,$ as W,l_ as X,u_ as Y,R as Z,M as _,Ne as $,C as aa,jf as ba,Vf as ca,Gf as da,$c as ea,Se as fa,qc as ga,d_ as ha,f_ as ia,p_ as ja,h_ as ka,g_ as la,m_ as ma,Oe as na,Ga as oa,Zr as pa,tt as qa,Qr as ra,_e as sa,ee as ta,nt as ua,y_ as va,Yr as wa,oh as xa,ih as ya,hi as za,v_ as Aa,I_ as Ba,ph as Ca,E_ as Da,D_ as Ea,w_ as Fa,Jr as Ga,Rt as Ha,Eh as Ia,Dh as Ja,nn as Ka,Wl as La,b_ as Ma,M_ as Na,C_ as Oa,__ as Pa,x_ as Qa,T_ as Ra,kh as Sa,N_ as Ta,Wh as Ua,S_ as Va,Ti as Wa,_r as Xa,xr as Ya,F_ as Za,so as _a,j_ as $a,Gs as ab,H_ as bb,sn as cb,Ym as db,Jm as eb,U_ as fb,q_ as gb,W_ as hb,z_ as ib,iy as jb,hy as kb,G_ as lb,$u as mb,Ey as nb,an as ob,Cy as pb,Fy as qb,jy as rb,Vy as sb,Z_ as tb,Q_ as ub,Y_ as vb,J_ as wb,K_ as xb,X_ as yb,ed as zb,td as Ab,Jy as Bb,ev as Cb,ex as Db,nv as Eb,sv as Fb,tx as Gb,nx as Hb,rx as Ib,ox as Jb,ix as Kb,sx as Lb,ax as Mb,cx as Nb,lx as Ob,ux as Pb,dx as Qb,dv as Rb,id as Sb,fv as Tb,pv as Ub,fx as Vb,hv as Wb,mv as Xb,px as Yb,hx as Zb,gx as _b,mx as $b,yx as ac,vx as bc,Ix as cc,Ex as dc,dd as ec,Dx as fc,Hv as gc,wx as hc,bx as ic,Mx as jc,Cx as kc,_x as lc,Bv as mc,xx as nc};
