<!DOCTYPE html>
<html>
<head>
    <title>File Test</title>
</head>
<body>
    <h1>Testing File Access</h1>
    <p>If you can see this, the Truck directory is accessible.</p>
    
    <script>
        // Test if main.js is accessible
        fetch('main.js')
            .then(response => {
                if (response.ok) {
                    document.body.innerHTML += '<p style="color: green;">✅ main.js is accessible</p>';
                } else {
                    document.body.innerHTML += '<p style="color: red;">❌ main.js is NOT accessible (Status: ' + response.status + ')</p>';
                }
            })
            .catch(error => {
                document.body.innerHTML += '<p style="color: red;">❌ Error accessing main.js: ' + error.message + '</p>';
            });
            
        // Test if styles.css is accessible
        fetch('styles.css')
            .then(response => {
                if (response.ok) {
                    document.body.innerHTML += '<p style="color: green;">✅ styles.css is accessible</p>';
                } else {
                    document.body.innerHTML += '<p style="color: red;">❌ styles.css is NOT accessible (Status: ' + response.status + ')</p>';
                }
            })
            .catch(error => {
                document.body.innerHTML += '<p style="color: red;">❌ Error accessing styles.css: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
