var Zu=Object.defineProperty,Qu=Object.defineProperties;var Yu=Object.getOwnPropertyDescriptors;var an=Object.getOwnPropertySymbols;var Vs=Object.prototype.hasOwnProperty,Hs=Object.prototype.propertyIsEnumerable;var js=(e,t,n)=>t in e?Zu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,K=(e,t)=>{for(var n in t||={})Vs.call(t,n)&&js(e,n,t[n]);if(an)for(var n of an(t))Hs.call(t,n)&&js(e,n,t[n]);return e},X=(e,t)=>Qu(e,Yu(t));var Cv=(e,t)=>{var n={};for(var r in e)Vs.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&an)for(var r of an(e))t.indexOf(r)<0&&Hs.call(e,r)&&(n[r]=e[r]);return n};function no(e,t){return Object.is(e,t)}var F=null,cn=!1,ro=1,z=Symbol("SIGNAL");function w(e){let t=F;return F=e,t}function oo(){return F}var nt={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Nt(e){if(cn)throw new Error("");if(F===null)return;F.consumerOnSignalRead(e);let t=F.nextProducerIndex++;if(pn(F),t<F.producerNode.length&&F.producerNode[t]!==e&&Tt(F)){let n=F.producerNode[t];fn(n,F.producerIndexOfThis[t])}F.producerNode[t]!==e&&(F.producerNode[t]=e,F.producerIndexOfThis[t]=Tt(F)?$s(e,F,t):0),F.producerLastReadVersion[t]=e.version}function Bs(){ro++}function io(e){if(!(Tt(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ro)){if(!e.producerMustRecompute(e)&&!dn(e)){to(e);return}e.producerRecomputeValue(e),to(e)}}function so(e){if(e.liveConsumerNode===void 0)return;let t=cn;cn=!0;try{for(let n of e.liveConsumerNode)n.dirty||Ju(n)}finally{cn=t}}function ao(){return F?.consumerAllowSignalWrites!==!1}function Ju(e){e.dirty=!0,so(e),e.consumerMarkedDirty?.(e)}function to(e){e.dirty=!1,e.lastCleanEpoch=ro}function St(e){return e&&(e.nextProducerIndex=0),w(e)}function un(e,t){if(w(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Tt(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)fn(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function dn(e){pn(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(io(n),r!==n.version))return!0}return!1}function kt(e){if(pn(e),Tt(e))for(let t=0;t<e.producerNode.length;t++)fn(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function $s(e,t,n){if(Us(e),e.liveConsumerNode.length===0&&qs(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=$s(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function fn(e,t){if(Us(e),e.liveConsumerNode.length===1&&qs(e))for(let r=0;r<e.producerNode.length;r++)fn(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];pn(o),o.producerIndexOfThis[r]=t}}function Tt(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function pn(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Us(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function qs(e){return e.producerNode!==void 0}function hn(e,t){let n=Object.create(Ku);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(io(n),Nt(n),n.value===ln)throw n.error;return n.value};return r[z]=n,r}var Xr=Symbol("UNSET"),eo=Symbol("COMPUTING"),ln=Symbol("ERRORED"),Ku=X(K({},nt),{value:Xr,dirty:!0,error:null,equal:no,kind:"computed",producerMustRecompute(e){return e.value===Xr||e.value===eo},producerRecomputeValue(e){if(e.value===eo)throw new Error("Detected cycle in computations.");let t=e.value;e.value=eo;let n=St(e),r,o=!1;try{r=e.computation(),w(null),o=t!==Xr&&t!==ln&&r!==ln&&e.equal(t,r)}catch(i){r=ln,e.error=i}finally{un(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Xu(){throw new Error}var Ws=Xu;function zs(e){Ws(e)}function co(e){Ws=e}var ed=null;function lo(e,t){let n=Object.create(gn);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Nt(n),n.value);return r[z]=n,r}function Rt(e,t){ao()||zs(e),e.equal(e.value,t)||(e.value=t,td(e))}function uo(e,t){ao()||zs(e),Rt(e,t(e.value))}var gn=X(K({},nt),{equal:no,value:void 0,kind:"signal"});function td(e){e.version++,Bs(),so(e),ed?.()}function fo(e){let t=w(null);try{return e()}finally{w(t)}}var po;function Ot(){return po}function me(e){let t=po;return po=e,t}var mn=Symbol("NotFound");function v(e){return typeof e=="function"}function rt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var yn=rt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Re(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var L=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(v(r))try{r()}catch(i){t=i instanceof yn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Gs(i)}catch(s){t=t??[],s instanceof yn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new yn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Gs(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Re(n,t)}remove(t){let{_finalizers:n}=this;n&&Re(n,t),t instanceof e&&t._removeParent(this)}};L.EMPTY=(()=>{let e=new L;return e.closed=!0,e})();var ho=L.EMPTY;function vn(e){return e instanceof L||e&&"closed"in e&&v(e.remove)&&v(e.add)&&v(e.unsubscribe)}function Gs(e){v(e)?e():e.unsubscribe()}var oe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var ot={setTimeout(e,t,...n){let{delegate:r}=ot;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=ot;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function In(e){ot.setTimeout(()=>{let{onUnhandledError:t}=oe;if(t)t(e);else throw e})}function At(){}var Zs=go("C",void 0,void 0);function Qs(e){return go("E",void 0,e)}function Ys(e){return go("N",e,void 0)}function go(e,t,n){return{kind:e,value:t,error:n}}var Oe=null;function it(e){if(oe.useDeprecatedSynchronousErrorHandling){let t=!Oe;if(t&&(Oe={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Oe;if(Oe=null,n)throw r}}else e()}function Js(e){oe.useDeprecatedSynchronousErrorHandling&&Oe&&(Oe.errorThrown=!0,Oe.error=e)}var Ae=class extends L{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,vn(t)&&t.add(this)):this.destination=ad}static create(t,n,r){return new ye(t,n,r)}next(t){this.isStopped?yo(Ys(t),this):this._next(t)}error(t){this.isStopped?yo(Qs(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?yo(Zs,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},id=Function.prototype.bind;function mo(e,t){return id.call(e,t)}var vo=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){En(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){En(r)}else En(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){En(n)}}},ye=class extends Ae{constructor(t,n,r){super();let o;if(v(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&oe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&mo(t.next,i),error:t.error&&mo(t.error,i),complete:t.complete&&mo(t.complete,i)}):o=t}this.destination=new vo(o)}};function En(e){oe.useDeprecatedSynchronousErrorHandling?Js(e):In(e)}function sd(e){throw e}function yo(e,t){let{onStoppedNotification:n}=oe;n&&ot.setTimeout(()=>n(e,t))}var ad={closed:!0,next:At,error:sd,complete:At};var st=typeof Symbol=="function"&&Symbol.observable||"@@observable";function B(e){return e}function cd(...e){return Io(e)}function Io(e){return e.length===0?B:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var _=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=ud(n)?n:new ye(n,r,o);return it(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ks(r),new r((o,i)=>{let s=new ye({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[st](){return this}pipe(...n){return Io(n)(this)}toPromise(n){return n=Ks(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ks(e){var t;return(t=e??oe.Promise)!==null&&t!==void 0?t:Promise}function ld(e){return e&&v(e.next)&&v(e.error)&&v(e.complete)}function ud(e){return e&&e instanceof Ae||ld(e)&&vn(e)}function Eo(e){return v(e?.lift)}function m(e){return t=>{if(Eo(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function g(e,t,n,r,o){return new Do(e,t,n,r,o)}var Do=class extends Ae{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function wo(){return m((e,t)=>{let n=null;e._refCount++;let r=g(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var bo=class extends _{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Eo(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new L;let n=this.getSubject();t.add(this.source.subscribe(g(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=L.EMPTY)}return t}refCount(){return wo()(this)}};var Xs=rt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var te=(()=>{class e extends _{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Dn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Xs}next(n){it(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){it(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){it(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ho:(this.currentObservers=null,i.push(n),new L(()=>{this.currentObservers=null,Re(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new _;return n.source=this,n}}return e.create=(t,n)=>new Dn(t,n),e})(),Dn=class extends te{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ho}};var Pt=class extends te{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Lt={now(){return(Lt.delegate||Date).now()},delegate:void 0};var wn=class extends te{constructor(t=1/0,n=1/0,r=Lt){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var bn=class extends L{constructor(t,n){super()}schedule(t,n=0){return this}};var Ft={setInterval(e,t,...n){let{delegate:r}=Ft;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Ft;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Mn=class extends bn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Ft.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Ft.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Re(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var at=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};at.now=Lt.now;var Cn=class extends at{constructor(t,n=at.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var jt=new Cn(Mn),ea=jt;var Pe=new _(e=>e.complete());function _n(e){return e&&v(e.schedule)}function Mo(e){return e[e.length-1]}function xn(e){return v(Mo(e))?e.pop():void 0}function ce(e){return _n(Mo(e))?e.pop():void 0}function ta(e,t){return typeof Mo(e)=="number"?e.pop():t}function ra(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function na(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Le(e){return this instanceof Le?(this.v=e,this):new Le(e)}function oa(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(h){return Promise.resolve(h).then(d,f)}}function a(d,h){r[d]&&(o[d]=function(I){return new Promise(function(O,N){i.push([d,I,O,N])>1||c(d,I)})},h&&(o[d]=h(o[d])))}function c(d,h){try{l(r[d](h))}catch(I){p(i[0][3],I)}}function l(d){d.value instanceof Le?Promise.resolve(d.value.v).then(u,f):p(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function p(d,h){d(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ia(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof na=="function"?na(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var Tn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Nn(e){return v(e?.then)}function Sn(e){return v(e[st])}function kn(e){return Symbol.asyncIterator&&v(e?.[Symbol.asyncIterator])}function Rn(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function dd(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var On=dd();function An(e){return v(e?.[On])}function Pn(e){return oa(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Le(n.read());if(o)return yield Le(void 0);yield yield Le(r)}}finally{n.releaseLock()}})}function Ln(e){return v(e?.getReader)}function S(e){if(e instanceof _)return e;if(e!=null){if(Sn(e))return fd(e);if(Tn(e))return pd(e);if(Nn(e))return hd(e);if(kn(e))return sa(e);if(An(e))return gd(e);if(Ln(e))return md(e)}throw Rn(e)}function fd(e){return new _(t=>{let n=e[st]();if(v(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function pd(e){return new _(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function hd(e){return new _(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,In)})}function gd(e){return new _(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function sa(e){return new _(t=>{yd(e,t).catch(n=>t.error(n))})}function md(e){return sa(Pn(e))}function yd(e,t){var n,r,o,i;return ra(this,void 0,void 0,function*(){try{for(n=ia(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function G(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Fn(e,t=0){return m((n,r)=>{n.subscribe(g(r,o=>G(r,e,()=>r.next(o),t),()=>G(r,e,()=>r.complete(),t),o=>G(r,e,()=>r.error(o),t)))})}function jn(e,t=0){return m((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function aa(e,t){return S(e).pipe(jn(t),Fn(t))}function ca(e,t){return S(e).pipe(jn(t),Fn(t))}function la(e,t){return new _(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ua(e,t){return new _(n=>{let r;return G(n,t,()=>{r=e[On](),G(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>v(r?.return)&&r.return()})}function Vn(e,t){if(!e)throw new Error("Iterable cannot be null");return new _(n=>{G(n,t,()=>{let r=e[Symbol.asyncIterator]();G(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function da(e,t){return Vn(Pn(e),t)}function fa(e,t){if(e!=null){if(Sn(e))return aa(e,t);if(Tn(e))return la(e,t);if(Nn(e))return ca(e,t);if(kn(e))return Vn(e,t);if(An(e))return ua(e,t);if(Ln(e))return da(e,t)}throw Rn(e)}function le(e,t){return t?fa(e,t):S(e)}function vd(...e){let t=ce(e);return le(e,t)}function Id(e,t){let n=v(e)?e:()=>e,r=o=>o.error(n());return new _(t?o=>t.schedule(r,0,o):r)}function Ed(e){return!!e&&(e instanceof _||v(e.lift)&&v(e.subscribe))}var Fe=rt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function pa(e){return e instanceof Date&&!isNaN(e)}function je(e,t){return m((n,r)=>{let o=0;n.subscribe(g(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Dd}=Array;function wd(e,t){return Dd(t)?e(...t):e(t)}function Hn(e){return je(t=>wd(e,t))}var{isArray:bd}=Array,{getPrototypeOf:Md,prototype:Cd,keys:_d}=Object;function Bn(e){if(e.length===1){let t=e[0];if(bd(t))return{args:t,keys:null};if(xd(t)){let n=_d(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function xd(e){return e&&typeof e=="object"&&Md(e)===Cd}function $n(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Td(...e){let t=ce(e),n=xn(e),{args:r,keys:o}=Bn(e);if(r.length===0)return le([],t);let i=new _(Nd(r,t,o?s=>$n(o,s):B));return n?i.pipe(Hn(n)):i}function Nd(e,t,n=B){return r=>{ha(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ha(t,()=>{let l=le(e[c],t),u=!1;l.subscribe(g(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ha(e,t,n){e?G(n,e,t):t()}function ga(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,p=()=>{f&&!c.length&&!l&&t.complete()},d=I=>l<r?h(I):c.push(I),h=I=>{i&&t.next(I),l++;let O=!1;S(n(I,u++)).subscribe(g(t,N=>{o?.(N),i?d(N):t.next(N)},()=>{O=!0},void 0,()=>{if(O)try{for(l--;c.length&&l<r;){let N=c.shift();s?G(t,s,()=>h(N)):h(N)}p()}catch(N){t.error(N)}}))};return e.subscribe(g(t,d,()=>{f=!0,p()})),()=>{a?.()}}function Ve(e,t,n=1/0){return v(t)?Ve((r,o)=>je((i,s)=>t(r,i,o,s))(S(e(r,o))),n):(typeof t=="number"&&(n=t),m((r,o)=>ga(r,o,e,n)))}function Un(e=1/0){return Ve(B,e)}function ma(){return Un(1)}function qn(...e){return ma()(le(e,ce(e)))}function Sd(e){return new _(t=>{S(e()).subscribe(t)})}function kd(...e){let t=xn(e),{args:n,keys:r}=Bn(e),o=new _(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let f=!1;S(n[u]).subscribe(g(i,p=>{f||(f=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!f)&&(l||i.next(r?$n(r,a):a),i.complete())}))}});return t?o.pipe(Hn(t)):o}function ya(e=0,t,n=ea){let r=-1;return t!=null&&(_n(t)?n=t:r=t),new _(o=>{let i=pa(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Rd(...e){let t=ce(e),n=ta(e,1/0),r=e;return r.length?r.length===1?S(r[0]):Un(n)(le(r,t)):Pe}function He(e,t){return m((n,r)=>{let o=0;n.subscribe(g(r,i=>e.call(t,i,o++)&&r.next(i)))})}function va(e){return m((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(g(n,l=>{r=!0,o=l,i||S(e(l)).subscribe(i=g(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Od(e,t=jt){return va(()=>ya(e,t))}function Ia(e){return m((t,n)=>{let r=null,o=!1,i;r=t.subscribe(g(n,void 0,void 0,s=>{i=S(e(s,Ia(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Ea(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(g(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ad(e,t){return v(t)?Ve(e,t,1):Ve(e,1)}function Pd(e,t=jt){return m((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(g(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Vt(e){return m((t,n)=>{let r=!1;t.subscribe(g(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Co(e){return e<=0?()=>Pe:m((t,n)=>{let r=0;t.subscribe(g(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Ld(e,t=B){return e=e??Fd,m((n,r)=>{let o,i=!0;n.subscribe(g(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Fd(e,t){return e===t}function Wn(e=jd){return m((t,n)=>{let r=!1;t.subscribe(g(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function jd(){return new Fe}function Vd(e){return m((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Hd(e,t){let n=arguments.length>=2;return r=>r.pipe(e?He((o,i)=>e(o,i,r)):B,Co(1),n?Vt(t):Wn(()=>new Fe))}function _o(e){return e<=0?()=>Pe:m((t,n)=>{let r=[];t.subscribe(g(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Bd(e,t){let n=arguments.length>=2;return r=>r.pipe(e?He((o,i)=>e(o,i,r)):B,_o(1),n?Vt(t):Wn(()=>new Fe))}function $d(){return m((e,t)=>{let n,r=!1;e.subscribe(g(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function Ud(e,t){return m(Ea(e,t,arguments.length>=2,!0))}function To(e={}){let{connector:t=()=>new te,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,l=0,u=!1,f=!1,p=()=>{a?.unsubscribe(),a=void 0},d=()=>{p(),s=c=void 0,u=f=!1},h=()=>{let I=s;d(),I?.unsubscribe()};return m((I,O)=>{l++,!f&&!u&&p();let N=c=c??t();O.add(()=>{l--,l===0&&!f&&!u&&(a=xo(h,o))}),N.subscribe(O),!s&&l>0&&(s=new ye({next:sn=>N.next(sn),error:sn=>{f=!0,p(),a=xo(d,n,sn),N.error(sn)},complete:()=>{u=!0,p(),a=xo(d,r),N.complete()}}),S(I).subscribe(s))})(i)}}function xo(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new ye({next:()=>{r.unsubscribe(),e()}});return S(t(...n)).subscribe(r)}function qd(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,To({connector:()=>new wn(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Wd(e){return He((t,n)=>e<=n)}function zd(...e){let t=ce(e);return m((n,r)=>{(t?qn(e,n,t):qn(e,n)).subscribe(r)})}function Gd(e,t){return m((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(g(r,c=>{o?.unsubscribe();let l=0,u=i++;S(e(c,u)).subscribe(o=g(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Zd(e){return m((t,n)=>{S(e).subscribe(g(n,()=>n.complete(),At)),!n.closed&&t.subscribe(n)})}function Qd(e,t=!1){return m((n,r)=>{let o=0;n.subscribe(g(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function Yd(e,t,n){let r=v(e)||t||n?{next:e,error:t,complete:n}:e;return r?m((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(g(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):B}var lc="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",x=class extends Error{code;constructor(t,n){super(Kd(t,n)),this.code=t}};function Jd(e){return`NG0${Math.abs(e)}`}function Kd(e,t){return`${Jd(e)}${t?": "+t:""}`}var uc=Symbol("InputSignalNode#UNSET"),Xd=X(K({},gn),{transformFn:void 0,applyValueToInputSignal(e,t){Rt(e,t)}});function dc(e,t){let n=Object.create(Xd);n.value=e,n.transformFn=t?.transform;function r(){if(Nt(n),n.value===uc){let o=null;throw new x(-950,o)}return n.value}return r[z]=n,r}function en(e){return{toString:e}.toString()}var zn="__parameters__";function ef(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function fc(e,t,n){return en(()=>{let r=ef(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let f=c.hasOwnProperty(zn)?c[zn]:Object.defineProperty(c,zn,{value:[]})[zn];for(;f.length<=u;)f.push(null);return(f[u]=f[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Da=globalThis;function k(e){for(let t in e)if(e[t]===k)return t;throw Error("Could not find renamed property on target object.")}function tf(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Q(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Q).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function $o(e,t){return e?t?`${e} ${t}`:e:t||""}var nf=k({__forward_ref__:k});function pc(e){return e.__forward_ref__=pc,e.toString=function(){return Q(this())},e}function $(e){return hc(e)?e():e}function hc(e){return typeof e=="function"&&e.hasOwnProperty(nf)&&e.__forward_ref__===pc}function H(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function BC(e){return{providers:e.providers||[],imports:e.imports||[]}}function kr(e){return wa(e,gc)||wa(e,mc)}function $C(e){return kr(e)!==null}function wa(e,t){return e.hasOwnProperty(t)?e[t]:null}function rf(e){let t=e&&(e[gc]||e[mc]);return t||null}function ba(e){return e&&(e.hasOwnProperty(Ma)||e.hasOwnProperty(of))?e[Ma]:null}var gc=k({\u0275prov:k}),Ma=k({\u0275inj:k}),mc=k({ngInjectableDef:k}),of=k({ngInjectorDef:k}),R=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=H({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function yc(e){return e&&!!e.\u0275providers}var sf=k({\u0275cmp:k}),af=k({\u0275dir:k}),cf=k({\u0275pipe:k}),lf=k({\u0275mod:k}),nr=k({\u0275fac:k}),Ut=k({__NG_ELEMENT_ID__:k}),Ca=k({__NG_ENV_ID__:k});function Gi(e){return typeof e=="string"?e:e==null?"":String(e)}function uf(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Gi(e)}function vc(e,t){throw new x(-200,e)}function Zi(e,t){throw new x(-201,!1)}var b=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(b||{}),Uo;function Ic(){return Uo}function Z(e){let t=Uo;return Uo=e,t}function Ec(e,t,n){let r=kr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&b.Optional)return null;if(t!==void 0)return t;Zi(e,"Injector")}var df={},Be=df,qo="__NG_DI_FLAG__",rr=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?mn:Be,r)}},or="ngTempTokenPath",ff="ngTokenPath",pf=/\n/gm,hf="\u0275",_a="__source";function gf(e,t=b.Default){if(Ot()===void 0)throw new x(-203,!1);if(Ot()===null)return Ec(e,void 0,t);{let n=Ot(),r;return n instanceof rr?r=n.injector:r=n,r.get(e,t&b.Optional?null:void 0,t)}}function _e(e,t=b.Default){return(Ic()||gf)($(e),t)}function C(e,t=b.Default){return _e(e,Rr(t))}function Rr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Wo(e){let t=[];for(let n=0;n<e.length;n++){let r=$(e[n]);if(Array.isArray(r)){if(r.length===0)throw new x(900,!1);let o,i=b.Default;for(let s=0;s<r.length;s++){let a=r[s],c=mf(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(_e(o,i))}else t.push(_e(r))}return t}function Dc(e,t){return e[qo]=t,e.prototype[qo]=t,e}function mf(e){return e[qo]}function yf(e,t,n,r){let o=e[or];throw t[_a]&&o.unshift(t[_a]),e.message=vf(`
`+e.message,o,n,r),e[ff]=o,e[or]=null,e}function vf(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==hf?e.slice(2):e;let o=Q(t);if(Array.isArray(t))o=t.map(Q).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Q(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(pf,`
  `)}`}var If=Dc(fc("Optional"),8);var Ef=Dc(fc("SkipSelf"),4);function Ue(e,t){let n=e.hasOwnProperty(nr);return n?e[nr]:null}function Df(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function wf(e){return e.flat(Number.POSITIVE_INFINITY)}function Qi(e,t){e.forEach(n=>Array.isArray(n)?Qi(n,t):t(n))}function wc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function ir(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function bf(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Mf(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Or(e,t,n){let r=tn(e,t);return r>=0?e[r|1]=n:(r=~r,Mf(e,r,t,n)),r}function No(e,t){let n=tn(e,t);if(n>=0)return e[n|1]}function tn(e,t){return Cf(e,t,1)}function Cf(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var qe={},U=[],sr=new R(""),bc=new R("",-1),Mc=new R(""),ar=class{get(t,n=Be){if(n===Be){let r=new Error(`NullInjectorError: No provider for ${Q(t)}!`);throw r.name="NullInjectorError",r}return n}};function Cc(e,t){let n=e[lf]||null;if(!n&&t===!0)throw new Error(`Type ${Q(e)} does not have '\u0275mod' property.`);return n}function We(e){return e[sf]||null}function _f(e){return e[af]||null}function xf(e){return e[cf]||null}function Tf(e){return{\u0275providers:e}}function Nf(...e){return{\u0275providers:_c(!0,e),\u0275fromNgModule:!0}}function _c(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Qi(t,s=>{let a=s;zo(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&xc(o,i),n}function xc(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Yi(o,i=>{t(i,r)})}}function zo(e,t,n,r){if(e=$(e),!e)return!1;let o=null,i=ba(e),s=!i&&We(e);if(!i&&!s){let c=e.ngModule;if(i=ba(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)zo(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Qi(i.imports,u=>{zo(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&xc(l,t)}if(!a){let l=Ue(o)||(()=>new o);t({provide:o,useFactory:l,deps:U},o),t({provide:Mc,useValue:o,multi:!0},o),t({provide:sr,useValue:()=>_e(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Yi(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Yi(e,t){for(let n of e)yc(n)&&(n=n.\u0275providers),Array.isArray(n)?Yi(n,t):t(n)}var Sf=k({provide:String,useValue:k});function Tc(e){return e!==null&&typeof e=="object"&&Sf in e}function kf(e){return!!(e&&e.useExisting)}function Rf(e){return!!(e&&e.useFactory)}function ht(e){return typeof e=="function"}function Of(e){return!!e.useClass}var Nc=new R(""),Yn={},xa={},So;function Ar(){return So===void 0&&(So=new ar),So}var xe=class{},qt=class extends xe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Zo(t,s=>this.processProvider(s)),this.records.set(bc,ct(void 0,this)),o.has("environment")&&this.records.set(xe,ct(void 0,this));let i=this.records.get(Nc);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Mc,U,b.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?mn:Be,r)}destroy(){Bt(this),this._destroyed=!0;let t=w(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),w(t)}}onDestroy(t){return Bt(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Bt(this);let n=me(this),r=Z(void 0),o;try{return t()}finally{me(n),Z(r)}}get(t,n=Be,r=b.Default){if(Bt(this),t.hasOwnProperty(Ca))return t[Ca](this);r=Rr(r);let o,i=me(this),s=Z(void 0);try{if(!(r&b.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=jf(t)&&kr(t);l&&this.injectableDefInScope(l)?c=ct(Go(t),Yn):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&b.Self?Ar():this.parent;return n=r&b.Optional&&n===Be?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[or]=a[or]||[]).unshift(Q(t)),i)throw a;return yf(a,t,"R3InjectorError",this.source)}else throw a}finally{Z(s),me(i)}}resolveInjectorInitializers(){let t=w(null),n=me(this),r=Z(void 0),o;try{let i=this.get(sr,U,b.Self);for(let s of i)s()}finally{me(n),Z(r),w(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Q(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=$(t);let n=ht(t)?t:$(t&&t.provide),r=Pf(t);if(!ht(t)&&t.multi===!0){let o=this.records.get(n);o||(o=ct(void 0,Yn,!0),o.factory=()=>Wo(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=w(null);try{return n.value===xa?vc(Q(t)):n.value===Yn&&(n.value=xa,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Ff(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{w(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=$(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Go(e){let t=kr(e),n=t!==null?t.factory:Ue(e);if(n!==null)return n;if(e instanceof R)throw new x(204,!1);if(e instanceof Function)return Af(e);throw new x(204,!1)}function Af(e){if(e.length>0)throw new x(204,!1);let n=rf(e);return n!==null?()=>n.factory(e):()=>new e}function Pf(e){if(Tc(e))return ct(void 0,e.useValue);{let t=Sc(e);return ct(t,Yn)}}function Sc(e,t,n){let r;if(ht(e)){let o=$(e);return Ue(o)||Go(o)}else if(Tc(e))r=()=>$(e.useValue);else if(Rf(e))r=()=>e.useFactory(...Wo(e.deps||[]));else if(kf(e))r=(o,i)=>_e($(e.useExisting),i!==void 0&&i&b.Optional?b.Optional:void 0);else{let o=$(e&&(e.useClass||e.provide));if(Lf(e))r=()=>new o(...Wo(e.deps));else return Ue(o)||Go(o)}return r}function Bt(e){if(e.destroyed)throw new x(205,!1)}function ct(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Lf(e){return!!e.deps}function Ff(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function jf(e){return typeof e=="function"||typeof e=="object"&&e instanceof R}function Zo(e,t){for(let n of e)Array.isArray(n)?Zo(n,t):n&&yc(n)?Zo(n.\u0275providers,t):t(n)}function kc(e,t){let n;e instanceof qt?(Bt(e),n=e):n=new rr(e);let r,o=me(n),i=Z(void 0);try{return t()}finally{me(o),Z(i)}}function Rc(){return Ic()!==void 0||Ot()!=null}function Ji(e){if(!Rc())throw new x(-203,!1)}function Vf(e){return typeof e=="function"}var Ee=0,E=1,y=2,V=3,ae=4,J=5,Wt=6,cr=7,q=8,gt=9,Te=10,A=11,zt=12,Ta=13,Dt=14,re=15,ze=16,lt=17,ve=18,Pr=19,Oc=20,Me=21,ko=22,Ge=23,ne=24,ft=25,j=26,Ac=1;var Ze=7,lr=8,mt=9,Y=10;function Ce(e){return Array.isArray(e)&&typeof e[Ac]=="object"}function De(e){return Array.isArray(e)&&e[Ac]===!0}function Ki(e){return(e.flags&4)!==0}function wt(e){return e.componentOffset>-1}function Lr(e){return(e.flags&1)===1}function de(e){return!!e.template}function ur(e){return(e[y]&512)!==0}function bt(e){return(e[y]&256)===256}var Qo=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Pc(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var UC=(()=>{let e=()=>Lc;return e.ngInherit=!0,e})();function Lc(e){return e.type.prototype.ngOnChanges&&(e.setInput=Bf),Hf}function Hf(){let e=jc(this),t=e?.current;if(t){let n=e.previous;if(n===qe)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Bf(e,t,n,r,o){let i=this.declaredInputs[r],s=jc(e)||$f(e,{previous:qe,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Qo(l&&l.currentValue,n,c===qe),Pc(e,t,o,n)}var Fc="__ngSimpleChanges__";function jc(e){return e[Fc]||null}function $f(e,t){return e[Fc]=t}var Na=null;var T=function(e,t=null,n){Na?.(e,t,n)},Vc="svg",Uf="math";function fe(e){for(;Array.isArray(e);)e=e[Ee];return e}function Hc(e,t){return fe(t[e])}function he(e,t){return fe(t[e.index])}function Xi(e,t){return e.data[t]}function Bc(e,t){return e[t]}function $c(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function pe(e,t){let n=t[e];return Ce(n)?n:n[Ee]}function qf(e){return(e[y]&4)===4}function es(e){return(e[y]&128)===128}function Wf(e){return De(e[V])}function yt(e,t){return t==null?null:e[t]}function Uc(e){e[lt]=0}function qc(e){e[y]&1024||(e[y]|=1024,es(e)&&Mt(e))}function zf(e,t){for(;e>0;)t=t[Dt],e--;return t}function Fr(e){return!!(e[y]&9216||e[ne]?.dirty)}function Yo(e){e[Te].changeDetectionScheduler?.notify(8),e[y]&64&&(e[y]|=1024),Fr(e)&&Mt(e)}function Mt(e){e[Te].changeDetectionScheduler?.notify(0);let t=Qe(e);for(;t!==null&&!(t[y]&8192||(t[y]|=8192,!es(t)));)t=Qe(t)}function Wc(e,t){if(bt(e))throw new x(911,!1);e[Me]===null&&(e[Me]=[]),e[Me].push(t)}function Gf(e,t){if(e[Me]===null)return;let n=e[Me].indexOf(t);n!==-1&&e[Me].splice(n,1)}function Qe(e){let t=e[V];return De(t)?t[V]:t}function ts(e){return e[cr]??=[]}function ns(e){return e.cleanup??=[]}function Zf(e,t,n,r){let o=ts(t);o.push(n),e.firstCreatePass&&ns(e).push(r,o.length-1)}var D={lFrame:Kc(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Jo=!1;function Qf(){return D.lFrame.elementDepthCount}function Yf(){D.lFrame.elementDepthCount++}function Jf(){D.lFrame.elementDepthCount--}function rs(){return D.bindingsEnabled}function zc(){return D.skipHydrationRootTNode!==null}function Kf(e){return D.skipHydrationRootTNode===e}function Xf(){D.skipHydrationRootTNode=null}function M(){return D.lFrame.lView}function P(){return D.lFrame.tView}function qC(e){return D.lFrame.contextLView=e,e[q]}function WC(e){return D.lFrame.contextLView=null,e}function W(){let e=Gc();for(;e!==null&&e.type===64;)e=e.parent;return e}function Gc(){return D.lFrame.currentTNode}function ep(){let e=D.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Se(e,t){let n=D.lFrame;n.currentTNode=e,n.isParent=t}function os(){return D.lFrame.isParent}function is(){D.lFrame.isParent=!1}function tp(){return D.lFrame.contextLView}function Zc(){return Jo}function dr(e){let t=Jo;return Jo=e,t}function np(){let e=D.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function rp(e){return D.lFrame.bindingIndex=e}function nn(){return D.lFrame.bindingIndex++}function Qc(e){let t=D.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function op(){return D.lFrame.inI18n}function ip(e,t){let n=D.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ko(t)}function sp(){return D.lFrame.currentDirectiveIndex}function Ko(e){D.lFrame.currentDirectiveIndex=e}function ap(e){let t=D.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function ss(){return D.lFrame.currentQueryIndex}function jr(e){D.lFrame.currentQueryIndex=e}function cp(e){let t=e[E];return t.type===2?t.declTNode:t.type===1?e[J]:null}function Yc(e,t,n){if(n&b.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&b.Host);)if(o=cp(i),o===null||(i=i[Dt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=D.lFrame=Jc();return r.currentTNode=t,r.lView=e,!0}function as(e){let t=Jc(),n=e[E];D.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Jc(){let e=D.lFrame,t=e===null?null:e.child;return t===null?Kc(e):t}function Kc(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Xc(){let e=D.lFrame;return D.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var el=Xc;function cs(){let e=Xc();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function lp(e){return(D.lFrame.contextLView=zf(e,D.lFrame.contextLView))[q]}function tt(){return D.lFrame.selectedIndex}function Ye(e){D.lFrame.selectedIndex=e}function ls(){let e=D.lFrame;return Xi(e.tView,e.selectedIndex)}function zC(){D.lFrame.currentNamespace=Vc}function GC(){up()}function up(){D.lFrame.currentNamespace=null}function dp(){return D.lFrame.currentNamespace}var tl=!0;function Vr(){return tl}function Hr(e){tl=e}function fp(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Lc(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function us(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Jn(e,t,n){nl(e,t,3,n)}function Kn(e,t,n,r){(e[y]&3)===n&&nl(e,t,n,r)}function Ro(e,t){let n=e[y];(n&3)===t&&(n&=16383,n+=1,e[y]=n)}function nl(e,t,n,r){let o=r!==void 0?e[lt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[lt]+=65536),(a<i||i==-1)&&(pp(e,n,t,c),e[lt]=(e[lt]&**********)+c+2),c++}function Sa(e,t){T(4,e,t);let n=w(null);try{t.call(e)}finally{w(n),T(5,e,t)}}function pp(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[y]>>14<e[lt]>>16&&(e[y]&3)===t&&(e[y]+=16384,Sa(a,i)):Sa(a,i)}var pt=-1,Je=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function hp(e){return(e.flags&8)!==0}function gp(e){return(e.flags&16)!==0}function mp(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];yp(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function rl(e){return e===3||e===4||e===6}function yp(e){return e.charCodeAt(0)===64}function vt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?ka(e,n,o,null,t[++r]):ka(e,n,o,null,null))}}return e}function ka(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function ol(e){return e!==pt}function fr(e){return e&32767}function vp(e){return e>>16}function pr(e,t){let n=vp(e),r=t;for(;n>0;)r=r[Dt],n--;return r}var Xo=!0;function hr(e){let t=Xo;return Xo=e,t}var Ip=256,il=Ip-1,sl=5,Ep=0,ue={};function Dp(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Ut)&&(r=n[Ut]),r==null&&(r=n[Ut]=Ep++);let o=r&il,i=1<<o;t.data[e+(o>>sl)]|=i}function gr(e,t){let n=al(e,t);if(n!==-1)return n;let r=t[E];r.firstCreatePass&&(e.injectorIndex=t.length,Oo(r.data,e),Oo(t,null),Oo(r.blueprint,null));let o=ds(e,t),i=e.injectorIndex;if(ol(o)){let s=fr(o),a=pr(o,t),c=a[E].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Oo(e,t){e.push(0,0,0,0,0,0,0,0,t)}function al(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function ds(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=fl(o),r===null)return pt;if(n++,o=o[Dt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return pt}function ei(e,t,n){Dp(e,t,n)}function wp(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(rl(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function cl(e,t,n){if(n&b.Optional||e!==void 0)return e;Zi(t,"NodeInjector")}function ll(e,t,n,r){if(n&b.Optional&&r===void 0&&(r=null),(n&(b.Self|b.Host))===0){let o=e[gt],i=Z(void 0);try{return o?o.get(t,r,n&b.Optional):Ec(t,r,n&b.Optional)}finally{Z(i)}}return cl(r,t,n)}function ul(e,t,n,r=b.Default,o){if(e!==null){if(t[y]&2048&&!(r&b.Self)){let s=_p(e,t,n,r,ue);if(s!==ue)return s}let i=dl(e,t,n,r,ue);if(i!==ue)return i}return ll(t,n,r,o)}function dl(e,t,n,r,o){let i=Mp(n);if(typeof i=="function"){if(!Yc(t,e,r))return r&b.Host?cl(o,n,r):ll(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&b.Optional))Zi(n);else return s}finally{el()}}else if(typeof i=="number"){let s=null,a=al(e,t),c=pt,l=r&b.Host?t[re][J]:null;for((a===-1||r&b.SkipSelf)&&(c=a===-1?ds(e,t):t[a+8],c===pt||!Oa(r,!1)?a=-1:(s=t[E],a=fr(c),t=pr(c,t)));a!==-1;){let u=t[E];if(Ra(i,a,u.data)){let f=bp(a,t,n,s,r,l);if(f!==ue)return f}c=t[a+8],c!==pt&&Oa(r,t[E].data[a+8]===l)&&Ra(i,a,t)?(s=u,a=fr(c),t=pr(c,t)):a=-1}}return o}function bp(e,t,n,r,o,i){let s=t[E],a=s.data[e+8],c=r==null?wt(a)&&Xo:r!=s&&(a.type&3)!==0,l=o&b.Host&&i===a,u=Xn(a,s,n,c,l);return u!==null?Gt(t,s,u,a,o):ue}function Xn(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,p=o?a+u:l;for(let d=f;d<p;d++){let h=s[d];if(d<c&&n===h||d>=c&&h.type===n)return d}if(o){let d=s[c];if(d&&de(d)&&d.type===n)return c}return null}function Gt(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof Je){let a=i;a.resolving&&vc(uf(s[n]));let c=hr(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?Z(a.injectImpl):null,f=Yc(e,r,b.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&fp(n,s[n],t)}finally{u!==null&&Z(u),hr(c),a.resolving=!1,el()}}return i}function Mp(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Ut)?e[Ut]:void 0;return typeof t=="number"?t>=0?t&il:Cp:t}function Ra(e,t,n){let r=1<<e;return!!(n[t+(e>>sl)]&r)}function Oa(e,t){return!(e&b.Self)&&!(e&b.Host&&t)}var $e=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return ul(this._tNode,this._lView,t,Rr(r),n)}};function Cp(){return new $e(W(),M())}function ZC(e){return en(()=>{let t=e.prototype.constructor,n=t[nr]||ti(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[nr]||ti(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ti(e){return hc(e)?()=>{let t=ti($(e));return t&&t()}:Ue(e)}function _p(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[y]&2048&&!ur(s);){let a=dl(i,s,n,r|b.Self,ue);if(a!==ue)return a;let c=i.parent;if(!c){let l=s[Oc];if(l){let u=l.get(n,ue,r);if(u!==ue)return u}c=fl(s),s=s[Dt]}i=c}return o}function fl(e){let t=e[E],n=t.type;return n===2?t.declTNode:n===1?e[J]:null}function xp(e){return wp(W(),e)}function Aa(e,t=null,n=null,r){let o=pl(e,t,n,r);return o.resolveInjectorInitializers(),o}function pl(e,t=null,n=null,r,o=new Set){let i=[n||U,Nf(e)];return r=r||(typeof e=="object"?void 0:Q(e)),new qt(i,t||Ar(),r||null,o)}var Ne=class e{static THROW_IF_NOT_FOUND=Be;static NULL=new ar;static create(t,n){if(Array.isArray(t))return Aa({name:""},n,t,"");{let r=t.name??"";return Aa({name:r},t.parent,t.providers,r)}}static \u0275prov=H({token:e,providedIn:"any",factory:()=>_e(bc)});static __NG_ELEMENT_ID__=-1};var Pa=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>xp(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},Tp=new R("");Tp.__NG_ELEMENT_ID__=e=>{let t=W();if(t===null)throw new x(204,!1);if(t.type&2)return t.value;if(e&b.Optional)return null;throw new x(204,!1)};var hl=!1,Br=(()=>{class e{static __NG_ELEMENT_ID__=Np;static __NG_ENV_ID__=n=>n}return e})(),mr=class extends Br{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return bt(n)?(t(),()=>{}):(Wc(n,t),()=>Gf(n,t))}};function Np(){return new mr(M())}var Ke=class{},fs=new R("",{providedIn:"root",factory:()=>!1});var gl=new R(""),ml=new R(""),$r=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Pt(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=H({token:e,providedIn:"root",factory:()=>new e})}return e})();var ni=class extends te{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Rc()&&(this.destroyRef=C(Br,{optional:!0})??void 0,this.pendingTasks=C($r,{optional:!0})??void 0)}emit(t){let n=w(null);try{super.next(t)}finally{w(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof L&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},be=ni;function Zt(...e){}function yl(e){let t,n;function r(){e=Zt;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function La(e){return queueMicrotask(()=>e()),()=>{e=Zt}}var ps="isAngularZone",yr=ps+"_ID",Sp=0,ee=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new be(!1);onMicrotaskEmpty=new be(!1);onStable=new be(!1);onError=new be(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=hl}=t;if(typeof Zone>"u")throw new x(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Op(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ps)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new x(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new x(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,kp,Zt,Zt);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},kp={};function hs(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Rp(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){yl(()=>{e.callbackScheduled=!1,ri(e),e.isCheckStableRunning=!0,hs(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),ri(e)}function Op(e){let t=()=>{Rp(e)},n=Sp++;e._inner=e._inner.fork({name:"angular",properties:{[ps]:!0,[yr]:n,[yr+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Ap(c))return r.invokeTask(i,s,a,c);try{return Fa(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),ja(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Fa(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Pp(c)&&t(),ja(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ri(e),hs(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ri(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Fa(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function ja(e){e._nesting--,hs(e)}var oi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new be;onMicrotaskEmpty=new be;onStable=new be;onError=new be;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Ap(e){return vl(e,"__ignore_ng_zone__")}function Pp(e){return vl(e,"__scheduler_tick__")}function vl(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Xe=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Lp=new R("",{providedIn:"root",factory:()=>{let e=C(ee),t=C(Xe);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Va(e,t){return dc(e,t)}function Fp(e){return dc(uc,e)}var QC=(Va.required=Fp,Va);function jp(){return Ct(W(),M())}function Ct(e,t){return new Ur(he(e,t))}var Ur=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=jp}return e})();function Il(e){return e instanceof Ur?e.nativeElement:e}function YC(e){return typeof e=="function"&&e[z]!==void 0}function Vp(e,t){let n=lo(e,t?.equal),r=n[z];return n.set=o=>Rt(r,o),n.update=o=>uo(r,o),n.asReadonly=Hp.bind(n),n}function Hp(){let e=this[z];if(e.readonlyFn===void 0){let t=()=>this();t[z]=e,e.readonlyFn=t}return e.readonlyFn}function Bp(){return this._results[Symbol.iterator]()}var ii=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new te}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=wf(t);(this._changesDetected=!Df(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Bp};function El(e){return(e.flags&128)===128}var Dl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Dl||{}),wl=new Map,$p=0;function Up(){return $p++}function qp(e){wl.set(e[Pr],e)}function si(e){wl.delete(e[Pr])}var Ha="__ngContext__";function _t(e,t){Ce(t)?(e[Ha]=t[Pr],qp(t)):e[Ha]=t}function bl(e){return Cl(e[zt])}function Ml(e){return Cl(e[ae])}function Cl(e){for(;e!==null&&!De(e);)e=e[ae];return e}var ai;function JC(e){ai=e}function Wp(){if(ai!==void 0)return ai;if(typeof document<"u")return document;throw new x(210,!1)}var KC=new R("",{providedIn:"root",factory:()=>zp}),zp="ng",Gp=new R(""),XC=new R("",{providedIn:"platform",factory:()=>"unknown"});var e_=new R(""),t_=new R("",{providedIn:"root",factory:()=>Wp().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Zp="h",Qp="b";var _l=!1,Yp=new R("",{providedIn:"root",factory:()=>_l});var gs=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(gs||{}),qr=new R(""),Ba=new Set;function rn(e){Ba.has(e)||(Ba.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var ms=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Jp}return e})();function Jp(){return new ms(M(),W())}var ut=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(ut||{}),xl=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=H({token:e,providedIn:"root",factory:()=>new e})}return e})(),Kp=[ut.EarlyRead,ut.Write,ut.MixedReadWrite,ut.Read],Xp=(()=>{class e{ngZone=C(ee);scheduler=C(Ke);errorHandler=C(Xe,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){C(qr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&T(16),this.executing=!0;for(let r of Kp)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&T(17)}register(n){let{view:r}=n;r!==void 0?((r[ft]??=[]).push(n),Mt(r),r[y]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(gs.AFTER_NEXT_RENDER,n):n()}static \u0275prov=H({token:e,providedIn:"root",factory:()=>new e})}return e})(),ci=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[ft];t&&(this.view[ft]=t.filter(n=>n!==this))}};function eh(e,t){!t?.injector&&Ji(eh);let n=t?.injector??C(Ne);return rn("NgAfterRender"),Tl(e,n,t,!1)}function th(e,t){!t?.injector&&Ji(th);let n=t?.injector??C(Ne);return rn("NgAfterNextRender"),Tl(e,n,t,!0)}function nh(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Tl(e,t,n,r){let o=t.get(xl);o.impl??=t.get(Xp);let i=t.get(qr,null,{optional:!0}),s=n?.phase??ut.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Br):null,c=t.get(ms,null,{optional:!0}),l=new ci(o.impl,nh(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var rh=(e,t,n,r)=>{};function oh(e,t,n,r){rh(e,t,n,r)}var ih=()=>null;function Nl(e,t,n=!1){return ih(e,t,n)}function Sl(e,t){let n=e.contentQueries;if(n!==null){let r=w(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];jr(i),a.contentQueries(2,t[s],s)}}}finally{w(r)}}}function li(e,t,n){jr(0);let r=w(null);try{t(e,n)}finally{w(r)}}function ys(e,t,n){if(Ki(t)){let r=w(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{w(r)}}}var Qt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Qt||{}),Gn;function sh(){if(Gn===void 0&&(Gn=null,Da.trustedTypes))try{Gn=Da.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Gn}function Wr(e){return sh()?.createHTML(e)||e}var Ie=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${lc})`}},ui=class extends Ie{getTypeName(){return"HTML"}},di=class extends Ie{getTypeName(){return"Style"}},fi=class extends Ie{getTypeName(){return"Script"}},pi=class extends Ie{getTypeName(){return"URL"}},hi=class extends Ie{getTypeName(){return"ResourceURL"}};function kl(e){return e instanceof Ie?e.changingThisBreaksApplicationSecurity:e}function n_(e,t){let n=ah(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${lc})`)}return n===t}function ah(e){return e instanceof Ie&&e.getTypeName()||null}function r_(e){return new ui(e)}function o_(e){return new di(e)}function i_(e){return new fi(e)}function s_(e){return new pi(e)}function a_(e){return new hi(e)}function ch(e){let t=new mi(e);return lh()?new gi(t):t}var gi=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Wr(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},mi=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Wr(t),n}};function lh(){try{return!!new window.DOMParser().parseFromString(Wr(""),"text/html")}catch{return!1}}var uh=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function dh(e){return e=String(e),e.match(uh)?e:"unsafe:"+e}function we(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function on(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Rl=we("area,br,col,hr,img,wbr"),Ol=we("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Al=we("rp,rt"),fh=on(Al,Ol),ph=on(Ol,we("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),hh=on(Al,we("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),$a=on(Rl,ph,hh,fh),Pl=we("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),gh=we("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),mh=we("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),yh=on(Pl,gh,mh),vh=we("script,style,template"),yi=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Dh(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Eh(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Ua(t).toLowerCase();if(!$a.hasOwnProperty(n))return this.sanitizedSomething=!0,!vh.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!yh.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Pl[a]&&(c=dh(c)),this.buf.push(" ",s,'="',qa(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Ua(t).toLowerCase();$a.hasOwnProperty(n)&&!Rl.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(qa(t))}};function Ih(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Eh(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Ll(t);return t}function Dh(e){let t=e.firstChild;if(t&&Ih(e,t))throw Ll(t);return t}function Ua(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Ll(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var wh=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,bh=/([^\#-~ |!])/g;function qa(e){return e.replace(/&/g,"&amp;").replace(wh,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(bh,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Zn;function c_(e,t){let n=null;try{Zn=Zn||ch(e);let r=t?String(t):"";n=Zn.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Zn.getInertBodyElement(r)}while(r!==i);let a=new yi().sanitizeChildren(Wa(n)||n);return Wr(a)}finally{if(n){let r=Wa(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Wa(e){return"content"in e&&Mh(e)?e.content:null}function Mh(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Ch=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Ch||{});var _h=/^>|^->|<!--|-->|--!>|<!-$/g,xh=/(<|>)/g,Th="\u200B$1\u200B";function Nh(e){return e.replace(_h,t=>t.replace(xh,Th))}function Fl(e){return e instanceof Function?e():e}function Sh(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var jl="ng-template";function kh(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Sh(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(vs(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function vs(e){return e.type===4&&e.value!==jl}function Rh(e,t,n){let r=e.type===4&&!n?jl:e.value;return t===r}function Oh(e,t,n){let r=4,o=e.attrs,i=o!==null?Lh(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!ie(r)&&!ie(c))return!1;if(s&&ie(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Rh(e,c,n)||c===""&&t.length===1){if(ie(r))return!1;s=!0}}else if(r&8){if(o===null||!kh(e,o,c,n)){if(ie(r))return!1;s=!0}}else{let l=t[++a],u=Ah(c,o,vs(e),n);if(u===-1){if(ie(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(ie(r))return!1;s=!0}}}}return ie(r)||s}function ie(e){return(e&1)===0}function Ah(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Fh(t,e)}function Vl(e,t,n=!1){for(let r=0;r<t.length;r++)if(Oh(e,t[r],n))return!0;return!1}function Ph(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function Lh(e){for(let t=0;t<e.length;t++){let n=e[t];if(rl(n))return t}return e.length}function Fh(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function jh(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function za(e,t){return e?":not("+t.trim()+")":t}function Vh(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ie(s)&&(t+=za(i,o),o=""),r=s,i=i||!ie(r);n++}return o!==""&&(t+=za(i,o)),t}function Hh(e){return e.map(Vh).join(",")}function Bh(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ie(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var ge={};function $h(e,t){return e.createText(t)}function Uh(e,t,n){e.setValue(t,n)}function qh(e,t){return e.createComment(Nh(t))}function Hl(e,t,n){return e.createElement(t,n)}function vr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Bl(e,t,n){e.appendChild(t,n)}function Ga(e,t,n,r,o){r!==null?vr(e,t,n,r,o):Bl(e,t,n)}function Wh(e,t,n){e.removeChild(null,t,n)}function zh(e,t,n){e.setAttribute(t,"style",n)}function Gh(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function $l(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&mp(e,t,r),o!==null&&Gh(e,t,o),i!==null&&zh(e,t,i)}function Is(e,t,n,r,o,i,s,a,c,l,u){let f=j+r,p=f+o,d=Zh(f,p),h=typeof l=="function"?l():l;return d[E]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function Zh(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:ge);return n}function Qh(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Is(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Es(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[Ee]=o,f[y]=r|4|128|8|64|1024,(l!==null||e&&e[y]&2048)&&(f[y]|=2048),Uc(f),f[V]=f[Dt]=e,f[q]=n,f[Te]=s||e&&e[Te],f[A]=a||e&&e[A],f[gt]=c||e&&e[gt]||null,f[J]=i,f[Pr]=Up(),f[Wt]=u,f[Oc]=l,f[re]=t.type==2?e[re]:f,f}function Yh(e,t,n){let r=he(t,e),o=Qh(n),i=e[Te].rendererFactory,s=Ds(e,Es(e,o,null,Ul(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Ul(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function ql(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ds(e,t){return e[zt]?e[Ta][ae]=t:e[zt]=t,e[Ta]=t,t}function l_(e=1){Wl(P(),M(),tt()+e,!1)}function Wl(e,t,n,r){if(!r)if((t[y]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Jn(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Kn(t,i,0,n)}Ye(n)}var zr=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(zr||{});function vi(e,t,n,r){let o=w(null);try{let[i,s,a]=e.inputs[n],c=null;(s&zr.SignalBased)!==0&&(c=t[i][z]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Pc(t,c,i,r)}finally{w(o)}}function zl(e,t,n,r,o){let i=tt(),s=r&2;try{Ye(-1),s&&t.length>j&&Wl(e,t,j,!1),T(s?2:0,o),n(r,o)}finally{Ye(i),T(s?3:1,o)}}function Gr(e,t,n){ng(e,t,n),(n.flags&64)===64&&rg(e,t,n)}function ws(e,t,n=he){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Jh(e,t,n,r){let i=r.get(Yp,_l)||n===Qt.ShadowDom,s=e.selectRootElement(t,i);return Kh(s),s}function Kh(e){Xh(e)}var Xh=()=>null;function eg(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Gl(e,t,n,r,o,i,s,a){if(!a&&Ms(t,e,n,r,o)){wt(t)&&tg(n,t.index);return}if(t.type&3){let c=he(t,n);r=eg(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function tg(e,t){let n=pe(t,e);n[y]&16||(n[y]|=64)}function ng(e,t,n){let r=n.directiveStart,o=n.directiveEnd;wt(n)&&Yh(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||gr(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Gt(t,e,s,n);if(_t(c,t),i!==null&&ag(t,s-r,c,a,n,i),de(a)){let l=pe(n.index,t);l[q]=Gt(t,e,s,n)}}}function rg(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=sp();try{Ye(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];Ko(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&og(c,l)}}finally{Ye(-1),Ko(s)}}function og(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function bs(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Vl(t,i.selectors,!1)&&(r??=[],de(i)?r.unshift(i):r.push(i))}return r}function ig(e,t,n,r,o,i){let s=he(e,t);sg(t[A],s,i,e.value,n,r,o)}function sg(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Gi(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function ag(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];vi(r,n,c,l)}}function cg(e,t){let n=e[gt],r=n?n.get(Xe,null):null;r&&r.handleError(t)}function Ms(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];vi(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];vi(u,l,r,o),a=!0}return a}function lg(e,t){let n=pe(t,e),r=n[E];ug(r,n);let o=n[Ee];o!==null&&n[Wt]===null&&(n[Wt]=Nl(o,n[gt])),T(18),Cs(r,n,n[q]),T(19,n[q])}function ug(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Cs(e,t,n){as(t);try{let r=e.viewQuery;r!==null&&li(1,r,n);let o=e.template;o!==null&&zl(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ve]?.finishViewCreation(e),e.staticContentQueries&&Sl(e,t),e.staticViewQueries&&li(2,e.viewQuery,n);let i=e.components;i!==null&&dg(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[y]&=-5,cs()}}function dg(e,t){for(let n=0;n<t.length;n++)lg(e,t[n])}function _s(e,t,n,r){let o=w(null);try{let i=t.tView,a=e[y]&4096?4096:16,c=Es(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[ze]=l;let u=e[ve];return u!==null&&(c[ve]=u.createEmbeddedView(i)),Cs(i,c,n),c}finally{w(o)}}function Ir(e,t){return!t||t.firstChild===null||El(e)}var fg;function xs(e,t){return fg(e,t)}var Ii=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Ii||{});function Ts(e){return(e.flags&32)===32}function dt(e,t,n,r,o){if(r!=null){let i,s=!1;De(r)?i=r:Ce(r)&&(s=!0,r=r[Ee]);let a=fe(r);e===0&&n!==null?o==null?Bl(t,n,a):vr(t,n,a,o||null,!0):e===1&&n!==null?vr(t,n,a,o||null,!0):e===2?Wh(t,a,s):e===3&&t.destroyNode(a),i!=null&&wg(t,e,i,n,o)}}function pg(e,t){Zl(e,t),t[Ee]=null,t[J]=null}function hg(e,t,n,r,o,i){r[Ee]=o,r[J]=t,Qr(e,r,n,1,o,i)}function Zl(e,t){t[Te].changeDetectionScheduler?.notify(9),Qr(e,t,t[A],2,null,null)}function gg(e){let t=e[zt];if(!t)return Ao(e[E],e);for(;t;){let n=null;if(Ce(t))n=t[zt];else{let r=t[Y];r&&(n=r)}if(!n){for(;t&&!t[ae]&&t!==e;)Ce(t)&&Ao(t[E],t),t=t[V];t===null&&(t=e),Ce(t)&&Ao(t[E],t),n=t&&t[ae]}t=n}}function Ns(e,t){let n=e[mt],r=n.indexOf(t);n.splice(r,1)}function Ss(e,t){if(bt(t))return;let n=t[A];n.destroyNode&&Qr(e,t,n,3,null,null),gg(t)}function Ao(e,t){if(bt(t))return;let n=w(null);try{t[y]&=-129,t[y]|=256,t[ne]&&kt(t[ne]),yg(e,t),mg(e,t),t[E].type===1&&t[A].destroy();let r=t[ze];if(r!==null&&De(t[V])){r!==t[V]&&Ns(r,t);let o=t[ve];o!==null&&o.detachView(e)}si(t)}finally{w(n)}}function mg(e,t){let n=e.cleanup,r=t[cr];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[cr]=null);let o=t[Me];if(o!==null){t[Me]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Ge];if(i!==null){t[Ge]=null;for(let s of i)s.destroy()}}function yg(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Je)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];T(4,a,c);try{c.call(a)}finally{T(5,a,c)}}else{T(4,o,i);try{i.call(o)}finally{T(5,o,i)}}}}}function Ql(e,t,n){return vg(e,t.parent,n)}function vg(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Ee];if(wt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Qt.None||o===Qt.Emulated)return null}return he(r,n)}function Yl(e,t,n){return Eg(e,t,n)}function Ig(e,t,n){return e.type&40?he(e,n):null}var Eg=Ig,Za;function Zr(e,t,n,r){let o=Ql(e,r,t),i=t[A],s=r.parent||t[J],a=Yl(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Ga(i,o,n[c],a,!1);else Ga(i,o,n,a,!1);Za!==void 0&&Za(i,r,t,n,o)}function $t(e,t){if(t!==null){let n=t.type;if(n&3)return he(t,e);if(n&4)return Ei(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return $t(e,r);{let o=e[t.index];return De(o)?Ei(-1,o):fe(o)}}else{if(n&128)return $t(e,t.next);if(n&32)return xs(t,e)()||fe(e[t.index]);{let r=Jl(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Qe(e[re]);return $t(o,r)}else return $t(e,t.next)}}}return null}function Jl(e,t){if(t!==null){let r=e[re][J],o=t.projection;return r.projection[o]}return null}function Ei(e,t){let n=Y+e+1;if(n<t.length){let r=t[n],o=r[E].firstChild;if(o!==null)return $t(r,o)}return t[Ze]}function ks(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&_t(fe(a),r),n.flags|=2),!Ts(n))if(c&8)ks(e,t,n.child,r,o,i,!1),dt(t,e,o,a,i);else if(c&32){let l=xs(n,r),u;for(;u=l();)dt(t,e,o,u,i);dt(t,e,o,a,i)}else c&16?Kl(e,t,r,n,o,i):dt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Qr(e,t,n,r,o,i){ks(n,r,e.firstChild,t,o,i,!1)}function Dg(e,t,n){let r=t[A],o=Ql(e,n,t),i=n.parent||t[J],s=Yl(i,n,t);Kl(r,0,t,n,o,s)}function Kl(e,t,n,r,o,i){let s=n[re],c=s[J].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];dt(t,e,o,u,i)}else{let l=c,u=s[V];El(r)&&(l.flags|=128),ks(e,t,l,u,o,i,!0)}}function wg(e,t,n,r,o){let i=n[Ze],s=fe(n);i!==s&&dt(t,e,r,i,o);for(let a=Y;a<n.length;a++){let c=n[a];Qr(c[E],c,e,t,r,i)}}function bg(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Ii.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Ii.Important),e.setStyle(n,r,o,i))}}function Er(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(fe(i)),De(i)&&Mg(i,r);let s=n.type;if(s&8)Er(e,t,n.child,r);else if(s&32){let a=xs(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Jl(t,n);if(Array.isArray(a))r.push(...a);else{let c=Qe(t[re]);Er(c[E],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Mg(e,t){for(let n=Y;n<e.length;n++){let r=e[n],o=r[E].firstChild;o!==null&&Er(r[E],r,o,t)}e[Ze]!==e[Ee]&&t.push(e[Ze])}function Xl(e){if(e[ft]!==null){for(let t of e[ft])t.impl.addSequence(t);e[ft].length=0}}var eu=[];function Cg(e){return e[ne]??_g(e)}function _g(e){let t=eu.pop()??Object.create(Tg);return t.lView=e,t}function xg(e){e.lView[ne]!==e&&(e.lView=null,eu.push(e))}var Tg=X(K({},nt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Mt(e.lView)},consumerOnSignalRead(){this.lView[ne]=this}});function Ng(e){let t=e[ne]??Object.create(Sg);return t.lView=e,t}var Sg=X(K({},nt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Qe(e.lView);for(;t&&!tu(t[E]);)t=Qe(t);t&&qc(t)},consumerOnSignalRead(){this.lView[ne]=this}});function tu(e){return e.type!==2}function nu(e){if(e[Ge]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Ge])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[y]&8192)}}var kg=100;function ru(e,t=!0,n=0){let o=e[Te].rendererFactory,i=!1;i||o.begin?.();try{Rg(e,n)}catch(s){throw t&&cg(e,s),s}finally{i||o.end?.()}}function Rg(e,t){let n=Zc();try{dr(!0),Di(e,t);let r=0;for(;Fr(e);){if(r===kg)throw new x(103,!1);r++,Di(e,1)}}finally{dr(n)}}function Og(e,t,n,r){if(bt(t))return;let o=t[y],i=!1,s=!1;as(t);let a=!0,c=null,l=null;i||(tu(e)?(l=Cg(t),c=St(l)):oo()===null?(a=!1,l=Ng(t),c=St(l)):t[ne]&&(kt(t[ne]),t[ne]=null));try{Uc(t),rp(e.bindingStartIndex),n!==null&&zl(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&Jn(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Kn(t,d,0,null),Ro(t,0)}if(s||Ag(t),nu(t),ou(t,0),e.contentQueries!==null&&Sl(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&Jn(t,d)}else{let d=e.contentHooks;d!==null&&Kn(t,d,1),Ro(t,1)}Lg(e,t);let f=e.components;f!==null&&su(t,f,0);let p=e.viewQuery;if(p!==null&&li(2,p,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&Jn(t,d)}else{let d=e.viewHooks;d!==null&&Kn(t,d,2),Ro(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ko]){for(let d of t[ko])d();t[ko]=null}i||(Xl(t),t[y]&=-73)}catch(u){throw i||Mt(t),u}finally{l!==null&&(un(l,c),a&&xg(l)),cs()}}function ou(e,t){for(let n=bl(e);n!==null;n=Ml(n))for(let r=Y;r<n.length;r++){let o=n[r];iu(o,t)}}function Ag(e){for(let t=bl(e);t!==null;t=Ml(t)){if(!(t[y]&2))continue;let n=t[mt];for(let r=0;r<n.length;r++){let o=n[r];qc(o)}}}function Pg(e,t,n){T(18);let r=pe(t,e);iu(r,n),T(19,r[q])}function iu(e,t){es(e)&&Di(e,t)}function Di(e,t){let r=e[E],o=e[y],i=e[ne],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&dn(i)),s||=!1,i&&(i.dirty=!1),e[y]&=-9217,s)Og(r,e,r.template,e[q]);else if(o&8192){nu(e),ou(e,1);let a=r.components;a!==null&&su(e,a,1),Xl(e)}}function su(e,t,n){for(let r=0;r<t.length;r++)Pg(e,t[r],n)}function Lg(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Ye(~o);else{let i=o,s=n[++r],a=n[++r];ip(s,i);let c=t[i];T(24,c),a(2,c),T(25,c)}}}finally{Ye(-1)}}function Rs(e,t){let n=Zc()?64:1088;for(e[Te].changeDetectionScheduler?.notify(t);e;){e[y]|=n;let r=Qe(e);if(ur(e)&&!r)return e;e=r}return null}function au(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Fg(e,t){let n=Y+t;if(n<e.length)return e[n]}function Os(e,t,n,r=!0){let o=t[E];if(Vg(o,t,e,n),r){let s=Ei(n,e),a=t[A],c=a.parentNode(e[Ze]);c!==null&&hg(o,e[J],a,t,c,s)}let i=t[Wt];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function jg(e,t){let n=Dr(e,t);return n!==void 0&&Ss(n[E],n),n}function Dr(e,t){if(e.length<=Y)return;let n=Y+t,r=e[n];if(r){let o=r[ze];o!==null&&o!==e&&Ns(o,r),t>0&&(e[n-1][ae]=r[ae]);let i=ir(e,Y+t);pg(r[E],r);let s=i[ve];s!==null&&s.detachView(i[E]),r[V]=null,r[ae]=null,r[y]&=-129}return r}function Vg(e,t,n,r){let o=Y+r,i=n.length;r>0&&(n[o-1][ae]=t),r<i-Y?(t[ae]=n[o],wc(n,Y+r,t)):(n.push(t),t[ae]=null),t[V]=n;let s=t[ze];s!==null&&n!==s&&cu(s,t);let a=t[ve];a!==null&&a.insertView(e),Yo(t),t[y]|=128}function cu(e,t){let n=e[mt],r=t[V];if(Ce(r))e[y]|=2;else{let o=r[V][re];t[re]!==o&&(e[y]|=2)}n===null?e[mt]=[t]:n.push(t)}var Yt=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[E];return Er(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[q]}set context(t){this._lView[q]=t}get destroyed(){return bt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[V];if(De(t)){let n=t[lr],r=n?n.indexOf(this):-1;r>-1&&(Dr(t,r),ir(n,r))}this._attachedToViewContainer=!1}Ss(this._lView[E],this._lView)}onDestroy(t){Wc(this._lView,t)}markForCheck(){Rs(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[y]&=-129}reattach(){Yo(this._lView),this._lView[y]|=128}detectChanges(){this._lView[y]|=1024,ru(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new x(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=ur(this._lView),n=this._lView[ze];n!==null&&!t&&Ns(n,this._lView),Zl(this._lView[E],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new x(902,!1);this._appRef=t;let n=ur(this._lView),r=this._lView[ze];r!==null&&!n&&cu(r,this._lView),Yo(this._lView)}};var wr=(()=>{class e{static __NG_ELEMENT_ID__=$g}return e})(),Hg=wr,Bg=class extends Hg{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=_s(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Yt(o)}};function $g(){return Yr(W(),M())}function Yr(e,t){return e.type&4?new Bg(t,e,Ct(e,t)):null}function xt(e,t,n,r,o){let i=e.data[t];if(i===null)i=Ug(e,t,n,r,o),op()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=ep();i.injectorIndex=s===null?-1:s.injectorIndex}return Se(i,!0),i}function Ug(e,t,n,r,o){let i=Gc(),s=os(),a=s?i:i&&i.parent,c=e.data[t]=Wg(e,a,n,t,r,o);return qg(e,c,i,s),c}function qg(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Wg(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return zc()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var f_=new RegExp(`^(\\d+)*(${Qp}|${Zp})*(.*)`);var zg=()=>null;function br(e,t){return zg(e,t)}var Gg=class{},lu=class{},wi=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Q(t)}.`)}},Jr=class{static NULL=new wi},Mr=class{},m_=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Zg()}return e})();function Zg(){let e=M(),t=W(),n=pe(t.index,e);return(Ce(n)?n:e)[A]}var Qg=(()=>{class e{static \u0275prov=H({token:e,providedIn:"root",factory:()=>null})}return e})();var Po={},bi=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Rr(r);let o=this.injector.get(t,Po,r);return o!==Po||n===Po?o:this.parentInjector.get(t,n,r)}};function Mi(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=$o(o,a);else if(i==2){let c=a,l=t[++s];r=$o(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Kr(e,t=b.Default){let n=M();if(n===null)return _e(e,t);let r=W();return ul(r,n,$(e),t)}function y_(){let e="invalid";throw new Error(e)}function As(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=Jg(s);u===null?a=s:[a,c,l]=u,em(e,t,n,a,i,c,l)}i!==null&&r!==null&&Yg(n,r,i)}function Yg(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new x(-301,!1);r.push(t[o],i)}}function Jg(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&de(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,Kg(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function Kg(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function Xg(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function em(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let d=r[p];!c&&de(d)&&(c=!0,Xg(e,n,p)),ei(gr(n,t),e,d.type)}sm(n,e.data.length,a);for(let p=0;p<a;p++){let d=r[p];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=ql(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let d=r[p];if(n.mergedAttrs=vt(n.mergedAttrs,d.hostAttrs),nm(e,n,t,f,d),im(f,d,o),s!==null&&s.has(d)){let[I,O]=s.get(d);n.directiveToIndex.set(d.type,[f,I+n.directiveStart,O+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let h=d.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}tm(e,n,i)}function tm(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Qa(0,t,o,r),Qa(1,t,o,r),Ja(t,r,!1);else{let i=n.get(o);Ya(0,t,i,r),Ya(1,t,i,r),Ja(t,r,!0)}}}function Qa(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),uu(t,i)}}function Ya(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),uu(t,s)}}function uu(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Ja(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||vs(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function nm(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ue(o.type,!0)),s=new Je(i,de(o),Kr);e.blueprint[r]=s,n[r]=s,rm(e,t,r,ql(e,n,o.hostVars,ge),o)}function rm(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;om(s)!=a&&s.push(a),s.push(n,r,i)}}function om(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function im(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;de(t)&&(n[""]=e)}}function sm(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function du(e,t,n,r,o,i,s,a){let c=t.consts,l=yt(c,s),u=xt(t,e,2,r,l);return i&&As(t,n,u,yt(c,a),o),u.mergedAttrs=vt(u.mergedAttrs,u.attrs),u.attrs!==null&&Mi(u,u.attrs,!1),u.mergedAttrs!==null&&Mi(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function fu(e,t){us(e,t),Ki(t)&&e.queries.elementEnd(t)}var Cr=class extends Jr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=We(t);return new It(n,this.ngModule)}};function am(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&zr.SignalBased)!==0};return o&&(i.transform=o),i})}function cm(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function lm(e,t,n){let r=t instanceof xe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new bi(n,r):n}function um(e){let t=e.get(Mr,null);if(t===null)throw new x(407,!1);let n=e.get(Qg,null),r=e.get(Ke,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function dm(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Hl(t,n,n==="svg"?Vc:n==="math"?Uf:null)}var It=class extends lu{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=am(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=cm(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Hh(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){T(22);let i=w(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:Bh(this.componentDef.selectors[0]),c=Is(0,null,null,1,0,null,null,null,null,[a],null),l=lm(s,o||this.ngModule,t),u=um(l),f=u.rendererFactory.createRenderer(null,s),p=r?Jh(f,r,s.encapsulation,l):dm(s,f),d=Es(null,c,null,512|Ul(s),null,null,u,f,l,null,Nl(p,l,!0));d[j]=p,as(d);let h=null;try{let I=du(j,c,d,"#host",()=>[this.componentDef],!0,0);p&&($l(f,p,I),_t(p,d)),Gr(c,d,I),ys(c,I,d),fu(c,I),n!==void 0&&fm(I,this.ngContentSelectors,n),h=pe(I.index,d),d[q]=h[q],Cs(c,d,null)}catch(I){throw h!==null&&si(h),si(d),I}finally{T(23),cs()}return new Ci(this.componentType,d)}finally{w(i)}}},Ci=class extends Gg{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=Xi(n[E],j),this.location=Ct(this._tNode,n),this.instance=pe(this._tNode.index,n)[q],this.hostView=this.changeDetectorRef=new Yt(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Ms(r,o[E],o,t,n);this.previousInputValues.set(t,n);let s=pe(r.index,o);Rs(s,1)}get injector(){return new $e(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function fm(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ps=(()=>{class e{static __NG_ELEMENT_ID__=pm}return e})();function pm(){let e=W();return hu(e,M())}var hm=Ps,pu=class extends hm{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Ct(this._hostTNode,this._hostLView)}get injector(){return new $e(this._hostTNode,this._hostLView)}get parentInjector(){let t=ds(this._hostTNode,this._hostLView);if(ol(t)){let n=pr(t,this._hostLView),r=fr(t),o=n[E].data[r+8];return new $e(o,n)}else return new $e(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Ka(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Y}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=br(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Ir(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Vf(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new It(We(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let I=(s?l:this.parentInjector).get(xe,null);I&&(i=I)}let u=We(c.componentType??{}),f=br(this._lContainer,u?.id??null),p=f?.firstChild??null,d=c.create(l,o,p,i);return this.insertImpl(d.hostView,a,Ir(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Wf(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[V],l=new pu(c,c[J],c[V]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Os(s,o,i,r),t.attachToViewContainerRef(),wc(Lo(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Ka(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Dr(this._lContainer,n);r&&(ir(Lo(this._lContainer),n),Ss(r[E],r))}detach(t){let n=this._adjustIndex(t,-1),r=Dr(this._lContainer,n);return r&&ir(Lo(this._lContainer),n)!=null?new Yt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Ka(e){return e[lr]}function Lo(e){return e[lr]||(e[lr]=[])}function hu(e,t){let n,r=t[e.index];return De(r)?n=r:(n=au(r,t,null,e),t[e.index]=n,Ds(t,n)),mm(n,t,e,r),new pu(n,e,t)}function gm(e,t){let n=e[A],r=n.createComment(""),o=he(t,e),i=n.parentNode(o);return vr(n,i,r,n.nextSibling(o),!1),r}var mm=Im,ym=()=>!1;function vm(e,t,n){return ym(e,t,n)}function Im(e,t,n,r){if(e[Ze])return;let o;n.type&8?o=fe(r):o=gm(t,n),e[Ze]=o}var _i=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},xi=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Fs(t,n).matches!==null&&this.queries[n].setDirty()}},_r=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Cm(t):this.predicate=t}},Ti=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Ni=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,Em(n,i)),this.matchTNodeWithReadOption(t,n,Xn(n,t,i,!1,!1))}else r===wr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Xn(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Ur||o===Ps||o===wr&&n.type&4)this.addMatch(n.index,-2);else{let i=Xn(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Em(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Dm(e,t){return e.type&11?Ct(e,t):e.type&4?Yr(e,t):null}function wm(e,t,n,r){return n===-1?Dm(t,e):n===-2?bm(e,t,r):Gt(e,e[E],n,t)}function bm(e,t,n){if(n===Ur)return Ct(t,e);if(n===wr)return Yr(t,e);if(n===Ps)return hu(t,e)}function gu(e,t,n,r){let o=t[ve].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(wm(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Si(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=gu(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let f=Y;f<u.length;f++){let p=u[f];p[ze]===p[V]&&Si(p[E],p,l,r)}if(u[mt]!==null){let f=u[mt];for(let p=0;p<f.length;p++){let d=f[p];Si(d[E],d,l,r)}}}}}return r}function Ls(e,t){return e[ve].queries[t].queryList}function mu(e,t,n){let r=new ii((n&4)===4);return Zf(e,t,r,r.destroy),(t[ve]??=new xi).queries.push(new _i(r))-1}function Mm(e,t,n){let r=P();return r.firstCreatePass&&(vu(r,new _r(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),mu(r,M(),t)}function yu(e,t,n,r){let o=P();if(o.firstCreatePass){let i=W();vu(o,new _r(t,n,r),i.index),_m(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return mu(o,M(),n)}function Cm(e){return e.split(",").map(t=>t.trim())}function vu(e,t,n){e.queries===null&&(e.queries=new Ti),e.queries.track(new Ni(t,n))}function _m(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Fs(e,t){return e.queries.getByIndex(t)}function Iu(e,t){let n=e[E],r=Fs(n,t);return r.crossesNgTemplate?Si(n,e,t,[]):gu(n,e,r,t)}function Eu(e,t,n){let r,o=hn(()=>{r._dirtyCounter();let i=Sm(r,e);if(t&&i===void 0)throw new x(-951,!1);return i});return r=o[z],r._dirtyCounter=Vp(0),r._flatValue=void 0,o}function xm(e){return Eu(!0,!1,e)}function Tm(e){return Eu(!0,!0,e)}function Nm(e,t){let n=e[z];n._lView=M(),n._queryIndex=t,n._queryList=Ls(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function Sm(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[y]&4)return t?void 0:U;let o=Ls(n,r),i=Iu(n,r);return o.reset(i,Il),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function Xa(e,t){return xm(t)}function km(e,t){return Tm(t)}var I_=(Xa.required=km,Xa);var Jt=class{},Rm=class{};var ki=class extends Jt{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Cr(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Cc(t);this._bootstrapComponents=Fl(i.bootstrap),this._r3Injector=pl(t,n,[{provide:Jt,useValue:this},{provide:Jr,useValue:this.componentFactoryResolver},...r],Q(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Ri=class extends Rm{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new ki(this.moduleType,t,[])}};var xr=class extends Jt{injector;componentFactoryResolver=new Cr(this);instance=null;constructor(t){super();let n=new qt([...t.providers,{provide:Jt,useValue:this},{provide:Jr,useValue:this.componentFactoryResolver}],t.parent||Ar(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Om(e,t,n=null){return new xr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Am=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=_c(!1,n.type),o=r.length>0?Om([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=H({token:e,providedIn:"environment",factory:()=>new e(_e(xe))})}return e})();function w_(e){return en(()=>{let t=Du(e),n=X(K({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Dl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Am).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Qt.Emulated,styles:e.styles||U,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&rn("NgStandalone"),wu(n);let r=e.dependencies;return n.directiveDefs=ec(r,!1),n.pipeDefs=ec(r,!0),n.id=Vm(n),n})}function Pm(e){return We(e)||_f(e)}function Lm(e){return e!==null}function b_(e){return en(()=>({type:e.type,bootstrap:e.bootstrap||U,declarations:e.declarations||U,imports:e.imports||U,exports:e.exports||U,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Fm(e,t){if(e==null)return qe;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=zr.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function jm(e){if(e==null)return qe;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function M_(e){return en(()=>{let t=Du(e);return wu(t),t})}function C_(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Du(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||qe,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||U,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Fm(e.inputs,t),outputs:jm(e.outputs),debugInfo:null}}function wu(e){e.features?.forEach(t=>t(e))}function ec(e,t){if(!e)return null;let n=t?xf:Pm;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(Lm)}function Vm(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Hm(e){return Object.getPrototypeOf(e.prototype).constructor}function Bm(e){let t=Hm(e.type),n=!0,r=[e];for(;t;){let o;if(de(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new x(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Fo(e.inputs),s.declaredInputs=Fo(e.declaredInputs),s.outputs=Fo(e.outputs);let a=o.hostBindings;a&&zm(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&qm(e,c),l&&Wm(e,l),$m(e,o),tf(e.outputs,o.outputs),de(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Bm&&(n=!1)}}t=Object.getPrototypeOf(t)}Um(r)}function $m(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Um(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=vt(o.hostAttrs,n=vt(n,o.hostAttrs))}}function Fo(e){return e===qe?{}:e===U?[]:e}function qm(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Wm(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function zm(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function bu(e){return Zm(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Gm(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Zm(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Qm(e,t,n){return e[t]=n}function ke(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Ym(e,t,n,r,o,i,s,a,c){let l=t.consts,u=xt(t,e,4,s||null,a||null);rs()&&As(t,n,u,yt(l,c),bs),u.mergedAttrs=vt(u.mergedAttrs,u.attrs),us(t,u);let f=u.tView=Is(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),f.queries=t.queries.embeddedTView(u)),u}function Mu(e,t,n,r,o,i,s,a,c,l){let u=n+j,f=t.firstCreatePass?Ym(u,t,e,r,o,i,s,a,c):t.data[u];Se(f,!1);let p=Km(t,e,f,n);Vr()&&Zr(t,e,p,f),_t(p,e);let d=au(p,e,p,f);return e[u]=d,Ds(e,d),vm(d,f,e),Lr(f)&&Gr(t,e,f),c!=null&&ws(e,f,l),f}function Jm(e,t,n,r,o,i,s,a){let c=M(),l=P(),u=yt(l.consts,i);return Mu(c,l,e,t,n,r,o,u,s,a),Jm}var Km=Xm;function Xm(e,t,n,r){return Hr(!0),t[A].createComment("")}var __=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var ey=new R("");var Cu=(()=>{class e{static \u0275prov=H({token:e,providedIn:"root",factory:()=>new Oi})}return e})(),Oi=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function _u(e){return!!e&&typeof e.then=="function"}function ty(e){return!!e&&typeof e.subscribe=="function"}var ny=new R("");var xu=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=C(ny,{optional:!0})??[];injector=C(Ne);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=kc(this.injector,o);if(_u(i))n.push(i);else if(ty(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ry=new R("");function oy(){co(()=>{throw new x(600,!1)})}function iy(e){return e.isBoundToModule}var sy=10;var Kt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=C(Lp);afterRenderManager=C(xl);zonelessEnabled=C(fs);rootEffectScheduler=C(Cu);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new te;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=C($r).hasPendingTasks.pipe(je(n=>!n));constructor(){C(qr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=C(xe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Ne.NULL){T(10);let i=n instanceof lu;if(!this._injector.get(xu).done){let d="";throw new x(405,d)}let a;i?a=n:a=this._injector.get(Jr).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=iy(a)?void 0:this._injector.get(Jt),l=r||a.selector,u=a.create(o,[],l,c),f=u.location.nativeElement,p=u.injector.get(ey,null);return p?.registerApplication(f),u.onDestroy(()=>{this.detachView(u.hostView),er(this.components,u),p?.unregisterApplication(f)}),this._loadComponent(u),T(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){T(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(gs.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new x(101,!1);let n=w(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,w(n),this.afterTick.next(),T(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Mr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<sy;)T(14),this.synchronizeOnce(),T(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)ay(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Fr(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;er(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(ry,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>er(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new x(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function er(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function ay(e,t,n,r){if(!n&&!Fr(e))return;ru(e,t,n&&!r?0:1)}function cy(e,t,n,r){let o=M(),i=nn();if(ke(o,i,t)){let s=P(),a=ls();ig(a,o,e,t,n,r)}return cy}function Tu(e,t,n,r){return ke(e,nn(),n)?t+Gi(n)+r:ge}function Qn(e,t){return e<<17|t<<2}function et(e){return e>>17&32767}function ly(e){return(e&2)==2}function uy(e,t){return e&131071|t<<17}function Ai(e){return e|2}function Et(e){return(e&131068)>>2}function jo(e,t){return e&-131069|t<<2}function dy(e){return(e&1)===1}function Pi(e){return e|1}function fy(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=et(s),c=Et(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let f=n;u=f[1],(u===null||tn(f,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=et(e[a+1]);e[r+1]=Qn(p,a),p!==0&&(e[p+1]=jo(e[p+1],r)),e[a+1]=uy(e[a+1],r)}else e[r+1]=Qn(a,0),a!==0&&(e[a+1]=jo(e[a+1],r)),a=r;else e[r+1]=Qn(c,0),a===0?a=r:e[c+1]=jo(e[c+1],r),c=r;l&&(e[r+1]=Ai(e[r+1])),tc(e,u,r,!0),tc(e,u,r,!1),py(t,u,e,r,i),s=Qn(a,c),i?t.classBindings=s:t.styleBindings=s}function py(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&tn(i,t)>=0&&(n[r+1]=Pi(n[r+1]))}function tc(e,t,n,r){let o=e[n+1],i=t===null,s=r?et(o):Et(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];hy(c,t)&&(a=!0,e[s+1]=r?Pi(l):Ai(l)),s=r?et(l):Et(l)}a&&(e[n+1]=r?Ai(o):Pi(o))}function hy(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?tn(e,t)>=0:!1}var se={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function gy(e){return e.substring(se.key,se.keyEnd)}function my(e){return yy(e),Nu(e,Su(e,0,se.textEnd))}function Nu(e,t){let n=se.textEnd;return n===t?-1:(t=se.keyEnd=vy(e,se.key=t,n),Su(e,t,n))}function yy(e){se.key=0,se.keyEnd=0,se.value=0,se.valueEnd=0,se.textEnd=e.length}function Su(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function vy(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function Iy(e,t,n){let r=M(),o=nn();if(ke(r,o,t)){let i=P(),s=ls();Gl(i,s,r,e,t,r[A],n,!1)}return Iy}function Li(e,t,n,r,o){Ms(t,e,n,o?"class":"style",r)}function Ey(e,t,n){return Ru(e,t,n,!1),Ey}function Dy(e,t){return Ru(e,t,null,!0),Dy}function x_(e){Ou(xy,ku,e,!0)}function ku(e,t){for(let n=my(t);n>=0;n=Nu(t,n))Or(e,gy(t),!0)}function Ru(e,t,n,r){let o=M(),i=P(),s=Qc(2);if(i.firstUpdatePass&&Pu(i,e,s,r),t!==ge&&ke(o,s,t)){let a=i.data[tt()];Lu(i,a,o,o[A],e,o[s+1]=Ny(t,n),r,s)}}function Ou(e,t,n,r){let o=P(),i=Qc(2);o.firstUpdatePass&&Pu(o,null,i,r);let s=M();if(n!==ge&&ke(s,i,n)){let a=o.data[tt()];if(Fu(a,r)&&!Au(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=$o(c,n||"")),Li(o,a,s,n,r)}else Ty(o,a,s,s[A],s[i+1],s[i+1]=_y(e,t,n),r,i)}}function Au(e,t){return t>=e.expandoStartIndex}function Pu(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[tt()],s=Au(e,n);Fu(i,r)&&t===null&&!s&&(t=!1),t=wy(o,i,t,r),fy(o,i,t,n,s,r)}}function wy(e,t,n,r){let o=ap(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Vo(null,e,t,n,r),n=Xt(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Vo(o,e,t,n,r),i===null){let c=by(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Vo(null,e,t,c[1],r),c=Xt(c,t.attrs,r),My(e,t,r,c))}else i=Cy(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function by(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Et(r)!==0)return e[et(r)]}function My(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[et(o)]=r}function Cy(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Xt(r,s,n)}return Xt(r,t.attrs,n)}function Vo(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Xt(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Xt(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Or(e,s,n?!0:t[++i]))}return e===void 0?null:e}function _y(e,t,n){if(n==null||n==="")return U;let r=[],o=kl(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function xy(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Or(e,r,n)}function Ty(e,t,n,r,o,i,s,a){o===ge&&(o=U);let c=0,l=0,u=0<o.length?o[0]:null,f=0<i.length?i[0]:null;for(;u!==null||f!==null;){let p=c<o.length?o[c+1]:void 0,d=l<i.length?i[l+1]:void 0,h=null,I;u===f?(c+=2,l+=2,p!==d&&(h=f,I=d)):f===null||u!==null&&u<f?(c+=2,h=u):(l+=2,h=f,I=d),h!==null&&Lu(e,t,n,r,h,I,s,a),u=c<o.length?o[c]:null,f=l<i.length?i[l]:null}}function Lu(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=dy(l)?nc(c,t,n,o,Et(l),s):void 0;if(!Tr(u)){Tr(i)||ly(l)&&(i=nc(c,null,n,o,a,s));let f=Hc(tt(),n);bg(r,s,f,o,i)}}function nc(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,f=u===null,p=n[o+1];p===ge&&(p=f?U:void 0);let d=f?No(p,r):u===r?p:void 0;if(l&&!Tr(d)&&(d=No(c,r)),Tr(d)&&(a=d,s))return a;let h=e[o+1];o=s?et(h):Et(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=No(c,r))}return a}function Tr(e){return e!==void 0}function Ny(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Q(kl(e)))),e}function Fu(e,t){return(e.flags&(t?8:16))!==0}function T_(e,t,n){let r=M(),o=Tu(r,e,t,n);Ou(Or,ku,o,!0)}function N_(e,t){rn("NgControlFlow");let n=M(),r=nn(),o=n[r]!==ge?n[r]:-1,i=o!==-1?rc(n,j+o):void 0,s=0;if(ke(n,r,e)){let a=w(null);try{if(i!==void 0&&jg(i,s),e!==-1){let c=j+e,l=rc(n,c),u=Sy(n[E],c),f=br(l,u.tView.ssrId),p=_s(n,u,t,{dehydratedView:f});Os(l,p,s,Ir(u,f))}}finally{w(a)}}else if(i!==void 0){let a=Fg(i,s);a!==void 0&&(a[q]=t)}}function rc(e,t){return e[t]}function Sy(e,t){return Xi(e,t)}function ju(e,t,n,r){let o=M(),i=P(),s=j+e,a=o[A],c=i.firstCreatePass?du(s,i,o,t,bs,rs(),n,r):i.data[s],l=Ry(i,o,c,a,t,e);o[s]=l;let u=Lr(c);return Se(c,!0),$l(a,l,c),!Ts(c)&&Vr()&&Zr(i,o,l,c),(Qf()===0||u)&&_t(l,o),Yf(),u&&(Gr(i,o,c),ys(i,c,o)),r!==null&&ws(o,c),ju}function Vu(){let e=W();os()?is():(e=e.parent,Se(e,!1));let t=e;Kf(t)&&Xf(),Jf();let n=P();return n.firstCreatePass&&fu(n,t),t.classesWithoutHost!=null&&hp(t)&&Li(n,t,M(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&gp(t)&&Li(n,t,M(),t.stylesWithoutHost,!1),Vu}function ky(e,t,n,r){return ju(e,t,n,r),Vu(),ky}var Ry=(e,t,n,r,o,i)=>(Hr(!0),Hl(r,o,dp()));function Oy(e,t,n,r,o){let i=t.consts,s=yt(i,r),a=xt(t,e,8,"ng-container",s);s!==null&&Mi(a,s,!0);let c=yt(i,o);return rs()&&As(t,n,a,c,bs),a.mergedAttrs=vt(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function Hu(e,t,n){let r=M(),o=P(),i=e+j,s=o.firstCreatePass?Oy(i,o,r,t,n):o.data[i];Se(s,!0);let a=Py(o,r,s,e);return r[i]=a,Vr()&&Zr(o,r,a,s),_t(a,r),Lr(s)&&(Gr(o,r,s),ys(o,s,r)),n!=null&&ws(r,s),Hu}function Bu(){let e=W(),t=P();return os()?is():(e=e.parent,Se(e,!1)),t.firstCreatePass&&(us(t,e),Ki(e)&&t.queries.elementEnd(e)),Bu}function Ay(e,t,n){return Hu(e,t,n),Bu(),Ay}var Py=(e,t,n,r)=>(Hr(!0),qh(t[A],""));function S_(){return M()}function Ly(e,t,n){let r=M(),o=nn();if(ke(r,o,t)){let i=P(),s=ls();Gl(i,s,r,e,t,r[A],n,!0)}return Ly}var Nr="en-US";var Fy=Nr;function jy(e){typeof e=="string"&&(Fy=e.toLowerCase().replace(/_/g,"-"))}function oc(e,t,n){return function r(o){if(o===Function)return n;let i=wt(e)?pe(e.index,t):t;Rs(i,5);let s=t[q],a=ic(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=ic(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ic(e,t,n,r){let o=w(null);try{return T(6,t,n),n(r)!==!1}catch(i){return Vy(e,i),!1}finally{T(7,t,n),w(o)}}function Vy(e,t){let n=e[gt],r=n?n.get(Xe,null):null;r&&r.handleError(t)}function sc(e,t,n,r,o,i){let s=t[n],a=t[E],l=a.data[n].outputs[r],u=s[l],f=a.firstCreatePass?ns(a):null,p=ts(t),d=u.subscribe(i),h=p.length;p.push(i,d),f&&f.push(o,e.index,h,-(h+1))}function Hy(e,t,n,r){let o=M(),i=P(),s=W();return $y(i,o,o[A],s,e,t,r),Hy}function By(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[cr],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function $y(e,t,n,r,o,i,s){let a=Lr(r),l=e.firstCreatePass?ns(e):null,u=ts(t),f=!0;if(r.type&3||s){let p=he(r,t),d=s?s(p):p,h=u.length,I=s?N=>s(fe(N[r.index])):r.index,O=null;if(!s&&a&&(O=By(e,t,o,r.index)),O!==null){let N=O.__ngLastListenerFn__||O;N.__ngNextListenerFn__=i,O.__ngLastListenerFn__=i,f=!1}else{i=oc(r,t,i),oh(t,d,o,i);let N=n.listen(d,o,i);u.push(i,N),l&&l.push(o,I,h,h+1)}}else i=oc(r,t,i);if(f){let p=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let h=0;h<d.length;h+=2){let I=d[h],O=d[h+1];sc(r,t,I,O,o,i)}if(p&&p.length)for(let h of p)sc(r,t,h,o,o,i)}}function k_(e=1){return lp(e)}function Uy(e,t){let n=null,r=Ph(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Vl(e,i,!0):jh(r,i))return o}return n}function R_(e){let t=M()[re][J];if(!t.projection){let n=e?e.length:1,r=t.projection=bf(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Uy(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function O_(e,t=0,n,r,o,i){let s=M(),a=P(),c=r?e+1:null;c!==null&&Mu(s,a,c,r,o,i,null,n);let l=xt(a,j+e,16,null,n||null);l.projection===null&&(l.projection=t),is();let f=!s[Wt]||zc();s[re][J].projection[l.projection]===null&&c!==null?qy(s,a,c):f&&!Ts(l)&&Dg(a,s,l)}function qy(e,t,n){let r=j+n,o=t.data[r],i=e[r],s=br(i,o.tView.ssrId),a=_s(e,o,void 0,{dehydratedView:s});Os(i,a,0,Ir(o,s))}function A_(e,t,n,r){yu(e,t,n,r)}function P_(e,t,n){Mm(e,t,n)}function L_(e){let t=M(),n=P(),r=ss();jr(r+1);let o=Fs(n,r);if(e.dirty&&qf(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Iu(t,r);e.reset(i,Il),e.notifyOnChanges()}return!0}return!1}function F_(){return Ls(M(),ss())}function j_(e,t,n,r,o){Nm(t,yu(e,n,r,o))}function V_(e=1){jr(ss()+e)}function H_(e){let t=tp();return Bc(t,j+e)}function B_(e,t=""){let n=M(),r=P(),o=e+j,i=r.firstCreatePass?xt(r,o,1,t,null):r.data[o],s=Wy(r,n,i,t,e);n[o]=s,Vr()&&Zr(r,n,s,i),Se(i,!1)}var Wy=(e,t,n,r,o)=>(Hr(!0),$h(t[A],r));function zy(e){return $u("",e,""),zy}function $u(e,t,n){let r=M(),o=Tu(r,e,t,n);return o!==ge&&Gy(r,tt(),o),$u}function Gy(e,t,n){let r=Hc(t,e);Uh(e[A],r,n)}var Zy={};function Qy(e){let t=P(),n=M(),r=e+j,o=xt(t,r,128,null,null);return Se(o,!1),$c(t,n,r,Zy),Qy}function Yy(e,t,n){let r=P();if(r.firstCreatePass){let o=de(e);Fi(n,r.data,r.blueprint,o,!0),Fi(t,r.data,r.blueprint,o,!1)}}function Fi(e,t,n,r,o){if(e=$(e),Array.isArray(e))for(let i=0;i<e.length;i++)Fi(e[i],t,n,r,o);else{let i=P(),s=M(),a=W(),c=ht(e)?e:$(e.provide),l=Sc(e),u=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(ht(e)||!e.multi){let d=new Je(l,o,Kr),h=Bo(c,t,o?u:u+p,f);h===-1?(ei(gr(a,s),i,c),Ho(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[h]=d,s[h]=d)}else{let d=Bo(c,t,u+p,f),h=Bo(c,t,u,u+p),I=d>=0&&n[d],O=h>=0&&n[h];if(o&&!O||!o&&!I){ei(gr(a,s),i,c);let N=Xy(o?Ky:Jy,n.length,o,r,l);!o&&O&&(n[h].providerFactory=N),Ho(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(N),s.push(N)}else{let N=Uu(n[o?h:d],l,!o&&r);Ho(i,e,d>-1?d:h,N)}!o&&r&&O&&n[h].componentProviders++}}}function Ho(e,t,n,r){let o=ht(t),i=Of(t);if(o||i){let c=(i?$(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function Uu(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Bo(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Jy(e,t,n,r,o){return ji(this.multi,[])}function Ky(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Gt(r,r[E],this.providerFactory.index,o);s=c.slice(0,a),ji(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],ji(i,s);return s}function ji(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Xy(e,t,n,r,o){let i=new Je(e,n,Kr);return i.multi=[],i.index=t,i.componentProviders=0,Uu(i,o,r&&!n),i}function $_(e,t=[]){return n=>{n.providersResolver=(r,o)=>Yy(r,o?o(e):e,t)}}function ev(e,t){let n=e[t];return n===ge?void 0:n}function tv(e,t,n,r,o,i){let s=t+n;return ke(e,s,o)?Qm(e,s+1,i?r.call(i,o):r(o)):ev(e,s+1)}function U_(e,t){let n=P(),r,o=e+j;n.firstCreatePass?(r=nv(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Ue(r.type,!0)),s,a=Z(Kr);try{let c=hr(!1),l=i();return hr(c),$c(n,M(),o,l),l}finally{Z(a)}}function nv(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function q_(e,t,n){let r=e+j,o=M(),i=Bc(o,r);return rv(o,r)?tv(o,np(),t,i.transform,n,i):i.transform(n)}function rv(e,t){return e[E].data[t].pure}function W_(e,t){return Yr(e,t)}var Vi=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},z_=new Vi("19.2.14"),Hi=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},G_=(()=>{class e{compileModuleSync(n){return new Ri(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Cc(n),i=Fl(o.declarations).reduce((s,a)=>{let c=We(a);return c&&s.push(new It(c)),s},[]);return new Hi(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ov=(()=>{class e{zone=C(ee);changeDetectionScheduler=C(Ke);applicationRef=C(Kt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),iv=new R("",{factory:()=>!1});function qu({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new ee(X(K({},Wu()),{scheduleInRootZone:n})),[{provide:ee,useFactory:e},{provide:sr,multi:!0,useFactory:()=>{let r=C(ov,{optional:!0});return()=>r.initialize()}},{provide:sr,multi:!0,useFactory:()=>{let r=C(sv);return()=>{r.initialize()}}},t===!0?{provide:gl,useValue:!0}:[],{provide:ml,useValue:n??hl}]}function Z_(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=qu({ngZoneFactory:()=>{let o=Wu(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&rn("NgZone_CoalesceEvent"),new ee(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Tf([{provide:iv,useValue:!0},{provide:fs,useValue:!1},r])}function Wu(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var sv=(()=>{class e{subscription=new L;initialized=!1;zone=C(ee);pendingTasks=C($r);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ee.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ee.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var av=(()=>{class e{appRef=C(Kt);taskService=C($r);ngZone=C(ee);zonelessEnabled=C(fs);tracing=C(qr,{optional:!0});disableScheduling=C(gl,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new L;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(yr):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(C(ml,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof oi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?La:yl;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(yr+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,La(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=H({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function cv(){return typeof $localize<"u"&&$localize.locale||Nr}var zu=new R("",{providedIn:"root",factory:()=>C(zu,b.Optional|b.SkipSelf)||cv()});var Bi=new R(""),lv=new R("");function Ht(e){return!e.moduleRef}function uv(e){let t=Ht(e)?e.r3Injector:e.moduleRef.injector,n=t.get(ee);return n.run(()=>{Ht(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Xe,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Ht(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Bi);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Bi);s.add(i),e.moduleRef.onDestroy(()=>{er(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return fv(r,n,()=>{let i=t.get(xu);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(zu,Nr);if(jy(s||Nr),!t.get(lv,!0))return Ht(e)?t.get(Kt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ht(e)){let c=t.get(Kt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return dv(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function dv(e,t){let n=e.injector.get(Kt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new x(-403,!1);t.push(e)}function fv(e,t,n){try{let r=n();return _u(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var tr=null;function pv(e=[],t){return Ne.create({name:t,providers:[{provide:Nc,useValue:"platform"},{provide:Bi,useValue:new Set([()=>tr=null])},...e]})}function hv(e=[]){if(tr)return tr;let t=pv(e);return tr=t,oy(),gv(t),t}function gv(e){let t=e.get(Gp,null);kc(e,()=>{t?.forEach(n=>n())})}var Q_=(()=>{class e{static __NG_ELEMENT_ID__=mv}return e})();function mv(e){return yv(W(),M(),(e&16)===16)}function yv(e,t,n){if(wt(e)&&!n){let r=pe(e.index,t);return new Yt(r,r)}else if(e.type&175){let r=t[re];return new Yt(r,t)}return null}var $i=class{constructor(){}supports(t){return bu(t)}create(t){return new Ui(t)}},vv=(e,t)=>t,Ui=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||vv}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<ac(r,o,i)?n:r,a=ac(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let d=p<i.length?i[p]:i[p]=0,h=d+p;u<=h&&h<l&&(i[p]=d+1)}let f=s.previousIndex;i[f]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!bu(t))throw new x(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Gm(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new qi(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Sr),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Sr),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},qi=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Wi=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Sr=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Wi,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ac(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function cc(){return new Iv([new $i])}var Iv=(()=>{class e{factories;static \u0275prov=H({token:e,providedIn:"root",factory:cc});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||cc()),deps:[[e,new Ef,new If]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new x(901,!1)}}return e})();function Y_(e){T(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=hv(r),i=[qu({}),{provide:Ke,useExisting:av},...n||[]],s=new xr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return uv({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{T(9)}}function J_(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function K_(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function X_(e){return fo(e)}function ex(e,t){return hn(e,t?.equal)}var zi=class{[z];constructor(t){this[z]=t}destroy(){this[z].destroy()}};function Ev(e,t){!t?.injector&&Ji(Ev);let n=t?.injector??C(Ne),r=t?.manualCleanup!==!0?n.get(Br):null,o,i=n.get(ms,null,{optional:!0}),s=n.get(Ke);return i!==null&&!t?.forceRoot?(o=bv(i.view,s,e),r instanceof mr&&r._lView===i.view&&(r=null)):o=Mv(e,n.get(Cu),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new zi(o)}var Gu=X(K({},nt),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Zt,run(){if(this.dirty=!1,this.hasRun&&!dn(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=St(this),n=dr(!1);try{this.maybeCleanup(),this.fn(e)}finally{dr(n),un(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),Dv=X(K({},Gu),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){kt(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),wv=X(K({},Gu),{consumerMarkedDirty(){this.view[y]|=8192,Mt(this.view),this.notifier.notify(13)},destroy(){kt(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Ge]?.delete(this)}});function bv(e,t,n){let r=Object.create(wv);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Ge]??=new Set,e[Ge].add(r),r.consumerMarkedDirty(r),r}function Mv(e,t,n){let r=Object.create(Dv);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function tx(e,t){let n=We(e),r=t.elementInjector||Ar();return new It(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}export{K as a,X as b,Cv as c,L as d,cd as e,_ as f,wo as g,bo as h,te as i,Pt as j,Pe as k,le as l,vd as m,Id as n,Ed as o,Fe as p,je as q,Td as r,Ve as s,qn as t,Sd as u,kd as v,Rd as w,He as x,Od as y,Ia as z,Ad as A,Pd as B,Vt as C,Co as D,Ld as E,Vd as F,Hd as G,_o as H,Bd as I,$d as J,Ud as K,To as L,qd as M,Wd as N,zd as O,Gd as P,Zd as Q,Qd as R,Yd as S,x as T,pc as U,H as V,BC as W,$C as X,R as Y,b as Z,_e as _,C as $,Tf as aa,Nc as ba,xe as ca,kc as da,UC as ea,qC as fa,WC as ga,zC as ha,GC as ia,ZC as ja,Ne as ka,Pa as la,Br as ma,Ke as na,$r as oa,be as pa,ee as qa,Xe as ra,QC as sa,Ur as ta,YC as ua,Vp as va,ii as wa,JC as xa,KC as ya,Gp as za,XC as Aa,e_ as Ba,t_ as Ca,qr as Da,rn as Ea,eh as Fa,th as Ga,Qt as Ha,kl as Ia,n_ as Ja,r_ as Ka,o_ as La,i_ as Ma,s_ as Na,a_ as Oa,dh as Pa,c_ as Qa,Ch as Ra,l_ as Sa,Ii as Ta,wr as Ua,Mr as Va,m_ as Wa,Kr as Xa,y_ as Ya,Ps as Za,I_ as _a,Jt as $a,Rm as ab,Om as bb,w_ as cb,b_ as db,M_ as eb,C_ as fb,Bm as gb,Jm as hb,__ as ib,_u as jb,ry as kb,Kt as lb,cy as mb,Iy as nb,Ey as ob,Dy as pb,x_ as qb,T_ as rb,N_ as sb,ju as tb,Vu as ub,ky as vb,Ay as wb,S_ as xb,Ly as yb,Hy as zb,k_ as Ab,R_ as Bb,O_ as Cb,A_ as Db,P_ as Eb,L_ as Fb,F_ as Gb,j_ as Hb,V_ as Ib,H_ as Jb,B_ as Kb,zy as Lb,$u as Mb,Qy as Nb,$_ as Ob,U_ as Pb,q_ as Qb,W_ as Rb,z_ as Sb,G_ as Tb,Z_ as Ub,Q_ as Vb,Iv as Wb,Y_ as Xb,J_ as Yb,K_ as Zb,X_ as _b,ex as $b,Ev as ac,tx as bc};
