@use "../variables" as *;

.social-chips {
  img {
    margin-left: -9px;
    border: 2px solid $white;

    &:first-child {
      margin-left: 0;
    }
  }
}

.minus-img {
  margin-bottom: -65px !important;
}

// theme select
.theme-select {
  width: 145px;
}

// dashboard 2
.welcome-img {
  margin-bottom: -82px;
  margin-top: -9px;
}

.timeline {
  position: relative;

  .timeline-item {
    position: relative;
    height: 70px;

    .time {
      padding: 6px 16px 6px 0;
      min-width: 90px;
      flex-shrink: 0;
    }

    .desc {
      padding: 6px 16px;
    }

    .timline-border {
      width: 1px;
      height: 100%;
      background-color: $borderColor;
      flex-shrink: 0;
    }

    .point {
      flex-direction: column;

      .timeline-badge {
        width: 12px;
        height: 12px;
        border-radius: 50px;
        background-color: transparent;
        flex-shrink: 0;

        &.border-primary {
          border: 2px solid $primary;
        }

        &.border-accent {
          border: 2px solid $accent;
        }

        &.border-success {
          border: 2px solid $success;
        }

        &.border-warning {
          border: 2px solid $warning;
        }

        &.border-error {
          border: 2px solid $error;
        }
      }

      .timeline-border {
        width: 1px;
        height: 100%;
        background-color: $borderColor;
        flex-shrink: 0;
      }
    }

    &:last-child {
      .timeline-border {
        display: none !important;
      }
    }
  }
}

// expansion panel

html .mat-expansion-panel:not([class*="mat-elevation-z"]) {
  box-shadow: var(--mat-sys-level2);
}

.most-visit-chart {
  .apexcharts-bar-series.apexcharts-plot-series .apexcharts-series path {
    clip-path: inset(0 0 5% 0 round 20px);
  }
}


// User Profile Tab
.profileTabs {
  background-color: var(--mat-sys-surface-bright);

  .mat-mdc-tab-label-container{
    border-bottom-width: 0;
  }

  .mat-mdc-tab.mdc-tab-indicator--active{
    .mdc-tab__text-label{
      color: var(--mat-sys-primary) !important;
    }
  }
}