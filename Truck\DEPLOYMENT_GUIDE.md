# Truck Management Module - Deployment Guide

## Overview
This guide explains how to deploy the Angular 19 Truck Management module to your production server.

## Pre-Deployment Checklist

### 1. Build the Angular Application
```bash
cd Truck
ng build
```
This creates the `dist/` folder with compiled Angular files.

### 2. Files to Deploy
Copy these files/folders to your server:
- ✅ `index.php` (Entry point)
- ✅ `dist/` folder (Complete Angular build)
- ✅ `database/` folder (PHP classes and SQL)
- ✅ `includes/` folder (PHP API endpoints)

### 3. Files NOT to Deploy
Do NOT copy these development files:
- ❌ `src/` folder (Angular source code)
- ❌ `node_modules/` folder (Dependencies)
- ❌ `angular.json` (Angular config)
- ❌ `package.json` (NPM config)
- ❌ `tsconfig.json` (TypeScript config)
- ❌ `.editorconfig`, `.gitignore` (Development files)

## Deployment Steps

### Step 1: Prepare Files
1. Run `ng build` in the Truck directory
2. Verify the `dist/browser/` folder contains:
   - `index.html`
   - `main-*.js`
   - `polyfills-*.js`
   - `styles-*.css`
   - `chunk-*.js` files

### Step 2: Upload to Server
Upload only the production files:
```
/aws/Truck/
├── index.php
├── dist/
│   └── browser/
│       ├── index.html
│       ├── main-3DSF7OG7.js
│       ├── polyfills-B6TNHZQ6.js
│       ├── styles-36AW6TKX.css
│       └── chunk-*.js files
├── database/
│   ├── truck.class.php
│   └── create_trucks_table.sql
└── includes/
    └── truck_submit.php
```

### Step 3: Database Setup
1. Run the SQL script: `database/create_trucks_table.sql`
2. Ensure your database connection in `../connection.php` is working

### Step 4: Update Navigation
Add the Truck module link to your main sidebar.php:
```html
<li><a href="Truck/">Truck Management</a></li>
```

### Step 5: Test the Module
1. Navigate to `https://yourserver.com/aws/Truck/`
2. Verify the form loads correctly
3. Test form submission
4. Check PHP session integration

## File Hash Verification
After each build, the Angular CLI generates new file hashes. Update `index.php` if needed:

Current files (update after each build):
- `main-3DSF7OG7.js`
- `polyfills-B6TNHZQ6.js`
- `styles-36AW6TKX.css`

## Troubleshooting

### Issue: Angular app doesn't load
- Check if all files in `dist/browser/` are uploaded
- Verify file paths in `index.php` match actual file names
- Check browser console for 404 errors

### Issue: PHP API errors
- Verify `../connection.php` path is correct
- Check database table exists
- Ensure PHP session is working

### Issue: Session not shared
- Verify `session_start()` is called in `index.php`
- Check if `$_SESSION['user']` exists
- Ensure base href is correct

## Production Optimization

### 1. Enable Gzip Compression
Add to your `.htaccess`:
```apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css application/javascript
</IfModule>
```

### 2. Set Cache Headers
```apache
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
</IfModule>
```

### 3. Security Headers
```apache
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
```

## Automated Deployment Script

Create a deployment script:
```bash
#!/bin/bash
# deploy.sh

echo "Building Angular application..."
ng build

echo "Creating deployment package..."
mkdir -p deploy/Truck
cp index.php deploy/Truck/
cp -r dist deploy/Truck/
cp -r database deploy/Truck/
cp -r includes deploy/Truck/

echo "Deployment package ready in deploy/ folder"
echo "Upload the contents of deploy/Truck/ to your server"
```

## Success Indicators

✅ Angular app loads without errors
✅ Form validation works
✅ PHP API responds correctly
✅ Session data is available
✅ Database operations work
✅ Navigation integration works

## Next Steps

After successful deployment:
1. Monitor server logs for any errors
2. Test all form functionality
3. Verify database operations
4. Train users on the new interface
5. Plan migration of other modules to Angular 19
