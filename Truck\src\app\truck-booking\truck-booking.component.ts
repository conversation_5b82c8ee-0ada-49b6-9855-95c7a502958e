import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { MaterialModule } from '../material.module';
import { MatNativeDateModule } from '@angular/material/core';

interface Facility {
  FacilityID: number;
  FacilityName: string;
}

interface ParkingLocation {
  ParkingLocationID: number;
  ParkingLocationName: string;
}

interface Carrier {
  CarrierID: number;
  CarrierName: string;
  WasteCollectionPermit?: string;
  WasteCollectionEligible?: number;
}

interface TruckType {
  TruckTypeID: number;
  TruckTypeName: string;
}

@Component({
  selector: 'app-truck-booking',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MaterialModule,
    MatNativeDateModule
  ],
  templateUrl: './truck-booking.component.html',
  styleUrl: './truck-booking.component.scss'
})
export class TruckBookingComponent implements OnInit {
  truckBookingForm!: FormGroup;
  isLoading = false;
  isSaving = false;
  submittedData: any = null;

  facilities: Facility[] = [];
  parkingLocations: ParkingLocation[] = [];
  carriers: Carrier[] = [];
  truckTypes: TruckType[] = [];
  selectedCarrier: Carrier | null = null;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.initializeForm();
    this.loadData();
  }

  private initializeForm(): void {
    const today = new Date();

    this.truckBookingForm = this.fb.group({
      FacilityID: ['', Validators.required],
      ArrivalType: ['', Validators.required],
      ParkingLocationID: ['', Validators.required],
      CarrierID: ['', Validators.required],
      ArrivalDate: [today, Validators.required],
      ArrivalTime: ['', Validators.required],
      LoadType: ['', Validators.required],
      LoadNumber: ['', [Validators.required, Validators.maxLength(50)]],
      TruckTypeID: ['', Validators.required],
      TruckReg: ['', [Validators.required, Validators.maxLength(50)]],
      TrailerNumber: ['', [Validators.required, Validators.maxLength(50)]],
      DriverName: ['', [Validators.required, Validators.maxLength(50)]],
      DriverID: ['', Validators.maxLength(50)],
      ShipmentTicketID: ['', Validators.maxLength(50)],
      ClassificationType: ['', Validators.required],
      WasteCollectionPermit: ['', Validators.maxLength(250)],
      Notes: ['', Validators.maxLength(250)],
      Status: ['Reserved', Validators.required]
    });
  }

  private loadData() {
    this.isLoading = true;

    // Load all data in parallel
    Promise.all([
      this.loadFacilities(),
      this.loadParkingLocations(),
      this.loadCarriers(),
      this.loadTruckTypes()
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  private loadFacilities(): Promise<void> {
    return new Promise((resolve) => {
      // Get session facility from window.sessionData
      const sessionData = (window as any).sessionData;
      if (sessionData?.facilityId) {
        this.truckBookingForm.patchValue({ FacilityID: sessionData.facilityId });
      }

      this.http.get<any>('/api/v1/truck-booking/facilities').subscribe({
        next: (data) => {
          if (data.Success) {
            this.facilities = data.Result;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private loadParkingLocations(): Promise<void> {
    return new Promise((resolve) => {
      this.http.get<any>('/api/v1/truck-booking/parking-locations').subscribe({
        next: (data) => {
          if (data.Success) {
            this.parkingLocations = data.Result;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private loadCarriers(): Promise<void> {
    return new Promise((resolve) => {
      this.http.get<any>('/api/v1/truck-booking/carriers').subscribe({
        next: (data) => {
          if (data.Success) {
            this.carriers = data.Result;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private loadTruckTypes(): Promise<void> {
    return new Promise((resolve) => {
      this.http.get<any>('/api/v1/truck-booking/truck-types').subscribe({
        next: (data) => {
          if (data.Success) {
            this.truckTypes = data.Result;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  onCarrierChange() {
    const carrierId = this.truckBookingForm.get('CarrierID')?.value;
    this.selectedCarrier = this.carriers.find(c => c.CarrierID.toString() === carrierId) || null;

    // Auto-fill Waste Collection Permit
    if (this.selectedCarrier?.WasteCollectionPermit) {
      this.truckBookingForm.patchValue({
        WasteCollectionPermit: this.selectedCarrier.WasteCollectionPermit
      });
    } else {
      this.truckBookingForm.patchValue({ WasteCollectionPermit: '' });
    }

    // Clear WEEE if carrier is not eligible
    const classificationType = this.truckBookingForm.get('ClassificationType')?.value;
    if (classificationType === 'WEEE' &&
        (!this.selectedCarrier || this.selectedCarrier.WasteCollectionEligible !== 1)) {
      this.truckBookingForm.patchValue({ ClassificationType: '' });
    }
  }

  onSave() {
    if (this.truckBookingForm.valid) {
      this.isSaving = true;
      const formData = this.truckBookingForm.value;

      // Format date for backend
      if (formData.ArrivalDate) {
        formData.ArrivalDate = formData.ArrivalDate.toISOString().substring(0, 10);
      }

      this.http.post<any>('/api/v1/truck-booking/save', formData).subscribe({
        next: (data) => {
          this.isSaving = false;
          if (data.Success) {
            this.submittedData = formData;
            alert('Truck booking saved successfully!');
            this.onReset();
          } else {
            alert('Error: ' + data.Result);
          }
        },
        error: () => {
          this.isSaving = false;
          alert('Error saving truck booking. Please try again.');
        }
      });
    } else {
      this.markFormGroupTouched();
      alert('Please fill in all required fields.');
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.truckBookingForm.controls).forEach(key => {
      const control = this.truckBookingForm.get(key);
      control?.markAsTouched();
    });
  }

  onReset() {
    const today = new Date();
    this.truckBookingForm.reset();
    this.truckBookingForm.patchValue({
      ArrivalDate: today,
      Status: 'Reserved'
    });
    this.selectedCarrier = null;
    this.submittedData = null;
  }

  isWeeeDisabled(): boolean {
    return !this.selectedCarrier || this.selectedCarrier.WasteCollectionEligible !== 1;
  }

  isWasteCollectionPermitRequired(): boolean {
    const classificationType = this.truckBookingForm.get('ClassificationType')?.value;
    const facilityId = this.truckBookingForm.get('FacilityID')?.value;
    return classificationType === 'WEEE' && facilityId === '14';
  }
}
