@echo off
echo ========================================
echo Truck Management Module - Deployment
echo ========================================
echo.

echo Building Angular application...
call ng build
if %errorlevel% neq 0 (
    echo ERROR: Angular build failed!
    pause
    exit /b 1
)

echo.
echo Creating deployment package...
if exist deploy rmdir /s /q deploy
mkdir deploy\Truck

echo Copying production files...
copy index.php deploy\Truck\
xcopy /e /i dist deploy\Truck\dist
xcopy /e /i database deploy\Truck\database
xcopy /e /i includes deploy\Truck\includes

echo.
echo ========================================
echo Deployment package created successfully!
echo ========================================
echo.
echo Location: deploy\Truck\
echo.
echo Files included:
echo - index.php (Entry point)
echo - dist\ (Angular build)
echo - database\ (PHP classes and SQL)
echo - includes\ (PHP API endpoints)
echo.
echo NEXT STEPS:
echo 1. Upload the contents of deploy\Truck\ to your server
echo 2. Run the SQL script: database\create_trucks_table.sql
echo 3. Add Truck module link to your main sidebar
echo 4. Test the application
echo.
echo For detailed instructions, see DEPLOYMENT_GUIDE.md
echo.
pause
