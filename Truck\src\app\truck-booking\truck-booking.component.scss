// Truck Booking Component Styles

.page {
  padding: 20px;
}

.full-width {
  width: 100%;
}

.btns-row {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  justify-content: flex-end;
}

.no-margin-h {
  margin: 0;
}

mat-card-content {
  padding: 16px;
}

mat-toolbar {
  background-color: #f5f5f5;
  color: #333;
}

mat-toolbar mat-icon {
  margin-right: 8px;
}

pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
}

// Form field spacing
.row {
  margin-bottom: 16px;
}

// Responsive design
@media (max-width: 768px) {
  .btns-row {
    flex-direction: column;
    align-items: stretch;
  }

  .page {
    padding: 12px;
  }
}

// Loading state
.text-center {
  text-align: center;
}

// Info header styling
.bg-light-primary {
  background-color: rgba(255, 202, 40, 0.08); // Light orange background
}

.text-primary {
  color: #8bc34a; // Green color
}

.text-success {
  color: #8bc34a; // Green color
}

.text-warning {
  color: #ffca28; // Orange color
}

// Icon sizing
.icon-20 {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

// Rounded corners
.rounded {
  border-radius: 8px;
}

// AngularJS Material Design button classes
.md-button {
  border: none;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  line-height: 36px;
  margin: 6px 8px;
  min-height: 36px;
  min-width: 64px;
  outline: none;
  padding: 0 16px;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  user-select: none;
  white-space: nowrap;
}

.md-button.md-raised {
  box-shadow: 0 2px 5px 0 rgba(0,0,0,0.26);
}

.md-button.md-raised:hover {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.4);
}

.md-button.md-default {
  background-color: #fafafa;
  color: rgba(0,0,0,0.87);
}

.md-button.md-primary {
  background-color: #7cc242;
  color: white;
}

.btn-w-md {
  min-width: 135px !important;
}

.btns-row {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}