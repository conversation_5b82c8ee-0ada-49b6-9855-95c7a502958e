Write-Host "Building Angular application..." -ForegroundColor Green
ng build --configuration=production

Write-Host "Copying files to browser directory..." -ForegroundColor Green
# Clean up old files
Remove-Item browser\*.js -ErrorAction SilentlyContinue
Remove-Item browser\*.css -ErrorAction SilentlyContinue
Remove-Item browser\*.html -ErrorAction SilentlyContinue

# Copy new files
Copy-Item browser\browser\*.js browser\
Copy-Item browser\browser\*.css browser\
Copy-Item browser\browser\*.html browser\

# Get the actual file names
$mainJs = (Get-ChildItem browser\main-*.js).Name
$polyfillsJs = (Get-ChildItem browser\polyfills-*.js).Name
$stylesCSS = (Get-ChildItem browser\styles-*.css).Name
$chunkFiles = (Get-ChildItem browser\chunk-*.js).Name

Write-Host "Found files:" -ForegroundColor Yellow
Write-Host "  Main: $mainJs" -ForegroundColor White
Write-Host "  Polyfills: $polyfillsJs" -ForegroundColor White
Write-Host "  Styles: $stylesCSS" -ForegroundColor White
Write-Host "  Chunks: $($chunkFiles -join ', ')" -ForegroundColor White

# Update index.php with correct file names
$indexContent = Get-Content index.php -Raw

# Build the new script section
$newScripts = @"
        <!-- Angular 19 Built Scripts -->
"@

foreach ($chunk in $chunkFiles) {
    $newScripts += "`n        <link rel=`"modulepreload`" href=`"browser/$chunk`">"
}

$newScripts += @"

        <link rel="stylesheet" href="browser/$stylesCSS">
        <script src="browser/$polyfillsJs" type="module"></script>
        <script src="browser/$mainJs" type="module"></script>
"@

# Replace the script section in index.php
$pattern = '(?s)(\s*<!-- Angular 19 Built Scripts -->.*?<script src="browser/[^"]*" type="module"></script>)'
$indexContent = $indexContent -replace $pattern, $newScripts

# Write back to index.php
Set-Content index.php $indexContent

Write-Host "Updated index.php with correct file names" -ForegroundColor Green

# Clean up nested directory
Remove-Item browser\browser -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Build and deployment complete!" -ForegroundColor Green
Write-Host "Files are ready to use with correct references in index.php" -ForegroundColor Green
