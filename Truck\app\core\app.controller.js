(function () {
    'use strict';

    // Wait for the app module to be defined
    angular.module('app').controller('AppCtrl', ['$scope', '$rootScope', AppCtrl]);

    function AppCtrl($scope, $rootScope) {
        // Configuration matching the audit module
        $scope.main = {
            layout: 'wide',
            menu: 'vertical',
            fixedHeader: true,
            fixedSidebar: true,
            isMenuCollapsed: false,
            skin: '11',
            pageTransition: {
                class: 'fadeIn'
            }
        };

        $scope.pageTransitionOpts = {
            class: 'fadeIn'
        };

        $scope.color = {
            primary: '#3f51b5'
        };

        // Essential functions for sidebar functionality
        $scope.toggleMenuCollapsed = function() {
            $scope.main.isMenuCollapsed = !$scope.main.isMenuCollapsed;
        };

        $scope.setTheme = function() {
            $scope.main.skin = $scope.main.skin;
        };

        $scope.setLayout = function() {
            $scope.main.layout = $scope.main.layout;
        };

        $scope.setMenuPosition = function() {
            $scope.main.menu = $scope.main.menu;
        };

        // Watch for main changes (needed for layout functionality)
        $scope.$watch('main', function(newVal, oldVal) {
            if (newVal && oldVal) {
                if (newVal.menu === 'horizontal' && oldVal.menu === 'vertical') {
                    $rootScope.$broadcast('nav:reset');
                }
                if (newVal.fixedHeader === false && newVal.fixedSidebar === true) {
                    if (oldVal.fixedHeader === false && oldVal.fixedSidebar === false) {
                        $scope.main.fixedHeader = true;
                        $scope.main.fixedSidebar = true;
                    }
                    if (oldVal.fixedHeader === true && oldVal.fixedSidebar === true) {
                        $scope.main.fixedHeader = false;
                        $scope.main.fixedSidebar = false;
                    }
                }
            }
        }, true);

        $scope.CurrentModule = 'Truck'; 
        $scope.host = host;
    }

})();
