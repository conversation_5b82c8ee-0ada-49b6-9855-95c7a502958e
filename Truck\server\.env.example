# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name

# Server Configuration
PORT=3001
NODE_ENV=development

# Session Configuration
SESSION_SECRET=your_super_secret_session_key_here_change_this_in_production

# API Configuration
API_PREFIX=/api/v1

# Security
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:4200,http://localhost:8080
