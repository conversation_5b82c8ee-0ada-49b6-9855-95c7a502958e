const express = require('express');
const router = express.Router();
const TruckBooking = require('../models/TruckBooking');
const { authenticateUser, checkPermission } = require('../middleware/auth');
const { validate, validateParams } = require('../middleware/validation');

// Get parking locations
router.get('/parking-locations', authenticateUser, async (req, res) => {
    try {
        const result = await TruckBooking.getParkingLocations();
        
        if (result.success) {
            res.json({
                Success: true,
                Result: result.data
            });
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error getting parking locations:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Get carriers
router.get('/carriers', authenticateUser, async (req, res) => {
    try {
        const result = await TruckBooking.getCarriers();
        
        if (result.success) {
            res.json({
                Success: true,
                Result: result.data
            });
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error getting carriers:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Get truck types
router.get('/truck-types', authenticateUser, async (req, res) => {
    try {
        const result = await TruckBooking.getTruckTypes();
        
        if (result.success) {
            res.json({
                Success: true,
                Result: result.data
            });
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error getting truck types:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Get facilities
router.get('/facilities', authenticateUser, async (req, res) => {
    try {
        const result = await TruckBooking.getFacilities();
        
        if (result.success) {
            res.json({
                Success: true,
                Result: result.data
            });
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error getting facilities:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Save truck booking
router.post('/save', authenticateUser, validate('truckBooking'), async (req, res) => {
    try {
        const result = await TruckBooking.saveTruckBooking(req.body, req.user.id || req.user.UserID);
        
        if (result.success) {
            res.json({
                Success: true,
                Result: {
                    message: 'Truck booking saved successfully',
                    TruckID: result.insertId
                }
            });
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error saving truck booking:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Get truck booking by ID
router.get('/:id', authenticateUser, validateParams('id'), async (req, res) => {
    try {
        const result = await TruckBooking.getTruckDetails(req.params.id);
        
        if (result.success) {
            if (result.data) {
                res.json({
                    Success: true,
                    Result: result.data
                });
            } else {
                res.status(404).json({
                    Success: false,
                    Result: 'Truck booking not found'
                });
            }
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error getting truck booking:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Get truck list with pagination and filters
router.post('/list', authenticateUser, async (req, res) => {
    try {
        const { filters = {}, pagination = {} } = req.body;
        
        // Get total count for pagination
        const countResult = await TruckBooking.getTruckCount(filters);
        const totalRecords = countResult.success ? countResult.data.total : 0;
        
        // Get truck list
        const result = await TruckBooking.getTruckList(filters, pagination);
        
        if (result.success) {
            res.json({
                Success: true,
                Result: {
                    data: result.data,
                    totalRecords: totalRecords,
                    currentPage: pagination.page || 1,
                    pageSize: pagination.limit || 10
                }
            });
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error getting truck list:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Update truck booking
router.put('/:id', authenticateUser, validateParams('id'), validate('updateTruckBooking'), async (req, res) => {
    try {
        const result = await TruckBooking.updateTruckBooking(req.params.id, req.body, req.user.id || req.user.UserID);
        
        if (result.success) {
            if (result.affectedRows > 0) {
                res.json({
                    Success: true,
                    Result: 'Truck booking updated successfully'
                });
            } else {
                res.status(404).json({
                    Success: false,
                    Result: 'Truck booking not found'
                });
            }
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error updating truck booking:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Delete truck booking
router.delete('/:id', authenticateUser, validateParams('id'), async (req, res) => {
    try {
        const result = await TruckBooking.deleteTruckBooking(req.params.id);
        
        if (result.success) {
            if (result.affectedRows > 0) {
                res.json({
                    Success: true,
                    Result: 'Truck booking deleted successfully'
                });
            } else {
                res.status(404).json({
                    Success: false,
                    Result: 'Truck booking not found'
                });
            }
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error deleting truck booking:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

// Change truck booking status
router.patch('/:id/status', authenticateUser, validateParams('id'), validate('statusChange'), async (req, res) => {
    try {
        const { Status } = req.body;
        const result = await TruckBooking.changeStatus(req.params.id, Status, req.user.id || req.user.UserID);
        
        if (result.success) {
            if (result.affectedRows > 0) {
                res.json({
                    Success: true,
                    Result: 'Truck booking status updated successfully'
                });
            } else {
                res.status(404).json({
                    Success: false,
                    Result: 'Truck booking not found'
                });
            }
        } else {
            res.status(500).json({
                Success: false,
                Result: result.error
            });
        }
    } catch (error) {
        console.error('Error changing truck booking status:', error);
        res.status(500).json({
            Success: false,
            Result: 'Server error occurred'
        });
    }
});

module.exports = router;
