# Testing Guide - Truck Management Module

## Pre-Testing Setup

### 1. Verify Build
```bash
cd Truck
ng build
```
Ensure no errors and dist/ folder is created.

### 2. Check File Structure
Verify these files exist:
- ✅ `index.php`
- ✅ `dist/browser/index.html`
- ✅ `dist/browser/main-*.js`
- ✅ `dist/browser/polyfills-*.js`
- ✅ `dist/browser/styles-*.css`
- ✅ `database/truck.class.php`
- ✅ `database/create_trucks_table.sql`
- ✅ `includes/truck_submit.php`

## Testing Checklist

### ✅ 1. Basic Loading Test
- [ ] Navigate to `/aws/Truck/`
- [ ] Page loads without errors
- [ ] Angular Material toolbar appears
- [ ] Form components render correctly
- [ ] No console errors in browser

### ✅ 2. Session Integration Test
- [ ] User must be logged in to access
- [ ] Redirects to login if not authenticated
- [ ] Session data available in browser console: `window.sessionData`
- [ ] User information displays correctly

### ✅ 3. Form Validation Test
- [ ] Required field validation works
- [ ] Form cannot be submitted when invalid
- [ ] Error messages display correctly
- [ ] All form fields accept input
- [ ] Dropdown selections work

### ✅ 4. Database Integration Test
- [ ] Database table created successfully
- [ ] PHP connection works
- [ ] API endpoints respond correctly
- [ ] Form submission saves to database
- [ ] Success/error messages display

### ✅ 5. Responsive Design Test
- [ ] Form works on desktop
- [ ] Form works on tablet
- [ ] Form works on mobile
- [ ] Layout adjusts properly
- [ ] All buttons accessible

### ✅ 6. Browser Compatibility Test
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### ✅ 7. Integration with Existing App Test
- [ ] Sidebar navigation works
- [ ] Can navigate between modules
- [ ] Session persists across modules
- [ ] No conflicts with existing AngularJS
- [ ] Styling consistent with main app

## API Testing

### Test Save Truck
```bash
curl -X POST "http://yourserver/aws/Truck/includes/truck_submit.php?ajax=SaveTruck" \
-H "Content-Type: application/json" \
-d '{
  "truckNumber": "TEST001",
  "driverName": "Test Driver",
  "licensePlate": "TEST123",
  "truckType": "delivery",
  "status": "active",
  "notes": "Test truck"
}'
```

### Test Get All Trucks
```bash
curl "http://yourserver/aws/Truck/includes/truck_submit.php?ajax=GetAllTrucks"
```

## Performance Testing

### 1. Load Time Test
- [ ] Initial page load < 3 seconds
- [ ] Form submission < 2 seconds
- [ ] API responses < 1 second

### 2. Bundle Size Check
```bash
ng build --stats-json
npx webpack-bundle-analyzer dist/stats.json
```
- [ ] Main bundle < 500KB
- [ ] Total bundle < 1MB

## Security Testing

### 1. Authentication Test
- [ ] Unauthenticated users redirected
- [ ] Session validation works
- [ ] No sensitive data in client-side code

### 2. Input Validation Test
- [ ] SQL injection protection
- [ ] XSS protection
- [ ] CSRF protection (if applicable)

## Error Handling Test

### 1. Network Errors
- [ ] Graceful handling of network failures
- [ ] User-friendly error messages
- [ ] Retry mechanisms work

### 2. Server Errors
- [ ] PHP errors handled gracefully
- [ ] Database connection failures handled
- [ ] Invalid data responses handled

## Deployment Testing

### 1. Production Environment
- [ ] All files uploaded correctly
- [ ] File permissions correct
- [ ] Database connection works
- [ ] SSL certificate valid (if HTTPS)

### 2. Rollback Test
- [ ] Can revert to previous version
- [ ] No data loss during rollback
- [ ] Existing modules unaffected

## User Acceptance Testing

### 1. Functionality Test
- [ ] Users can create trucks
- [ ] Form validation is clear
- [ ] Success feedback is obvious
- [ ] Error messages are helpful

### 2. Usability Test
- [ ] Interface is intuitive
- [ ] Navigation is clear
- [ ] Form is easy to complete
- [ ] Mobile experience is good

## Common Issues and Solutions

### Issue: Angular app doesn't load
**Solution**: Check browser console for errors, verify file paths in index.php

### Issue: Form submission fails
**Solution**: Check PHP error logs, verify database connection, test API endpoints

### Issue: Session not working
**Solution**: Verify session_start() is called, check PHP session configuration

### Issue: Styling looks wrong
**Solution**: Check if CSS files are loaded, verify Angular Material is working

### Issue: Mobile layout broken
**Solution**: Test responsive breakpoints, check viewport meta tag

## Success Criteria

The module is ready for production when:
- ✅ All tests pass
- ✅ No console errors
- ✅ Performance meets requirements
- ✅ Security tests pass
- ✅ User acceptance criteria met
- ✅ Documentation complete
- ✅ Deployment process verified

## Post-Deployment Monitoring

### 1. Monitor for 24 hours
- [ ] Check server logs for errors
- [ ] Monitor database performance
- [ ] Watch for user feedback

### 2. Weekly Review
- [ ] Review usage statistics
- [ ] Check for any issues
- [ ] Plan future enhancements
