<div class="page">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">
                <!--Truck Booking Start-->
                <mat-card class="no-margin-h">

                    <mat-toolbar class="md-table-toolbar md-default">
                        <mat-toolbar-row>
                            <mat-icon class="md-primary">event</mat-icon>
                            <span>Truck Booking</span>
                        </mat-toolbar-row>
                    </mat-toolbar>

                    <!-- Loading State -->
                    <div class="row" *ngIf="isLoading">
                        <div class="col-md-12" style="text-align: center; padding: 40px;">
                            <mat-spinner diameter="50"></mat-spinner>
                            <p>Loading form data...</p>
                        </div>
                    </div>

                    <!-- Form -->
                    <mat-card-content *ngIf="!isLoading">
                        <form [formGroup]="truckBookingForm" (ngSubmit)="onSave()" name="truckBookingForm" class="form-validation">
                            <div class="row">

                                <!-- Facility -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Facility</mat-label>
                                        <mat-select formControlName="FacilityID" required [disabled]="true">
                                            <mat-option *ngFor="let facility of facilities" [value]="facility.FacilityID">
                                                {{facility.FacilityName}}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('FacilityID')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Arrival Type -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Arrival Type</mat-label>
                                        <mat-select formControlName="ArrivalType" required>
                                            <mat-option value="Inbound">Inbound</mat-option>
                                            <mat-option value="Outbound">Outbound</mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('ArrivalType')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Parking Location -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Parking Location</mat-label>
                                        <mat-select formControlName="ParkingLocationID" required>
                                            <mat-option *ngFor="let location of parkingLocations" [value]="location.ParkingLocationID">
                                                {{location.ParkingLocationName}}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('ParkingLocationID')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Carrier -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Carrier</mat-label>
                                        <mat-select formControlName="CarrierID" required (selectionChange)="onCarrierChange()">
                                            <mat-option *ngFor="let carrier of carriers" [value]="carrier.CarrierID">
                                                {{carrier.CarrierName}}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('CarrierID')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Arrival Date -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Arrival Date</mat-label>
                                        <input matInput [matDatepicker]="picker" formControlName="ArrivalDate" required>
                                        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                                        <mat-datepicker #picker></mat-datepicker>
                                        <mat-error *ngIf="truckBookingForm.get('ArrivalDate')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Arrival Time -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Arrival Time</mat-label>
                                        <input matInput type="time" formControlName="ArrivalTime" required />
                                        <mat-error *ngIf="truckBookingForm.get('ArrivalTime')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Load Type -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Load Type</mat-label>
                                        <mat-select formControlName="LoadType" required>
                                            <mat-option value="Pallet">Pallet</mat-option>
                                            <mat-option value="Rack">Rack</mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('LoadType')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Load Number -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Load Number</mat-label>
                                        <input matInput formControlName="LoadNumber" required maxlength="50" />
                                        <mat-error *ngIf="truckBookingForm.get('LoadNumber')?.hasError('required')">This is required.</mat-error>
                                        <mat-error *ngIf="truckBookingForm.get('LoadNumber')?.hasError('maxlength')">Max length 50.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Truck Type -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Truck Type</mat-label>
                                        <mat-select formControlName="TruckTypeID" required>
                                            <mat-option *ngFor="let truckType of truckTypes" [value]="truckType.TruckTypeID">
                                                {{truckType.TruckTypeName}}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('TruckTypeID')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Truck Registration -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Truck Registration</mat-label>
                                        <input matInput formControlName="TruckReg" required maxlength="50" />
                                        <mat-error *ngIf="truckBookingForm.get('TruckReg')?.hasError('required')">This is required.</mat-error>
                                        <mat-error *ngIf="truckBookingForm.get('TruckReg')?.hasError('maxlength')">Max length 50.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Trailer Number -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Trailer Number</mat-label>
                                        <input matInput formControlName="TrailerNumber" required maxlength="50" />
                                        <mat-error *ngIf="truckBookingForm.get('TrailerNumber')?.hasError('required')">This is required.</mat-error>
                                        <mat-error *ngIf="truckBookingForm.get('TrailerNumber')?.hasError('maxlength')">Max length 50.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Driver Name -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Driver Name</mat-label>
                                        <input matInput formControlName="DriverName" required maxlength="50" />
                                        <mat-error *ngIf="truckBookingForm.get('DriverName')?.hasError('required')">This is required.</mat-error>
                                        <mat-error *ngIf="truckBookingForm.get('DriverName')?.hasError('maxlength')">Max length 50.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Driver ID -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Driver ID</mat-label>
                                        <input matInput formControlName="DriverID" maxlength="50" />
                                        <mat-error *ngIf="truckBookingForm.get('DriverID')?.hasError('maxlength')">Max length 50.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Shipment Ticket ID -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Shipment Ticket ID</mat-label>
                                        <input matInput formControlName="ShipmentTicketID" maxlength="50" />
                                        <mat-error *ngIf="truckBookingForm.get('ShipmentTicketID')?.hasError('maxlength')">Max length 50.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Classification Type -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Classification Type</mat-label>
                                        <mat-select formControlName="ClassificationType" required>
                                            <mat-option value="All">All</mat-option>
                                            <mat-option value="UEEE">UEEE</mat-option>
                                            <mat-option value="WEEE" [disabled]="isWeeeDisabled()">WEEE</mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('ClassificationType')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Waste Collection Permit -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Waste Collection Permit</mat-label>
                                        <input matInput formControlName="WasteCollectionPermit" maxlength="250" />
                                        <mat-error *ngIf="truckBookingForm.get('WasteCollectionPermit')?.hasError('maxlength')">Max length 250.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Notes -->
                                <div class="col-md-6">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Notes</mat-label>
                                        <textarea matInput formControlName="Notes" maxlength="250" rows="3"></textarea>
                                        <mat-error *ngIf="truckBookingForm.get('Notes')?.hasError('maxlength')">Max length 250.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Status -->
                                <div class="col-md-3">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <mat-label>Status</mat-label>
                                        <mat-select formControlName="Status" required>
                                            <mat-option value="Reserved">Reserved</mat-option>
                                            <mat-option value="Requested">Requested</mat-option>
                                        </mat-select>
                                        <mat-error *ngIf="truckBookingForm.get('Status')?.hasError('required')">This is required.</mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Buttons -->
                                <div class="col-md-12 btns-row">
                                    <button mat-raised-button type="button" (click)="onReset()">Cancel</button>

                                    <button 
                                        mat-raised-button
                                        color="primary"
                                        type="submit"
                                        [disabled]="truckBookingForm.invalid || isSaving">
                                        <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
                                        <span *ngIf="!isSaving">Save</span>
                                        <span *ngIf="isSaving">Saving...</span>
                                    </button>
                                </div>

                            </div>
                        </form>
                    </mat-card-content>

                </mat-card>
                <!--Truck Booking Close-->

                <!-- Display submitted data -->
                <mat-card class="no-margin-h" *ngIf="submittedData">
                    <mat-toolbar class="md-table-toolbar md-default">
                        <mat-toolbar-row>                            
                            <mat-icon class="md-primary">check_circle</mat-icon>
                            <span>Submitted Data</span>
                        </mat-toolbar-row>
                    </mat-toolbar>
                    <mat-card-content>
                        <pre>{{ submittedData | json }}</pre>
                    </mat-card-content>
                </mat-card>

            </article>
        </div>
    </div>
</div>
