@use "../variables" as *;

@media (max-width: 1279px) {
  .detail-part.movetodetail {
    display: block;
    position: absolute;
    z-index: 9;
    left: 0;
    background: var(--mat-card-elevated-container-color);
  }
}

@media (max-width: 1279px) {
  .welcome-app {
    display: none;
  }
}

@media (max-width: 959px) {
  .contact-detail-part {
    display: none;
  }

  .contact-detail-part.activeContact {
    position: absolute !important;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    z-index: 999;
    background-color: var(--mat-card-elevated-container-color);
  }
}

// contact app
.uploader {
  .upload-image {
    width: 100px;
    height: auto;
    cursor: pointer;
  }

  input[type="file"] {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
}

.contact-listing {
  .mdc-list-item__primary-text {
    margin-bottom: -11px !important;
    margin-top: 10px !important;
  }
}
