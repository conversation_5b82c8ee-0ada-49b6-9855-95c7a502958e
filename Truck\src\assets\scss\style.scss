@use "sass:map";
@use "@angular/material" as mat;

@use "themecolors/blue_theme";
@use "themecolors/orange_theme";
@use "themecolors/aqua_theme";
@use "themecolors/cyan_theme";
@use "themecolors/green_theme";
@use "themecolors/purple_theme";

@use "variables" as *;
@use "override-component";
@use "theme-variables/default-variables";
@use "theme-variables/light-theme-variables";
@use "theme-variables/dark-theme-variables";

//container layout
@use "layouts/transitions";
@use "helpers/color";
@use "helpers/border-color";
@use "helpers/icon-size";
@use "container";
@use "layouts/layouts";
@use "grid/grid";
@use "helpers/custom-flex";
@use "helpers";

// horizontal
@use "horizontal/horizontal";
@use "dark/dark";

// apps
@use "apps/calendar";
@use "apps/email";
@use "apps/blogs";
@use "apps/chat";
@use "apps/contact-list";
@use "apps/kanban";
@use "apps/courses";
@use "apps/todo";
@use "apps/ecommerce";

@use "pages/auth";
@use "pages/dashboards";
@use "pages/landingpage";
@use "pages/toast";
@use "pages/pricing";
@use "pages/frontend";

// RTL Theme
@use "rtl/rtl";