const { executeQuery, getOne, insert, modify } = require('../config/database');

class TruckBooking {
    
    // Get all parking locations
    static async getParkingLocations() {
        const query = "SELECT * FROM ParkingLocation WHERE Status = '1' ORDER BY ParkingLocationName";
        return await executeQuery(query);
    }

    // Get all carriers
    static async getCarriers() {
        const query = "SELECT * FROM Carrier WHERE StatusID = '1' ORDER BY CarrierName";
        return await executeQuery(query);
    }

    // Get all truck types
    static async getTruckTypes() {
        const query = "SELECT * FROM TruckType WHERE Status = '1' ORDER BY TruckTypeName";
        return await executeQuery(query);
    }

    // Get all facilities (assuming this table exists)
    static async getFacilities() {
        const query = "SELECT * FROM facility WHERE Status = '1' ORDER BY FacilityName";
        return await executeQuery(query);
    }

    // Save truck booking
    static async saveTruckBooking(data, userId) {
        const query = `
            INSERT INTO Truck (
                FacilityID, ArrivalType, ParkingLocationID, CarrierID, 
                ArrivalDate, ArrivalTime, LoadType, LoadNumber, 
                TruckTypeID, TruckReg, TrailerNumber, DriverName, 
                DriverID, ShipmentTicketID, ClassificationType, 
                WasteCollectionPermit, Notes, Status, 
                CreatedDate, CreatedBy
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)
        `;
        
        const params = [
            data.FacilityID,
            data.ArrivalType,
            data.ParkingLocationID,
            data.CarrierID,
            data.ArrivalDate,
            data.ArrivalTime,
            data.LoadType,
            data.LoadNumber,
            data.TruckTypeID,
            data.TruckReg,
            data.TrailerNumber,
            data.DriverName,
            data.DriverID || null,
            data.ShipmentTicketID || null,
            data.ClassificationType,
            data.WasteCollectionPermit || null,
            data.Notes || null,
            data.Status,
            userId
        ];
        
        return await insert(query, params);
    }

    // Get truck booking by ID
    static async getTruckById(truckId) {
        const query = "SELECT * FROM Truck WHERE TruckID = ?";
        return await getOne(query, [truckId]);
    }

    // Get truck booking details with joins
    static async getTruckDetails(truckId) {
        const query = `
            SELECT T.*, 
                   PL.ParkingLocationName,
                   F.FacilityName,
                   C.CarrierName,
                   TT.TruckTypeName
            FROM Truck T 
            LEFT JOIN ParkingLocation PL ON T.ParkingLocationID = PL.ParkingLocationID 
            LEFT JOIN facility F ON T.FacilityID = F.FacilityID 
            LEFT JOIN Carrier C ON T.CarrierID = C.CarrierID
            LEFT JOIN TruckType TT ON T.TruckTypeID = TT.TruckTypeID
            WHERE T.TruckID = ?
        `;
        return await getOne(query, [truckId]);
    }

    // Get truck list with pagination and filters
    static async getTruckList(filters = {}, pagination = {}) {
        let query = `
            SELECT T.*, 
                   PL.ParkingLocationName,
                   F.FacilityName,
                   C.CarrierName,
                   TT.TruckTypeName
            FROM Truck T 
            LEFT JOIN ParkingLocation PL ON T.ParkingLocationID = PL.ParkingLocationID 
            LEFT JOIN facility F ON T.FacilityID = F.FacilityID 
            LEFT JOIN Carrier C ON T.CarrierID = C.CarrierID
            LEFT JOIN TruckType TT ON T.TruckTypeID = TT.TruckTypeID
            WHERE 1=1
        `;
        
        const params = [];
        
        // Apply filters
        if (filters.FacilityName) {
            query += " AND F.FacilityName LIKE ?";
            params.push(`%${filters.FacilityName}%`);
        }
        
        if (filters.CarrierName) {
            query += " AND C.CarrierName LIKE ?";
            params.push(`%${filters.CarrierName}%`);
        }
        
        if (filters.Status) {
            query += " AND T.Status = ?";
            params.push(filters.Status);
        }
        
        if (filters.ArrivalDate) {
            query += " AND DATE(T.ArrivalDate) = ?";
            params.push(filters.ArrivalDate);
        }
        
        if (filters.TruckReg) {
            query += " AND T.TruckReg LIKE ?";
            params.push(`%${filters.TruckReg}%`);
        }
        
        if (filters.DriverName) {
            query += " AND T.DriverName LIKE ?";
            params.push(`%${filters.DriverName}%`);
        }
        
        // Add ordering
        query += " ORDER BY T.CreatedDate DESC";
        
        // Add pagination
        if (pagination.limit) {
            query += " LIMIT ?";
            params.push(parseInt(pagination.limit));
            
            if (pagination.offset) {
                query += " OFFSET ?";
                params.push(parseInt(pagination.offset));
            }
        }
        
        return await executeQuery(query, params);
    }

    // Get total count for pagination
    static async getTruckCount(filters = {}) {
        let query = `
            SELECT COUNT(*) as total
            FROM Truck T 
            LEFT JOIN ParkingLocation PL ON T.ParkingLocationID = PL.ParkingLocationID 
            LEFT JOIN facility F ON T.FacilityID = F.FacilityID 
            LEFT JOIN Carrier C ON T.CarrierID = C.CarrierID
            WHERE 1=1
        `;
        
        const params = [];
        
        // Apply same filters as getTruckList
        if (filters.FacilityName) {
            query += " AND F.FacilityName LIKE ?";
            params.push(`%${filters.FacilityName}%`);
        }
        
        if (filters.CarrierName) {
            query += " AND C.CarrierName LIKE ?";
            params.push(`%${filters.CarrierName}%`);
        }
        
        if (filters.Status) {
            query += " AND T.Status = ?";
            params.push(filters.Status);
        }
        
        if (filters.ArrivalDate) {
            query += " AND DATE(T.ArrivalDate) = ?";
            params.push(filters.ArrivalDate);
        }
        
        if (filters.TruckReg) {
            query += " AND T.TruckReg LIKE ?";
            params.push(`%${filters.TruckReg}%`);
        }
        
        if (filters.DriverName) {
            query += " AND T.DriverName LIKE ?";
            params.push(`%${filters.DriverName}%`);
        }
        
        return await getOne(query, params);
    }

    // Update truck booking
    static async updateTruckBooking(truckId, data, userId) {
        const query = `
            UPDATE Truck SET 
                FacilityID = ?, ArrivalType = ?, ParkingLocationID = ?, CarrierID = ?,
                ArrivalDate = ?, ArrivalTime = ?, LoadType = ?, LoadNumber = ?,
                TruckTypeID = ?, TruckReg = ?, TrailerNumber = ?, DriverName = ?,
                DriverID = ?, ShipmentTicketID = ?, ClassificationType = ?,
                WasteCollectionPermit = ?, Notes = ?, Status = ?,
                UpdatedDate = NOW(), UpdatedBy = ?
            WHERE TruckID = ?
        `;
        
        const params = [
            data.FacilityID, data.ArrivalType, data.ParkingLocationID, data.CarrierID,
            data.ArrivalDate, data.ArrivalTime, data.LoadType, data.LoadNumber,
            data.TruckTypeID, data.TruckReg, data.TrailerNumber, data.DriverName,
            data.DriverID || null, data.ShipmentTicketID || null, data.ClassificationType,
            data.WasteCollectionPermit || null, data.Notes || null, data.Status,
            userId, truckId
        ];
        
        return await modify(query, params);
    }

    // Delete truck booking
    static async deleteTruckBooking(truckId) {
        const query = "DELETE FROM Truck WHERE TruckID = ?";
        return await modify(query, [truckId]);
    }

    // Change truck booking status
    static async changeStatus(truckId, status, userId) {
        const query = "UPDATE Truck SET Status = ?, UpdatedDate = NOW(), UpdatedBy = ? WHERE TruckID = ?";
        return await modify(query, [status, userId, truckId]);
    }
}

module.exports = TruckBooking;
