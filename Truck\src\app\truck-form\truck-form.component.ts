import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { MaterialModule } from '../material.module';

@Component({
  selector: 'app-truck-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MaterialModule
  ],
  templateUrl: './truck-form.component.html',
  styleUrl: './truck-form.component.scss'
})
export class TruckFormComponent implements OnInit {
  truckForm!: FormGroup;
  submittedData: any = null;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    console.log('Truck form component initialized');
  }

  private initializeForm(): void {
    this.truckForm = this.fb.group({
      truckNumber: ['', [Validators.required, Validators.maxLength(50)]],
      driverName: ['', [Validators.required, Validators.maxLength(100)]],
      licensePlate: ['', [Validators.required, Validators.maxLength(20)]],
      truckType: ['', Validators.required],
      status: ['active', Validators.required],
      notes: ['', Validators.maxLength(500)]
    });
  }

  onSubmit(): void {
    if (this.truckForm.valid) {
      this.isLoading = true;
      const formData = this.truckForm.value;

      const params = new URLSearchParams();
      params.append('ajax', 'SaveTruck');
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '') {
          params.append(key, formData[key]);
        }
      });

      this.http.post<any>('/Truck/includes/truck_submit.php', params.toString(), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      }).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.Success) {
            this.submittedData = formData;
            alert('Truck saved successfully!');
            this.onReset();
          } else {
            alert('Error: ' + response.Result);
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error saving truck:', error);
          alert('Error saving truck. Please try again.');
        }
      });
    } else {
      this.markFormGroupTouched();
      alert('Please fill in all required fields.');
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.truckForm.controls).forEach(key => {
      const control = this.truckForm.get(key);
      control?.markAsTouched();
    });
  }

  onReset(): void {
    this.truckForm.reset();
    this.truckForm.patchValue({ status: 'active' });
    this.submittedData = null;
  }
}
